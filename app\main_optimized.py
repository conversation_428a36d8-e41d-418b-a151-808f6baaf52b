"""
优化的AI占卜项目主应用文件

使用优化后的配置管理、错误处理、中间件等组件
提供完整的API服务和监控功能
"""

import asyncio
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any
import logging
from pathlib import Path

from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.staticfiles import StaticFiles
from prometheus_client import make_asgi_app
import uvicorn

# 导入优化后的模块
from app.core.config_optimized import settings
from app.core.error_handler_optimized import (
    error_handler, ErrorContext, BaseAPIError, ErrorSeverity
)
from app.core.cache_manager import CacheManager
from app.services.ai_service_optimized import create_ai_service, AIService
from app.core.middleware import (
    SecurityHeadersMiddleware,
    RequestLoggingMiddleware, 
    PerformanceMiddleware,
    RateLimitMiddleware
)

# 导入路由
from app.routers import auth, model, fortune, community, admin
from app.routers.miniprogram import router as miniprogram_router

logger = logging.getLogger(__name__)

class AppState:
    """应用状态管理"""
    
    def __init__(self):
        self.ai_service: AIService = None
        self.cache_manager: CacheManager = None
        self.startup_time: float = None
        self.is_ready: bool = False

# 全局应用状态
app_state = AppState()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("开始初始化AI占卜系统...")
    
    try:
        # 初始化缓存管理器
        app_state.cache_manager = CacheManager(
            redis_url=settings.get_redis_uri(),
            default_ttl=settings.cache.default_ttl
        )
        await app_state.cache_manager.initialize()
        logger.info("缓存管理器初始化完成")
        
        # 初始化AI服务
        app_state.ai_service = await create_ai_service(
            cache_manager=app_state.cache_manager,
            default_model="chatglm-6b"
        )
        logger.info("AI服务初始化完成")
        
        # 设置错误通知处理器
        async def error_notification_handler(error: BaseAPIError):
            """错误通知处理器"""
            if error.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                # 这里可以集成邮件、短信、钉钉等通知
                logger.critical(f"严重错误通知: {error.error_id} - {error.message}")
        
        error_handler.add_notification_handler(error_notification_handler)
        
        app_state.is_ready = True
        logger.info("系统初始化完成，服务已就绪")
        
        yield
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        raise
    
    finally:
        # 关闭时的清理
        logger.info("开始关闭系统...")
        
        try:
            if app_state.ai_service:
                await app_state.ai_service.shutdown()
                logger.info("AI服务已关闭")
            
            if app_state.cache_manager:
                await app_state.cache_manager.close()
                logger.info("缓存管理器已关闭")
                
        except Exception as e:
            logger.error(f"系统关闭时出现错误: {e}")
        
        logger.info("系统已完全关闭")

# 创建FastAPI应用
app = FastAPI(
    title=settings.project_name,
    description=f"""
    {settings.description}
    
    ## 🎯 功能特点
    
    ### 🤖 AI模型管理
    * 多模型支持和动态切换
    * 智能缓存和性能优化
    * 实时监控和告警
    * 自动故障恢复
    
    ### 🔮 占卜预测服务
    * 📅 **八字分析** - 基于生辰八字的命理分析
    * 🎴 **易经卦象** - 传统周易卦象解读
    * 🏠 **风水分析** - 家居和办公环境风水
    * ⚡ **五行分析** - 五行相生相克关系
    * 🌟 **运势预测** - 日运、月运、年运分析
    
    ### 👥 用户系统
    * 🔐 **安全认证** - JWT令牌管理和权限控制
    * 📱 **多端登录** - 支持微信小程序等多种登录方式
    * 👤 **用户画像** - 个性化推荐和分析
    * 📊 **使用统计** - 详细的使用数据分析
    
    ### 🌐 社区功能
    * 📝 **内容发布** - 支持图文、视频等多媒体内容
    * 💬 **互动交流** - 评论、点赞、分享功能
    * 🏷️ **内容分类** - 智能标签和分类管理
    * 🔍 **搜索发现** - 强大的内容搜索和推荐
    
    ### 🛡️ 系统特性
    * 🔒 **安全防护** - 多层安全防护和数据加密
    * 📈 **性能监控** - 实时性能指标和告警
    * 💾 **智能缓存** - 多级缓存提升响应速度
    * 🗃️ **知识库** - 丰富的占卜知识和案例库
    * 🔄 **自动恢复** - 故障自动检测和恢复
    
    ## 📖 API使用指南
    
    ### 认证方式
    大部分API需要认证，请在请求头中添加：
    ```
    Authorization: Bearer <your_token>
    ```
    
    ### 获取Token
    * **用户登录**: `POST /api/auth/login`
    * **微信登录**: `POST /api/auth/wx-login`
    * **刷新Token**: `POST /api/auth/refresh`
    
    ### 速率限制
    * 每个IP每分钟最多 {settings.security.rate_limit_per_minute} 次请求
    * 每个用户每天最多 {settings.security.rate_limit_per_day} 次AI预测
    
    ### 缓存策略
    * 预测结果缓存 {settings.cache.qa_cache_ttl // 60} 分钟
    * 知识库数据缓存 {settings.cache.knowledge_cache_ttl // 3600} 小时
    * 用户信息缓存 {settings.cache.user_cache_ttl // 60} 分钟
    
    ## 🔗 相关链接
    
    * [API文档](/docs)
    * [ReDoc文档](/redoc)
    * [系统监控](/metrics)
    * [健康检查](/health)
    
    ---
    
    💡 **提示**: 本系统采用模块化架构，支持高并发和高可用部署。
    如有问题，请查看错误响应中的 `error_id` 并联系技术支持。
    """,
    version=settings.version,
    docs_url=None,  # 我们将自定义文档页面
    redoc_url=None,
    openapi_url=f"{settings.api_v1_prefix}/openapi.json",
    lifespan=lifespan,
    openapi_tags=[
        {
            "name": "Authentication",
            "description": "🔐 用户认证相关接口，包括注册、登录、token管理等",
        },
        {
            "name": "Model Management", 
            "description": "🤖 AI模型管理相关接口，包括模型加载、切换、监控等",
        },
        {
            "name": "Fortune Telling",
            "description": "🔮 占卜预测相关接口，包括八字、易经、风水、五行等分析",
        },
        {
            "name": "Community",
            "description": "🌐 社区相关接口，包括发帖、评论、用户资料等",
        },
        {
            "name": "Admin",
            "description": "⚙️ 管理员功能，包括用户管理、系统配置、数据统计等",
        },
        {
            "name": "小程序专用API",
            "description": "📱 微信小程序专用接口，包括智能聊天、占卜分析等",
        },
        {
            "name": "Health",
            "description": "📊 系统健康检查和监控接口",
        }
    ]
)

# 添加中间件（按执行顺序）
if not settings.is_development():
    # 生产环境才启用受信任主机检查
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 在实际部署中应该限制为具体域名
    )

# 安全响应头中间件
app.add_middleware(
    SecurityHeadersMiddleware,
    headers=settings.security.security_headers
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.security.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.security.allowed_methods,
    allow_headers=settings.security.allowed_headers,
)

# Gzip压缩中间件
app.add_middleware(
    GZipMiddleware,
    minimum_size=1000
)

# 性能监控中间件
app.add_middleware(PerformanceMiddleware)

# 请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# 速率限制中间件
app.add_middleware(
    RateLimitMiddleware,
    calls=settings.security.rate_limit_per_minute,
    period=60
)

# 挂载Prometheus监控
if settings.monitoring.enable_metrics:
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)

# 挂载静态文件
if Path("app/static").exists():
    app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 全局异常处理器
@app.exception_handler(BaseAPIError)
async def api_error_handler(request: Request, error: BaseAPIError):
    """处理自定义API异常"""
    context = ErrorContext(
        request_id=getattr(request.state, 'request_id', 'unknown'),
        endpoint=str(request.url.path),
        method=request.method,
        user_agent=request.headers.get('user-agent'),
        ip_address=request.client.host if request.client else None
    )
    
    error_response = await error_handler.handle_error(error, context)
    return JSONResponse(
        status_code=error.code,
        content=error_response,
        headers={"X-Error-ID": error.error_id}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, error: Exception):
    """处理所有其他异常"""
    context = ErrorContext(
        request_id=getattr(request.state, 'request_id', 'unknown'),
        endpoint=str(request.url.path),
        method=request.method,
        user_agent=request.headers.get('user-agent'),
        ip_address=request.client.host if request.client else None
    )
    
    error_response = await error_handler.handle_error(error, context)
    return JSONResponse(
        status_code=500,
        content=error_response
    )

# 依赖注入
async def get_ai_service() -> AIService:
    """获取AI服务实例"""
    if not app_state.ai_service:
        raise HTTPException(status_code=503, detail="AI服务未初始化")
    return app_state.ai_service

async def get_cache_manager() -> CacheManager:
    """获取缓存管理器实例"""
    if not app_state.cache_manager:
        raise HTTPException(status_code=503, detail="缓存服务未初始化")
    return app_state.cache_manager

# 自定义文档页面
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义Swagger UI"""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - 交互式API文档",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )

# 基础健康检查
@app.get(
    "/health",
    tags=["Health"],
    summary="🏥 系统健康检查",
    description="""
    检查系统的整体健康状态，包括：
    
    * 🤖 AI模型加载状态
    * 💾 缓存服务状态  
    * 📊 性能指标
    * 🔧 依赖服务状态
    * 💿 系统资源使用情况
    
    返回详细的健康状态报告，用于监控和告警。
    """,
    response_description="系统健康状态报告",
    responses={
        200: {
            "description": "系统健康",
            "content": {
                "application/json": {
                    "example": {
                        "status": "healthy",
                        "timestamp": "2024-01-01T12:00:00Z",
                        "uptime": 3600.5,
                        "version": "1.0.0",
                        "environment": "production",
                        "services": {
                            "ai_service": {"status": "healthy", "model": "chatglm-6b"},
                            "cache": {"status": "healthy", "hit_rate": 0.85},
                            "database": {"status": "healthy", "connections": 10}
                        },
                        "performance": {
                            "avg_response_time": 0.15,
                            "requests_per_second": 120.5,
                            "error_rate": 0.001
                        },
                        "resources": {
                            "cpu_usage": 0.35,
                            "memory_usage": 0.60,
                            "disk_usage": 0.45
                        }
                    }
                }
            }
        },
        503: {
            "description": "服务不健康",
            "content": {
                "application/json": {
                    "example": {
                        "status": "unhealthy",
                        "error": "AI模型加载失败",
                        "timestamp": "2024-01-01T12:00:00Z"
                    }
                }
            }
        }
    }
)
async def health_check() -> Dict[str, Any]:
    """系统健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": settings.monitoring.health_check_interval,
            "version": settings.version,
            "environment": settings.environment.value,
            "uptime": asyncio.get_event_loop().time(),
            "services": {},
            "performance": {},
            "resources": {}
        }
        
        # 检查AI服务
        if app_state.ai_service:
            ai_health = await app_state.ai_service.health_check()
            health_status["services"]["ai_service"] = ai_health
            
            if ai_health["status"] != "healthy":
                health_status["status"] = "degraded"
        else:
            health_status["services"]["ai_service"] = {"status": "not_initialized"}
            health_status["status"] = "unhealthy"
        
        # 检查缓存服务
        if app_state.cache_manager:
            try:
                await app_state.cache_manager.ping()
                health_status["services"]["cache"] = {"status": "healthy"}
            except Exception as e:
                health_status["services"]["cache"] = {"status": "unhealthy", "error": str(e)}
                health_status["status"] = "degraded"
        else:
            health_status["services"]["cache"] = {"status": "not_initialized"}
            health_status["status"] = "unhealthy"
        
        # 获取性能统计
        if app_state.ai_service and app_state.ai_service.monitor:
            perf_stats = await app_state.ai_service.get_performance_stats()
            health_status["performance"] = {
                "avg_latency": perf_stats.get("avg_latency", 0),
                "throughput": perf_stats.get("throughput", 0),
                "error_rate": perf_stats.get("error_rate", 0),
                "total_requests": perf_stats.get("total_requests", 0)
            }
        
        return health_status
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": asyncio.get_event_loop().time()
        }

# 系统信息接口
@app.get(
    "/info",
    tags=["Health"],
    summary="ℹ️ 系统信息",
    description="获取系统基本信息和配置摘要"
)
async def system_info() -> Dict[str, Any]:
    """获取系统信息"""
    return {
        "name": settings.project_name,
        "version": settings.version,
        "description": settings.description,
        "environment": settings.environment.value,
        "debug": settings.debug,
        "api_prefix": settings.api_v1_prefix,
        "features": {
            "monitoring": settings.monitoring.enable_metrics,
            "caching": True,
            "rate_limiting": True,
            "security_headers": True,
            "compression": True
        },
        "limits": {
            "rate_limit_per_minute": settings.security.rate_limit_per_minute,
            "rate_limit_per_day": settings.security.rate_limit_per_day,
            "max_request_size": "10MB",
            "max_response_size": "50MB"
        },
        "cache_ttl": {
            "qa_cache": f"{settings.cache.qa_cache_ttl // 60}分钟",
            "user_cache": f"{settings.cache.user_cache_ttl // 60}分钟",
            "knowledge_cache": f"{settings.cache.knowledge_cache_ttl // 3600}小时"
        }
    }

# 错误统计接口
@app.get(
    "/errors/stats",
    tags=["Health"],
    summary="📊 错误统计",
    description="获取系统错误统计信息"
)
async def error_stats() -> Dict[str, Any]:
    """获取错误统计"""
    return {
        "error_counts": error_handler.get_error_stats(),
        "timestamp": asyncio.get_event_loop().time()
    }

# 注册路由
app.include_router(auth.router)
app.include_router(fortune.router)
app.include_router(community.router)
app.include_router(model.router)
app.include_router(miniprogram_router)  # 小程序专用路由

# 根路径欢迎信息
@app.get(
    "/",
    summary="🏠 欢迎页面",
    description="API根路径，返回系统欢迎信息和导航"
)
async def root() -> Dict[str, Any]:
    """根路径欢迎信息"""
    return {
        "message": f"欢迎使用 {settings.project_name}",
        "version": settings.version,
        "description": settings.description,
        "status": "运行中" if app_state.is_ready else "初始化中",
        "docs": "/docs",
        "redoc": "/redoc", 
        "health": "/health",
        "metrics": "/metrics" if settings.monitoring.enable_metrics else None,
        "api": {
            "authentication": f"{settings.api_v1_prefix}/auth",
            "models": f"{settings.api_v1_prefix}/models",
            "fortune": f"{settings.api_v1_prefix}/fortune",
            "community": f"{settings.api_v1_prefix}/community",
            "admin": f"{settings.api_v1_prefix}/admin"
        },
        "features": [
            "🤖 AI模型管理",
            "🔮 占卜预测服务", 
            "🔐 用户认证系统",
            "🌐 社区功能",
            "📊 性能监控",
            "💾 智能缓存",
            "🛡️ 安全防护"
        ]
    }

# 开发模式下的调试信息
if settings.is_development():
    @app.get("/debug/config", include_in_schema=False)
    async def debug_config():
        """调试配置信息（仅开发环境）"""
        return settings.export_config()

def create_app() -> FastAPI:
    """创建应用实例（工厂函数）"""
    return app

if __name__ == "__main__":
    # 直接运行时的配置
    uvicorn.run(
        "app.main_optimized:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload and settings.is_development(),
        workers=1 if settings.is_development() else settings.workers,
        log_level=settings.logging.log_level.value.lower(),
        access_log=True,
        use_colors=True
    ) 