#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端服务
提供统一的API调用接口
"""

import httpx
import asyncio
import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import json
from urllib.parse import urljoin

from app.models.api_models import (
    APIResponse, WxLoginRequest, WxLoginResponse, UserInfo,
    BirthInfoRequest, BirthInfo, AIChatRequest, AIChatResponse,
    AnalysisRecord, PointBalance, PointRecord, KnowledgeSearchRequest,
    KnowledgeSearchResult, KnowledgeDocument
)
from app.core.config import settings

logger = logging.getLogger(__name__)

class APIClientError(Exception):
    """API客户端异常"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(message)

class APIClient:
    """API客户端"""
    
    def __init__(self, base_url: str = None, timeout: int = 30):
        self.base_url = base_url or getattr(settings, 'api_base_url', 'http://localhost:8000')
        self.timeout = timeout
        self.session: Optional[httpx.AsyncClient] = None
        self._token: Optional[str] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def connect(self):
        """建立连接"""
        if not self.session:
            self.session = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "ChatBot-API-Client/1.0"
                }
            )
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.aclose()
            self.session = None
    
    def set_token(self, token: str):
        """设置认证令牌"""
        self._token = token
        if self.session:
            self.session.headers["Authorization"] = f"Bearer {token}"
    
    def clear_token(self):
        """清除认证令牌"""
        self._token = None
        if self.session:
            self.session.headers.pop("Authorization", None)
    
    async def _request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """发送HTTP请求"""
        if not self.session:
            await self.connect()
        
        url = urljoin(self.base_url, endpoint)
        
        # 合并请求头
        request_headers = {}
        if headers:
            request_headers.update(headers)
        
        try:
            logger.debug(f"发送 {method} 请求到 {url}")
            
            if method.upper() == "GET":
                response = await self.session.get(url, params=params, headers=request_headers)
            elif method.upper() == "POST":
                response = await self.session.post(url, json=data, params=params, headers=request_headers)
            elif method.upper() == "PUT":
                response = await self.session.put(url, json=data, params=params, headers=request_headers)
            elif method.upper() == "DELETE":
                response = await self.session.delete(url, params=params, headers=request_headers)
            else:
                raise APIClientError(f"不支持的HTTP方法: {method}")
            
            # 检查响应状态
            if response.status_code >= 400:
                error_data = None
                try:
                    error_data = response.json()
                except:
                    pass
                
                raise APIClientError(
                    f"API请求失败: {response.status_code}",
                    status_code=response.status_code,
                    response_data=error_data
                )
            
            # 解析响应
            try:
                result = response.json()
                logger.debug(f"API响应: {result}")
                return result
            except json.JSONDecodeError as e:
                raise APIClientError(f"响应JSON解析失败: {e}")
                
        except httpx.TimeoutException:
            raise APIClientError("请求超时")
        except httpx.RequestError as e:
            raise APIClientError(f"请求错误: {e}")
    
    # ===== 用户认证相关 =====
    
    async def wx_login(self, request: WxLoginRequest) -> WxLoginResponse:
        """微信登录"""
        response = await self._request("POST", "/api/auth/wx-login", data=request.dict())
        
        if response.get("status") == "success":
            login_data = response["data"]
            # 自动设置token
            if "token" in login_data:
                self.set_token(login_data["token"])
            return WxLoginResponse(**login_data)
        else:
            raise APIClientError(response.get("message", "登录失败"))
    
    async def get_user_profile(self) -> UserInfo:
        """获取用户信息"""
        response = await self._request("GET", "/api/user/profile")
        
        if response.get("status") == "success":
            return UserInfo(**response["data"]["user"])
        else:
            raise APIClientError(response.get("message", "获取用户信息失败"))
    
    async def update_user_profile(self, **kwargs) -> UserInfo:
        """更新用户信息"""
        response = await self._request("PUT", "/api/user/profile", data=kwargs)
        
        if response.get("status") == "success":
            return UserInfo(**response["data"]["user"])
        else:
            raise APIClientError(response.get("message", "更新用户信息失败"))
    
    # ===== 出生信息相关 =====
    
    async def save_birth_info(self, request: BirthInfoRequest) -> BirthInfo:
        """保存出生信息"""
        response = await self._request("POST", "/api/birth-info", data=request.dict())
        
        if response.get("status") == "success":
            return BirthInfo(**response["data"]["birth_info"])
        else:
            raise APIClientError(response.get("message", "保存出生信息失败"))
    
    async def get_birth_info(self) -> Optional[BirthInfo]:
        """获取出生信息"""
        try:
            response = await self._request("GET", "/api/birth-info")
            
            if response.get("status") == "success" and response.get("data", {}).get("birth_info"):
                return BirthInfo(**response["data"]["birth_info"])
            return None
        except APIClientError:
            return None
    
    async def update_birth_info(self, request: BirthInfoRequest) -> BirthInfo:
        """更新出生信息"""
        response = await self._request("PUT", "/api/birth-info", data=request.dict())
        
        if response.get("status") == "success":
            return BirthInfo(**response["data"]["birth_info"])
        else:
            raise APIClientError(response.get("message", "更新出生信息失败"))
    
    # ===== AI聊天相关 =====
    
    async def send_ai_message(self, request: AIChatRequest) -> AIChatResponse:
        """发送AI聊天消息"""
        response = await self._request("POST", "/api/ai-chat/message", data=request.dict())
        
        if response.get("status") == "success":
            return AIChatResponse(**response["data"])
        else:
            raise APIClientError(response.get("message", "AI聊天失败"))
    
    async def get_chat_history(
        self,
        session_id: Optional[str] = None,
        page: int = 1,
        limit: int = 20,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取聊天历史"""
        params = {"page": page, "limit": limit}
        if session_id:
            params["session_id"] = session_id
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
        
        response = await self._request("GET", "/api/ai-chat/history", params=params)
        
        if response.get("status") == "success":
            return response["data"]
        else:
            raise APIClientError(response.get("message", "获取聊天历史失败"))
    
    async def delete_chat_session(self, session_id: str) -> Dict[str, Any]:
        """删除聊天会话"""
        response = await self._request("DELETE", f"/api/ai-chat/session/{session_id}")
        
        if response.get("status") == "success":
            return response["data"]
        else:
            raise APIClientError(response.get("message", "删除聊天会话失败"))
    
    # ===== 命理分析相关 =====
    
    async def bazi_analysis(self, **kwargs) -> AnalysisRecord:
        """八字分析"""
        response = await self._request("POST", "/api/analysis/bazi", data=kwargs)
        
        if response.get("status") == "success":
            return AnalysisRecord(**response["data"])
        else:
            raise APIClientError(response.get("message", "八字分析失败"))
    
    async def yijing_analysis(self, **kwargs) -> AnalysisRecord:
        """易经占卜"""
        response = await self._request("POST", "/api/analysis/yijing", data=kwargs)
        
        if response.get("status") == "success":
            return AnalysisRecord(**response["data"])
        else:
            raise APIClientError(response.get("message", "易经占卜失败"))
    
    async def fengshui_analysis(self, **kwargs) -> AnalysisRecord:
        """风水分析"""
        response = await self._request("POST", "/api/analysis/fengshui", data=kwargs)
        
        if response.get("status") == "success":
            return AnalysisRecord(**response["data"])
        else:
            raise APIClientError(response.get("message", "风水分析失败"))
    
    async def wuxing_analysis(self, **kwargs) -> AnalysisRecord:
        """五行分析"""
        response = await self._request("POST", "/api/analysis/wuxing", data=kwargs)
        
        if response.get("status") == "success":
            return AnalysisRecord(**response["data"])
        else:
            raise APIClientError(response.get("message", "五行分析失败"))
    
    async def ziwei_analysis(self, **kwargs) -> AnalysisRecord:
        """紫薇斗数分析"""
        response = await self._request("POST", "/api/analysis/ziwei", data=kwargs)
        
        if response.get("status") == "success":
            return AnalysisRecord(**response["data"])
        else:
            raise APIClientError(response.get("message", "紫薇斗数分析失败"))
    
    async def marriage_analysis(self, **kwargs) -> AnalysisRecord:
        """合婚分析"""
        response = await self._request("POST", "/api/analysis/marriage", data=kwargs)
        
        if response.get("status") == "success":
            return AnalysisRecord(**response["data"])
        else:
            raise APIClientError(response.get("message", "合婚分析失败"))
    
    # ===== 积分系统相关 =====
    
    async def get_point_balance(self) -> PointBalance:
        """获取积分余额"""
        response = await self._request("GET", "/api/points/balance")
        
        if response.get("status") == "success":
            return PointBalance(**response["data"])
        else:
            raise APIClientError(response.get("message", "获取积分余额失败"))
    
    async def get_point_records(
        self,
        type: Optional[str] = None,
        source: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """获取积分记录"""
        params = {"page": page, "limit": limit}
        if type:
            params["type"] = type
        if source:
            params["source"] = source
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
        
        response = await self._request("GET", "/api/points/records", params=params)
        
        if response.get("status") == "success":
            return response["data"]
        else:
            raise APIClientError(response.get("message", "获取积分记录失败"))
    
    async def sign_in(self) -> Dict[str, Any]:
        """签到获取积分"""
        response = await self._request("POST", "/api/points/sign-in")
        
        if response.get("status") == "success":
            return response["data"]
        else:
            raise APIClientError(response.get("message", "签到失败"))
    
    # ===== 知识库相关 =====
    
    async def search_knowledge(self, request: KnowledgeSearchRequest) -> Dict[str, Any]:
        """搜索知识库"""
        response = await self._request("POST", "/api/knowledge/search", data=request.dict())
        
        if response.get("status") == "success":
            return response["data"]
        else:
            raise APIClientError(response.get("message", "知识库搜索失败"))
    
    async def get_knowledge_document(self, document_id: str, track_view: bool = True) -> KnowledgeDocument:
        """获取知识库文档详情"""
        params = {"track_view": track_view}
        response = await self._request("GET", f"/api/knowledge/documents/{document_id}", params=params)
        
        if response.get("status") == "success":
            return KnowledgeDocument(**response["data"]["document"])
        else:
            raise APIClientError(response.get("message", "获取文档详情失败"))

# 全局API客户端实例
_api_client: Optional[APIClient] = None

async def get_api_client() -> APIClient:
    """获取API客户端实例"""
    global _api_client
    if _api_client is None:
        _api_client = APIClient()
        await _api_client.connect()
    return _api_client

async def close_api_client():
    """关闭API客户端"""
    global _api_client
    if _api_client:
        await _api_client.close()
        _api_client = None
