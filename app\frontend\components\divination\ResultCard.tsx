import React from 'react';
import styled from 'styled-components';
import { <PERSON>, Text, Tag, But<PERSON>, Divider } from '../base';

interface ResultCardProps {
  title: string;
  type: 'iching' | 'tarot' | 'astrology';
  confidence: number;
  interpretation: {
    summary: string;
    details: Array<{
      title: string;
      content: string;
    }>;
    recommendations: Array<{
      text: string;
      priority: 'high' | 'medium' | 'low';
    }>;
  };
  timestamp: string;
  onShare: () => void;
  onSave: () => void;
}

const StyledResultCard = styled(Card)`
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const HeaderInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Actions = styled.div`
  display: flex;
  gap: 12px;
`;

const InterpretationSection = styled.div`
  margin: 24px 0;
`;

const RecommendationCard = styled.div<{ priority: 'high' | 'medium' | 'low' }>`
  padding: 16px;
  border-radius: ${props => props.theme.borderRadius.small};
  background-color: ${props => {
    switch (props.priority) {
      case 'high':
        return props.theme.colors.error + '11';
      case 'medium':
        return props.theme.colors.warning + '11';
      case 'low':
        return props.theme.colors.success + '11';
      default:
        return props.theme.colors.background;
    }
  }};
  margin-bottom: 12px;
`;

const PriorityTag = styled(Tag)<{ priority: 'high' | 'medium' | 'low' }>`
  ${props => {
    switch (props.priority) {
      case 'high':
        return `
          background-color: ${props.theme.colors.error}22;
          color: ${props.theme.colors.error};
        `;
      case 'medium':
        return `
          background-color: ${props.theme.colors.warning}22;
          color: ${props.theme.colors.warning};
        `;
      case 'low':
        return `
          background-color: ${props.theme.colors.success}22;
          color: ${props.theme.colors.success};
        `;
    }
  }}
`;

const ResultCard: React.FC<ResultCardProps> = ({
  title,
  type,
  confidence,
  interpretation,
  timestamp,
  onShare,
  onSave
}) => {
  const getTypeLabel = () => {
    switch (type) {
      case 'iching':
        return '易经';
      case 'tarot':
        return '塔罗';
      case 'astrology':
        return '占星';
      default:
        return '占卜';
    }
  };

  const getPriorityLabel = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high':
        return '重要';
      case 'medium':
        return '中等';
      case 'low':
        return '一般';
    }
  };

  return (
    <StyledResultCard elevation="large">
      <Header>
        <HeaderInfo>
          <Text variant="h1">{title}</Text>
          <Tag>{getTypeLabel()}</Tag>
          <Tag type={confidence > 80 ? 'success' : 'warning'}>
            准确度: {confidence}%
          </Tag>
        </HeaderInfo>
        <Actions>
          <Button variant="outline" size="small" onClick={onShare}>
            分享结果
          </Button>
          <Button variant="primary" size="small" onClick={onSave}>
            保存解读
          </Button>
        </Actions>
      </Header>

      <Text variant="body" color="secondary">
        解读时间: {timestamp}
      </Text>

      <Divider />

      <InterpretationSection>
        <Text variant="h2">总体解读</Text>
        <Text variant="body" style={{ marginTop: 16 }}>
          {interpretation.summary}
        </Text>
      </InterpretationSection>

      <Divider />

      <InterpretationSection>
        <Text variant="h2">详细分析</Text>
        {interpretation.details.map((detail, index) => (
          <div key={index} style={{ marginTop: 16 }}>
            <Text variant="body" style={{ fontWeight: 600 }}>
              {detail.title}
            </Text>
            <Text variant="body" color="secondary" style={{ marginTop: 8 }}>
              {detail.content}
            </Text>
          </div>
        ))}
      </InterpretationSection>

      <Divider />

      <InterpretationSection>
        <Text variant="h2">建议事项</Text>
        {interpretation.recommendations.map((recommendation, index) => (
          <RecommendationCard key={index} priority={recommendation.priority}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
              <PriorityTag priority={recommendation.priority}>
                {getPriorityLabel(recommendation.priority)}
              </PriorityTag>
            </div>
            <Text variant="body">{recommendation.text}</Text>
          </RecommendationCard>
        ))}
      </InterpretationSection>
    </StyledResultCard>
  );
};

export default ResultCard; 