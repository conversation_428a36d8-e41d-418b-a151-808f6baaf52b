"""
数据库配置模块
处理SQLAlchemy数据库连接和会话管理
"""
import os
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import logging

logger = logging.getLogger(__name__)

# 数据库配置
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "mysql+pymysql://root:password@localhost:3306/guaqi_qiankun?charset=utf8mb4"
)

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    # 连接池配置
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600,
    pool_pre_ping=True,
    # 其他配置
    echo=False,  # 生产环境设为False
    echo_pool=False,
    connect_args={
        "charset": "utf8mb4",
        "autocommit": False
    }
)

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# 声明基类
Base = declarative_base()

def get_db() -> Session:
    """
    获取数据库会话
    
    Yields:
        数据库会话对象
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话异常: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def create_tables():
    """创建所有数据表"""
    try:
        # 导入所有模型以确保它们被注册
        from app.models.user import User, UserProfile, DivinationRecord, UserFavorite, UserPoints, UserSignIn, MasterInfo
        
        # 创建表
        Base.metadata.create_all(bind=engine)
        logger.info("数据表创建成功")
        
    except Exception as e:
        logger.error(f"创建数据表失败: {e}")
        raise

def drop_tables():
    """删除所有数据表（慎用）"""
    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("所有数据表已删除")
    except Exception as e:
        logger.error(f"删除数据表失败: {e}")
        raise

def check_database_connection():
    """检查数据库连接"""
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        logger.info("数据库连接正常")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

# 数据库事件监听器
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """数据库连接时的配置"""
    if "mysql" in DATABASE_URL:
        # MySQL配置
        cursor = dbapi_connection.cursor()
        cursor.execute("SET SESSION sql_mode='TRADITIONAL'")
        cursor.execute("SET SESSION time_zone='+8:00'")
        cursor.close()

@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """连接池检出时的处理"""
    logger.debug("数据库连接从池中检出")

@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """连接池检入时的处理"""
    logger.debug("数据库连接返回到池中")

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_session(self) -> Session:
        """获取新的数据库会话"""
        return self.SessionLocal()
    
    def execute_raw_sql(self, sql: str, params: dict = None):
        """执行原始SQL"""
        db = self.get_session()
        try:
            result = db.execute(sql, params or {})
            db.commit()
            return result
        except Exception as e:
            db.rollback()
            logger.error(f"执行SQL失败: {sql}, 错误: {e}")
            raise
        finally:
            db.close()
    
    def backup_table(self, table_name: str, backup_name: str = None):
        """备份数据表"""
        if not backup_name:
            from datetime import datetime
            backup_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        sql = f"CREATE TABLE {backup_name} AS SELECT * FROM {table_name}"
        
        try:
            self.execute_raw_sql(sql)
            logger.info(f"表 {table_name} 备份为 {backup_name}")
            return backup_name
        except Exception as e:
            logger.error(f"备份表失败: {e}")
            raise
    
    def get_table_info(self, table_name: str):
        """获取表信息"""
        sql = f"""
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            COLUMN_COMMENT
        FROM information_schema.COLUMNS 
        WHERE TABLE_NAME = '{table_name}' 
        AND TABLE_SCHEMA = DATABASE()
        """
        
        try:
            result = self.execute_raw_sql(sql)
            return result.fetchall()
        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return []
    
    def optimize_table(self, table_name: str):
        """优化数据表"""
        sql = f"OPTIMIZE TABLE {table_name}"
        
        try:
            self.execute_raw_sql(sql)
            logger.info(f"表 {table_name} 优化完成")
        except Exception as e:
            logger.error(f"优化表失败: {e}")
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        stats_sql = """
        SELECT 
            TABLE_NAME,
            TABLE_ROWS,
            DATA_LENGTH,
            INDEX_LENGTH,
            CREATE_TIME,
            UPDATE_TIME
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = DATABASE()
        """
        
        try:
            result = self.execute_raw_sql(stats_sql)
            return result.fetchall()
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return []

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 健康检查函数
def health_check():
    """数据库健康检查"""
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}

# 初始化函数
def init_database():
    """初始化数据库"""
    try:
        # 检查连接
        if not check_database_connection():
            raise Exception("数据库连接失败")
        
        # 创建表
        create_tables()
        
        logger.info("数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False 