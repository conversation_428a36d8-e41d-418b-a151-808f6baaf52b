"""
认证工具模块
处理JWT token的生成、验证和用户认证
"""
import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Optional
from fastapi import HTTPException, status
import logging

logger = logging.getLogger(__name__)

# JWT配置
JWT_SECRET_KEY = "your-super-secret-jwt-key-change-in-production"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24 * 7  # 7天过期

def create_access_token(data: Dict) -> str:
    """
    创建访问令牌
    
    Args:
        data: 要编码的数据字典
        
    Returns:
        JWT token字符串
    """
    try:
        # 复制数据避免修改原始数据
        to_encode = data.copy()
        
        # 设置过期时间
        expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRE_HOURS)
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        # 编码JWT
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        logger.info(f"创建访问令牌成功，用户ID: {data.get('user_id', 'unknown')}")
        return encoded_jwt
        
    except Exception as e:
        logger.error(f"创建访问令牌失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌创建失败"
        )

def verify_token(token: str) -> Dict:
    """
    验证访问令牌
    
    Args:
        token: JWT token字符串
        
    Returns:
        解码后的数据字典
        
    Raises:
        HTTPException: 令牌无效或过期
    """
    try:
        # 解码JWT
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        # 检查必要字段
        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌格式无效"
            )
        
        return payload
        
    except jwt.ExpiredSignatureError:
        logger.warning("访问令牌已过期")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌已过期，请重新登录"
        )
    except jwt.JWTError as e:
        logger.warning(f"JWT验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌无效"
        )
    except Exception as e:
        logger.error(f"令牌验证异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="认证服务异常"
        )

def hash_password(password: str) -> str:
    """
    哈希密码
    
    Args:
        password: 明文密码
        
    Returns:
        哈希后的密码
    """
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    
    Args:
        plain_password: 明文密码
        hashed_password: 哈希密码
        
    Returns:
        密码是否匹配
    """
    return hash_password(plain_password) == hashed_password

def generate_api_key(user_id: str) -> str:
    """
    生成API密钥
    
    Args:
        user_id: 用户ID
        
    Returns:
        API密钥字符串
    """
    timestamp = str(datetime.utcnow().timestamp())
    raw_string = f"{user_id}:{timestamp}:{JWT_SECRET_KEY}"
    return hashlib.sha256(raw_string.encode()).hexdigest()

def validate_api_key(api_key: str, user_id: str) -> bool:
    """
    验证API密钥
    
    Args:
        api_key: API密钥
        user_id: 用户ID
        
    Returns:
        密钥是否有效
    """
    # 简单的API密钥验证（实际项目中应该存储在数据库中）
    try:
        # 这里可以添加更复杂的验证逻辑
        return len(api_key) == 64 and all(c in "0123456789abcdef" for c in api_key)
    except:
        return False

class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.failed_attempts = {}  # 失败尝试记录
        self.max_attempts = 5      # 最大尝试次数
        self.lockout_duration = 300 # 锁定时长（秒）
    
    def check_rate_limit(self, identifier: str) -> bool:
        """
        检查频率限制
        
        Args:
            identifier: 标识符（IP或用户ID）
            
        Returns:
            是否允许继续尝试
        """
        current_time = datetime.utcnow()
        
        if identifier in self.failed_attempts:
            attempts, last_attempt = self.failed_attempts[identifier]
            
            # 如果锁定时间已过，重置计数
            if (current_time - last_attempt).total_seconds() > self.lockout_duration:
                del self.failed_attempts[identifier]
                return True
            
            # 检查是否超过最大尝试次数
            if attempts >= self.max_attempts:
                return False
        
        return True
    
    def record_failed_attempt(self, identifier: str):
        """
        记录失败尝试
        
        Args:
            identifier: 标识符
        """
        current_time = datetime.utcnow()
        
        if identifier in self.failed_attempts:
            attempts, _ = self.failed_attempts[identifier]
            self.failed_attempts[identifier] = (attempts + 1, current_time)
        else:
            self.failed_attempts[identifier] = (1, current_time)
    
    def clear_failed_attempts(self, identifier: str):
        """
        清除失败尝试记录
        
        Args:
            identifier: 标识符
        """
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]

# 全局认证管理器实例
auth_manager = AuthManager()

def get_current_user_id(token: str) -> str:
    """
    从token中获取当前用户ID
    
    Args:
        token: JWT token
        
    Returns:
        用户ID
    """
    payload = verify_token(token)
    return payload.get("user_id")

def create_refresh_token(user_id: str) -> str:
    """
    创建刷新令牌
    
    Args:
        user_id: 用户ID
        
    Returns:
        刷新令牌
    """
    data = {
        "user_id": user_id,
        "type": "refresh"
    }
    # 刷新令牌有效期更长
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=30)  # 30天
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    
    return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

def verify_refresh_token(token: str) -> str:
    """
    验证刷新令牌并返回用户ID
    
    Args:
        token: 刷新令牌
        
    Returns:
        用户ID
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        return payload.get("user_id")
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌已过期"
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌无效"
        ) 