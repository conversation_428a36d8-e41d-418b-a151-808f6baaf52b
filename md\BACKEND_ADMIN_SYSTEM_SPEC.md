# 卦里乾坤后端管理系统界面功能规范

## 📋 系统概述

本文档详细定义了"卦里乾坤"小程序后端管理系统的所有界面、功能、参数和交互规范，可直接用于前端开发和UI设计。

## 🏠 1. 仪表盘 (Dashboard)

### 1.1 主仪表盘页面
**路由**: `/dashboard`
**权限**: 所有管理员

#### 页面布局
```javascript
{
  "layout": "grid",
  "columns": 4,
  "rows": "auto",
  "components": [
    {
      "type": "stat_card_group",
      "span": 4,
      "cards": [
        {
          "title": "总用户数",
          "value": "{{totalUsers}}",
          "trend": "{{userGrowthRate}}",
          "icon": "users",
          "color": "blue",
          "link": "/users"
        },
        {
          "title": "今日分析",
          "value": "{{todayAnalyses}}",
          "trend": "{{analysisGrowthRate}}",
          "icon": "chart-line",
          "color": "green",
          "link": "/analyses"
        },
        {
          "title": "总收入",
          "value": "{{totalRevenue}}",
          "trend": "{{revenueGrowthRate}}",
          "icon": "dollar-sign",
          "color": "purple",
          "link": "/revenue"
        },
        {
          "title": "AI对话",
          "value": "{{totalChats}}",
          "trend": "{{chatGrowthRate}}",
          "icon": "message-circle",
          "color": "orange",
          "link": "/ai-chat"
        }
      ]
    }
  ]
}
```

#### 图表组件
```javascript
{
  "charts": [
    {
      "id": "user_growth_chart",
      "type": "line",
      "title": "用户增长趋势",
      "span": 2,
      "height": 300,
      "api": "/api/admin/dashboard/user-growth",
      "params": {
        "period": "30d",
        "granularity": "day"
      },
      "config": {
        "xAxis": "date",
        "yAxis": "count",
        "smooth": true,
        "area": true
      }
    },
    {
      "id": "analysis_distribution",
      "type": "pie",
      "title": "分析类型分布",
      "span": 2,
      "height": 300,
      "api": "/api/admin/dashboard/analysis-distribution",
      "config": {
        "labelField": "type",
        "valueField": "count",
        "showPercentage": true
      }
    },
    {
      "id": "revenue_chart",
      "type": "bar",
      "title": "收入统计",
      "span": 2,
      "height": 300,
      "api": "/api/admin/dashboard/revenue-stats",
      "params": {
        "period": "7d"
      }
    },
    {
      "id": "system_health",
      "type": "gauge",
      "title": "系统健康度",
      "span": 2,
      "height": 300,
      "api": "/api/admin/dashboard/system-health",
      "config": {
        "min": 0,
        "max": 100,
        "thresholds": [
          {"value": 60, "color": "red"},
          {"value": 80, "color": "orange"},
          {"value": 100, "color": "green"}
        ]
      }
    }
  ]
}
```

#### 快捷操作面板
```javascript
{
  "quick_actions": {
    "title": "快捷操作",
    "actions": [
      {
        "id": "add_user",
        "title": "添加用户",
        "icon": "user-plus",
        "action": "modal",
        "target": "/users/create"
      },
      {
        "id": "system_announcement",
        "title": "系统公告",
        "icon": "megaphone",
        "action": "modal",
        "target": "/announcements/create"
      },
      {
        "id": "export_data",
        "title": "数据导出",
        "icon": "download",
        "action": "modal",
        "target": "/export"
      },
      {
        "id": "system_settings",
        "title": "系统设置",
        "icon": "settings",
        "action": "navigate",
        "target": "/settings"
      }
    ]
  }
}
```

## 👥 2. 用户管理 (User Management)

### 2.1 用户列表页面
**路由**: `/users`
**权限**: 用户管理权限

#### 页面配置
```javascript
{
  "page": {
    "title": "用户管理",
    "subtitle": "管理所有注册用户",
    "breadcrumb": ["首页", "用户管理"]
  },
  "toolbar": {
    "search": {
      "placeholder": "搜索用户昵称、手机号、邮箱",
      "fields": ["nickname", "phone", "email"],
      "realtime": true
    },
    "filters": [
      {
        "field": "status",
        "type": "select",
        "label": "状态",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "0", "label": "正常"},
          {"value": "1", "label": "禁用"},
          {"value": "2", "label": "删除"}
        ]
      },
      {
        "field": "vip_level",
        "type": "select",
        "label": "VIP等级",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "0", "label": "普通用户"},
          {"value": "1", "label": "VIP"},
          {"value": "2", "label": "SVIP"}
        ]
      },
      {
        "field": "register_time",
        "type": "daterange",
        "label": "注册时间",
        "format": "YYYY-MM-DD"
      }
    ],
    "actions": [
      {
        "type": "button",
        "text": "批量操作",
        "icon": "layers",
        "action": "batch_modal"
      },
      {
        "type": "button",
        "text": "导出数据",
        "icon": "download",
        "action": "export"
      },
      {
        "type": "button",
        "text": "添加用户",
        "icon": "plus",
        "action": "create_modal",
        "style": "primary"
      }
    ]
  }
}
```

#### 表格配置
```javascript
{
  "table": {
    "api": "/api/admin/users",
    "rowKey": "id",
    "selection": true,
    "pagination": {
      "pageSize": 20,
      "showSizeChanger": true,
      "showQuickJumper": true
    },
    "columns": [
      {
        "key": "avatar_nickname",
        "title": "用户信息",
        "width": 200,
        "render": "user_info",
        "fields": ["avatar_url", "nickname", "id"]
      },
      {
        "key": "contact",
        "title": "联系方式",
        "width": 150,
        "render": "contact_info",
        "fields": ["phone", "email"]
      },
      {
        "key": "vip_info",
        "title": "VIP信息",
        "width": 120,
        "render": "vip_badge",
        "fields": ["vip_level", "vip_expire_time"]
      },
      {
        "key": "points",
        "title": "积分",
        "width": 100,
        "dataIndex": "points",
        "sorter": true,
        "render": "number"
      },
      {
        "key": "statistics",
        "title": "使用统计",
        "width": 150,
        "render": "user_stats",
        "fields": ["analysis_count", "total_spent"]
      },
      {
        "key": "status",
        "title": "状态",
        "width": 100,
        "dataIndex": "status",
        "render": "status_badge",
        "filters": [
          {"text": "正常", "value": "0"},
          {"text": "禁用", "value": "1"},
          {"text": "删除", "value": "2"}
        ]
      },
      {
        "key": "register_time",
        "title": "注册时间",
        "width": 150,
        "dataIndex": "register_time",
        "render": "datetime",
        "sorter": true
      },
      {
        "key": "last_login_time",
        "title": "最后登录",
        "width": 150,
        "dataIndex": "last_login_time",
        "render": "relative_time"
      },
      {
        "key": "actions",
        "title": "操作",
        "width": 200,
        "fixed": "right",
        "render": "actions",
        "actions": [
          {
            "text": "查看",
            "icon": "eye",
            "action": "view_detail"
          },
          {
            "text": "编辑",
            "icon": "edit",
            "action": "edit_modal"
          },
          {
            "text": "更多",
            "icon": "more-horizontal",
            "action": "dropdown",
            "items": [
              {"text": "重置密码", "action": "reset_password"},
              {"text": "调整积分", "action": "adjust_points"},
              {"text": "设置VIP", "action": "set_vip"},
              {"text": "禁用账户", "action": "disable_user", "confirm": true},
              {"text": "删除用户", "action": "delete_user", "confirm": true, "danger": true}
            ]
          }
        ]
      }
    ]
  }
}
```

### 2.2 用户详情页面
**路由**: `/users/:id`
**权限**: 用户管理权限

#### 页面布局
```javascript
{
  "layout": {
    "type": "tabs",
    "tabs": [
      {
        "key": "basic",
        "title": "基本信息",
        "icon": "user"
      },
      {
        "key": "birth_info",
        "title": "出生信息",
        "icon": "calendar"
      },
      {
        "key": "usage",
        "title": "使用记录",
        "icon": "activity"
      },
      {
        "key": "points",
        "title": "积分记录",
        "icon": "coins"
      },
      {
        "key": "chat",
        "title": "聊天记录",
        "icon": "message-circle"
      }
    ]
  }
}
```

#### 基本信息Tab
```javascript
{
  "basic_info": {
    "sections": [
      {
        "title": "个人资料",
        "type": "form",
        "editable": true,
        "fields": [
          {
            "name": "nickname",
            "label": "昵称",
            "type": "input",
            "required": true,
            "rules": [{"max": 50, "message": "昵称不能超过50个字符"}]
          },
          {
            "name": "avatar_url",
            "label": "头像",
            "type": "image_upload",
            "accept": "image/*",
            "maxSize": "2MB"
          },
          {
            "name": "phone",
            "label": "手机号",
            "type": "input",
            "pattern": "phone"
          },
          {
            "name": "email",
            "label": "邮箱",
            "type": "input",
            "pattern": "email"
          }
        ]
      },
      {
        "title": "账户状态",
        "type": "form",
        "editable": true,
        "fields": [
          {
            "name": "status",
            "label": "账户状态",
            "type": "radio",
            "options": [
              {"value": "0", "label": "正常"},
              {"value": "1", "label": "禁用"},
              {"value": "2", "label": "删除"}
            ]
          },
          {
            "name": "vip_level",
            "label": "VIP等级",
            "type": "select",
            "options": [
              {"value": "0", "label": "普通用户"},
              {"value": "1", "label": "VIP"},
              {"value": "2", "label": "SVIP"}
            ]
          },
          {
            "name": "vip_expire_time",
            "label": "VIP过期时间",
            "type": "datetime",
            "dependency": "vip_level > 0"
          },
          {
            "name": "points",
            "label": "积分余额",
            "type": "number",
            "min": 0,
            "addonAfter": "分"
          }
        ]
      },
      {
        "title": "统计信息",
        "type": "descriptions",
        "readonly": true,
        "fields": [
          {
            "label": "注册时间",
            "value": "{{register_time}}",
            "render": "datetime"
          },
          {
            "label": "最后登录",
            "value": "{{last_login_time}}",
            "render": "relative_time"
          },
          {
            "label": "登录次数",
            "value": "{{login_count}}",
            "render": "number"
          },
          {
            "label": "分析次数",
            "value": "{{analysis_count}}",
            "render": "number"
          },
          {
            "label": "总消费",
            "value": "{{total_spent}}",
            "render": "currency"
          }
        ]
      }
    ]
  }
}
```

## 📊 3. 分析记录管理

### 3.1 分析记录列表
**路由**: `/analyses`
**权限**: 分析管理权限

#### 页面配置
```javascript
{
  "page": {
    "title": "分析记录管理",
    "subtitle": "查看和管理所有分析记录"
  },
  "toolbar": {
    "search": {
      "placeholder": "搜索用户昵称或分析ID",
      "fields": ["user_nickname", "id"]
    },
    "filters": [
      {
        "field": "analysis_type",
        "type": "select",
        "label": "分析类型",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "bazi", "label": "八字分析"},
          {"value": "yijing", "label": "易经占卜"},
          {"value": "fengshui", "label": "风水分析"},
          {"value": "wuxing", "label": "五行分析"},
          {"value": "ziwei", "label": "紫薇斗数"},
          {"value": "marriage", "label": "合婚分析"}
        ]
      },
      {
        "field": "score_range",
        "type": "slider",
        "label": "评分范围",
        "min": 0,
        "max": 100,
        "range": true
      },
      {
        "field": "created_at",
        "type": "daterange",
        "label": "分析时间"
      }
    ],
    "actions": [
      {
        "text": "导出报告",
        "icon": "file-text",
        "action": "export_reports"
      },
      {
        "text": "批量删除",
        "icon": "trash-2",
        "action": "batch_delete",
        "danger": true
      }
    ]
  },
  "table": {
    "api": "/api/admin/analyses",
    "columns": [
      {
        "key": "user_info",
        "title": "用户信息",
        "width": 150,
        "render": "user_link",
        "fields": ["user_id", "user_nickname", "user_avatar"]
      },
      {
        "key": "analysis_type",
        "title": "分析类型",
        "width": 120,
        "render": "analysis_type_badge"
      },
      {
        "key": "score",
        "title": "评分",
        "width": 100,
        "render": "score_progress",
        "sorter": true
      },
      {
        "key": "points_cost",
        "title": "消耗积分",
        "width": 100,
        "render": "number",
        "sorter": true
      },
      {
        "key": "processing_time",
        "title": "处理时间",
        "width": 100,
        "render": "duration",
        "suffix": "ms"
      },
      {
        "key": "created_at",
        "title": "分析时间",
        "width": 150,
        "render": "datetime",
        "sorter": true
      },
      {
        "key": "actions",
        "title": "操作",
        "width": 150,
        "render": "actions",
        "actions": [
          {
            "text": "查看详情",
            "action": "view_detail"
          },
          {
            "text": "删除",
            "action": "delete",
            "confirm": true,
            "danger": true
          }
        ]
      }
    ]
  }
}
```

## 🤖 4. AI聊天管理

### 4.1 聊天会话列表
**路由**: `/ai-chat/sessions`
**权限**: AI管理权限

#### 页面配置
```javascript
{
  "page": {
    "title": "AI聊天管理",
    "subtitle": "管理AI聊天会话和消息"
  },
  "toolbar": {
    "search": {
      "placeholder": "搜索用户或会话ID"
    },
    "filters": [
      {
        "field": "status",
        "type": "select",
        "label": "会话状态",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "active", "label": "活跃"},
          {"value": "ended", "label": "已结束"},
          {"value": "archived", "label": "已归档"}
        ]
      },
      {
        "field": "intent",
        "type": "select",
        "label": "意图类型",
        "api": "/api/admin/ai-chat/intents/list"
      }
    ],
    "actions": [
      {
        "text": "质量分析",
        "icon": "bar-chart-2",
        "action": "quality_analysis"
      },
      {
        "text": "批量归档",
        "icon": "archive",
        "action": "batch_archive"
      }
    ]
  },
  "table": {
    "api": "/api/admin/ai-chat/sessions",
    "expandable": true,
    "columns": [
      {
        "key": "user_info",
        "title": "用户",
        "width": 150,
        "render": "user_link"
      },
      {
        "key": "title",
        "title": "会话标题",
        "width": 200,
        "ellipsis": true
      },
      {
        "key": "message_count",
        "title": "消息数",
        "width": 80,
        "render": "number"
      },
      {
        "key": "total_tokens",
        "title": "Token消耗",
        "width": 100,
        "render": "number"
      },
      {
        "key": "total_cost",
        "title": "成本",
        "width": 80,
        "render": "currency"
      },
      {
        "key": "status",
        "title": "状态",
        "width": 80,
        "render": "status_badge"
      },
      {
        "key": "start_time",
        "title": "开始时间",
        "width": 150,
        "render": "datetime"
      },
      {
        "key": "actions",
        "title": "操作",
        "width": 150,
        "render": "actions",
        "actions": [
          {
            "text": "查看对话",
            "action": "view_messages"
          },
          {
            "text": "归档",
            "action": "archive"
          }
        ]
      }
    ]
  }
}
```

### 4.2 AI配置管理
**路由**: `/ai-chat/config`
**权限**: 系统管理权限

#### 配置表单
```javascript
{
  "config_form": {
    "sections": [
      {
        "title": "模型配置",
        "fields": [
          {
            "name": "model_name",
            "label": "AI模型",
            "type": "select",
            "options": [
              {"value": "gpt-3.5-turbo", "label": "GPT-3.5 Turbo"},
              {"value": "gpt-4", "label": "GPT-4"},
              {"value": "claude-3", "label": "Claude-3"}
            ],
            "required": true
          },
          {
            "name": "temperature",
            "label": "创造性",
            "type": "slider",
            "min": 0,
            "max": 2,
            "step": 0.1,
            "marks": {
              "0": "保守",
              "1": "平衡",
              "2": "创新"
            }
          },
          {
            "name": "max_tokens",
            "label": "最大Token数",
            "type": "number",
            "min": 100,
            "max": 4000,
            "step": 100
          }
        ]
      },
      {
        "title": "对话配置",
        "fields": [
          {
            "name": "max_history",
            "label": "最大历史记录",
            "type": "number",
            "min": 5,
            "max": 50,
            "addonAfter": "条"
          },
          {
            "name": "session_timeout",
            "label": "会话超时",
            "type": "number",
            "min": 5,
            "max": 120,
            "addonAfter": "分钟"
          },
          {
            "name": "typing_delay",
            "label": "打字延迟",
            "type": "number",
            "min": 500,
            "max": 3000,
            "addonAfter": "毫秒"
          }
        ]
      }
    ]
  }
}
```

## 📚 5. 知识库管理

### 5.1 文档管理
**路由**: `/knowledge/documents`
**权限**: 内容管理权限

#### 页面配置
```javascript
{
  "page": {
    "title": "知识库文档管理",
    "subtitle": "管理知识库文档内容"
  },
  "toolbar": {
    "search": {
      "placeholder": "搜索文档标题或内容"
    },
    "filters": [
      {
        "field": "category",
        "type": "tree_select",
        "label": "分类",
        "api": "/api/admin/knowledge/categories/tree"
      },
      {
        "field": "status",
        "type": "select",
        "label": "状态",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "draft", "label": "草稿"},
          {"value": "published", "label": "已发布"},
          {"value": "archived", "label": "已归档"}
        ]
      },
      {
        "field": "difficulty_level",
        "type": "select",
        "label": "难度",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "beginner", "label": "初级"},
          {"value": "intermediate", "label": "中级"},
          {"value": "advanced", "label": "高级"}
        ]
      }
    ],
    "actions": [
      {
        "text": "新建文档",
        "icon": "plus",
        "action": "create",
        "style": "primary"
      },
      {
        "text": "批量导入",
        "icon": "upload",
        "action": "batch_import"
      },
      {
        "text": "重建索引",
        "icon": "refresh-cw",
        "action": "rebuild_index"
      }
    ]
  },
  "table": {
    "api": "/api/admin/knowledge/documents",
    "columns": [
      {
        "key": "title",
        "title": "文档标题",
        "width": 250,
        "ellipsis": true,
        "render": "link"
      },
      {
        "key": "category",
        "title": "分类",
        "width": 120,
        "render": "category_path"
      },
      {
        "key": "author",
        "title": "作者",
        "width": 100
      },
      {
        "key": "status",
        "title": "状态",
        "width": 80,
        "render": "status_badge"
      },
      {
        "key": "difficulty_level",
        "title": "难度",
        "width": 80,
        "render": "difficulty_badge"
      },
      {
        "key": "view_count",
        "title": "查看数",
        "width": 80,
        "render": "number",
        "sorter": true
      },
      {
        "key": "like_count",
        "title": "点赞数",
        "width": 80,
        "render": "number",
        "sorter": true
      },
      {
        "key": "published_at",
        "title": "发布时间",
        "width": 150,
        "render": "datetime",
        "sorter": true
      },
      {
        "key": "actions",
        "title": "操作",
        "width": 200,
        "render": "actions",
        "actions": [
          {
            "text": "编辑",
            "action": "edit"
          },
          {
            "text": "预览",
            "action": "preview"
          },
          {
            "text": "更多",
            "action": "dropdown",
            "items": [
              {"text": "复制", "action": "duplicate"},
              {"text": "发布", "action": "publish"},
              {"text": "归档", "action": "archive"},
              {"text": "删除", "action": "delete", "danger": true}
            ]
          }
        ]
      }
    ]
  }
}
```

### 5.2 文档编辑器
**路由**: `/knowledge/documents/edit/:id`
**权限**: 内容管理权限

#### 编辑器配置
```javascript
{
  "editor": {
    "type": "markdown",
    "toolbar": [
      "bold", "italic", "strikethrough", "|",
      "heading", "quote", "code", "|",
      "unordered-list", "ordered-list", "|",
      "link", "image", "table", "|",
      "preview", "fullscreen"
    ],
    "plugins": [
      "image-upload",
      "table-editor",
      "code-highlight",
      "math-formula"
    ],
    "upload": {
      "api": "/api/admin/upload/image",
      "maxSize": "5MB",
      "accept": "image/*"
    }
  },
  "sidebar": {
    "sections": [
      {
        "title": "文档设置",
        "fields": [
          {
            "name": "title",
            "label": "标题",
            "type": "input",
            "required": true
          },
          {
            "name": "summary",
            "label": "摘要",
            "type": "textarea",
            "rows": 3
          },
          {
            "name": "category",
            "label": "分类",
            "type": "tree_select",
            "api": "/api/admin/knowledge/categories/tree"
          },
          {
            "name": "tags",
            "label": "标签",
            "type": "tag_input",
            "max": 10
          },
          {
            "name": "difficulty_level",
            "label": "难度等级",
            "type": "radio",
            "options": [
              {"value": "beginner", "label": "初级"},
              {"value": "intermediate", "label": "中级"},
              {"value": "advanced", "label": "高级"}
            ]
          }
        ]
      },
      {
        "title": "发布设置",
        "fields": [
          {
            "name": "status",
            "label": "状态",
            "type": "radio",
            "options": [
              {"value": "draft", "label": "草稿"},
              {"value": "published", "label": "发布"}
            ]
          },
          {
            "name": "published_at",
            "label": "发布时间",
            "type": "datetime",
            "dependency": "status === 'published'"
          }
        ]
      }
    ]
  }
}
```

## 💰 6. 积分系统管理

### 6.1 积分配置
**路由**: `/points/config`
**权限**: 系统管理权限

#### 配置表单
```javascript
{
  "point_config": {
    "sections": [
      {
        "title": "获取规则",
        "description": "设置用户获取积分的规则",
        "fields": [
          {
            "name": "sign_in_reward",
            "label": "每日签到奖励",
            "type": "number",
            "min": 1,
            "max": 100,
            "addonAfter": "积分",
            "help": "用户每日签到可获得的积分"
          },
          {
            "name": "consecutive_bonus",
            "label": "连续签到奖励",
            "type": "form_list",
            "fields": [
              {
                "name": "days",
                "label": "连续天数",
                "type": "number",
                "min": 2
              },
              {
                "name": "bonus",
                "label": "额外奖励",
                "type": "number",
                "min": 1
              }
            ]
          },
          {
            "name": "share_reward",
            "label": "分享奖励",
            "type": "number",
            "min": 1,
            "max": 50,
            "addonAfter": "积分/次"
          },
          {
            "name": "share_daily_limit",
            "label": "分享每日上限",
            "type": "number",
            "min": 1,
            "max": 10,
            "addonAfter": "次"
          },
          {
            "name": "invite_reward",
            "label": "邀请奖励",
            "type": "number",
            "min": 10,
            "max": 500,
            "addonAfter": "积分/人"
          }
        ]
      },
      {
        "title": "消费规则",
        "description": "设置各项功能的积分消费",
        "fields": [
          {
            "name": "analysis_costs",
            "label": "分析消费",
            "type": "object",
            "fields": [
              {
                "name": "bazi",
                "label": "八字分析",
                "type": "number",
                "min": 0,
                "addonAfter": "积分"
              },
              {
                "name": "yijing",
                "label": "易经占卜",
                "type": "number",
                "min": 0,
                "addonAfter": "积分"
              },
              {
                "name": "fengshui",
                "label": "风水分析",
                "type": "number",
                "min": 0,
                "addonAfter": "积分"
              },
              {
                "name": "wuxing",
                "label": "五行分析",
                "type": "number",
                "min": 0,
                "addonAfter": "积分"
              },
              {
                "name": "ziwei",
                "label": "紫薇斗数",
                "type": "number",
                "min": 0,
                "addonAfter": "积分"
              },
              {
                "name": "marriage",
                "label": "合婚分析",
                "type": "number",
                "min": 0,
                "addonAfter": "积分"
              }
            ]
          },
          {
            "name": "vip_discounts",
            "label": "VIP折扣",
            "type": "object",
            "fields": [
              {
                "name": "vip_discount",
                "label": "VIP折扣",
                "type": "number",
                "min": 0.1,
                "max": 1,
                "step": 0.1,
                "addonAfter": "折"
              },
              {
                "name": "svip_discount",
                "label": "SVIP折扣",
                "type": "number",
                "min": 0.1,
                "max": 1,
                "step": 0.1,
                "addonAfter": "折"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 6.2 积分记录管理
**路由**: `/points/records`
**权限**: 积分管理权限

#### 页面配置
```javascript
{
  "page": {
    "title": "积分记录管理",
    "subtitle": "查看和管理用户积分记录"
  },
  "toolbar": {
    "search": {
      "placeholder": "搜索用户昵称或记录ID"
    },
    "filters": [
      {
        "field": "type",
        "type": "select",
        "label": "记录类型",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "earn", "label": "获得"},
          {"value": "spend", "label": "消费"},
          {"value": "refund", "label": "退还"},
          {"value": "expire", "label": "过期"}
        ]
      },
      {
        "field": "source",
        "type": "select",
        "label": "来源",
        "options": [
          {"value": "", "label": "全部"},
          {"value": "sign_in", "label": "签到"},
          {"value": "share", "label": "分享"},
          {"value": "invite", "label": "邀请"},
          {"value": "purchase", "label": "购买"},
          {"value": "analysis", "label": "分析"},
          {"value": "refund", "label": "退款"}
        ]
      },
      {
        "field": "amount_range",
        "type": "input_number_range",
        "label": "积分范围"
      },
      {
        "field": "created_at",
        "type": "daterange",
        "label": "时间范围"
      }
    ],
    "actions": [
      {
        "text": "批量发放",
        "icon": "gift",
        "action": "batch_grant",
        "style": "primary"
      },
      {
        "text": "导出记录",
        "icon": "download",
        "action": "export"
      }
    ]
  },
  "table": {
    "api": "/api/admin/points/records",
    "summary": {
      "api": "/api/admin/points/summary",
      "fields": [
        {"label": "总发放积分", "field": "total_earned"},
        {"label": "总消费积分", "field": "total_spent"},
        {"label": "净增长", "field": "net_change"}
      ]
    },
    "columns": [
      {
        "key": "user_info",
        "title": "用户",
        "width": 150,
        "render": "user_link"
      },
      {
        "key": "type",
        "title": "类型",
        "width": 80,
        "render": "type_badge"
      },
      {
        "key": "amount",
        "title": "积分变动",
        "width": 120,
        "render": "amount_change",
        "sorter": true
      },
      {
        "key": "source",
        "title": "来源",
        "width": 100,
        "render": "source_badge"
      },
      {
        "key": "description",
        "title": "描述",
        "width": 200,
        "ellipsis": true
      },
      {
        "key": "balance_after",
        "title": "余额",
        "width": 100,
        "render": "number"
      },
      {
        "key": "created_at",
        "title": "时间",
        "width": 150,
        "render": "datetime",
        "sorter": true
      }
    ]
  }
}
```

## ⚙️ 7. 系统配置

### 7.1 基础配置
**路由**: `/settings/basic`
**权限**: 系统管理权限

#### 配置表单
```javascript
{
  "basic_settings": {
    "sections": [
      {
        "title": "应用信息",
        "fields": [
          {
            "name": "app_name",
            "label": "应用名称",
            "type": "input",
            "required": true,
            "placeholder": "卦里乾坤"
          },
          {
            "name": "app_logo",
            "label": "应用Logo",
            "type": "image_upload",
            "accept": "image/*",
            "maxSize": "2MB"
          },
          {
            "name": "app_description",
            "label": "应用描述",
            "type": "textarea",
            "rows": 3
          },
          {
            "name": "contact_info",
            "label": "联系方式",
            "type": "object",
            "fields": [
              {
                "name": "phone",
                "label": "客服电话",
                "type": "input"
              },
              {
                "name": "email",
                "label": "客服邮箱",
                "type": "input"
              },
              {
                "name": "wechat",
                "label": "微信号",
                "type": "input"
              }
            ]
          }
        ]
      },
      {
        "title": "功能开关",
        "fields": [
          {
            "name": "registration_enabled",
            "label": "允许注册",
            "type": "switch",
            "help": "关闭后新用户无法注册"
          },
          {
            "name": "maintenance_mode",
            "label": "维护模式",
            "type": "switch",
            "help": "开启后用户无法使用功能"
          },
          {
            "name": "ai_chat_enabled",
            "label": "AI聊天功能",
            "type": "switch"
          },
          {
            "name": "analysis_enabled",
            "label": "分析功能",
            "type": "switch"
          },
          {
            "name": "wxwork_enabled",
            "label": "企业微信功能",
            "type": "switch"
          }
        ]
      },
      {
        "title": "限制设置",
        "fields": [
          {
            "name": "max_daily_analyses",
            "label": "每日分析上限",
            "type": "number",
            "min": 1,
            "max": 100,
            "addonAfter": "次"
          },
          {
            "name": "max_chat_sessions",
            "label": "最大聊天会话",
            "type": "number",
            "min": 1,
            "max": 10,
            "addonAfter": "个"
          },
          {
            "name": "file_upload_limit",
            "label": "文件上传限制",
            "type": "number",
            "min": 1,
            "max": 50,
            "addonAfter": "MB"
          }
        ]
      }
    ]
  }
}
```

## 📊 8. 数据分析

### 8.1 用户分析
**路由**: `/analytics/users`
**权限**: 数据分析权限

#### 分析面板
```javascript
{
  "user_analytics": {
    "filters": {
      "period": {
        "type": "date_range_picker",
        "presets": [
          {"label": "今天", "value": "today"},
          {"label": "昨天", "value": "yesterday"},
          {"label": "最近7天", "value": "7d"},
          {"label": "最近30天", "value": "30d"},
          {"label": "本月", "value": "month"},
          {"label": "上月", "value": "last_month"}
        ]
      },
      "user_type": {
        "type": "select",
        "options": [
          {"value": "all", "label": "全部用户"},
          {"value": "new", "label": "新用户"},
          {"value": "active", "label": "活跃用户"},
          {"value": "vip", "label": "VIP用户"}
        ]
      }
    },
    "metrics": [
      {
        "title": "用户概览",
        "type": "metric_cards",
        "cards": [
          {
            "title": "总用户数",
            "value": "{{totalUsers}}",
            "trend": "{{userGrowthRate}}",
            "icon": "users"
          },
          {
            "title": "新增用户",
            "value": "{{newUsers}}",
            "trend": "{{newUserGrowthRate}}",
            "icon": "user-plus"
          },
          {
            "title": "活跃用户",
            "value": "{{activeUsers}}",
            "trend": "{{activeUserRate}}",
            "icon": "activity"
          },
          {
            "title": "用户留存率",
            "value": "{{retentionRate}}",
            "trend": "{{retentionTrend}}",
            "icon": "repeat"
          }
        ]
      },
      {
        "title": "用户增长趋势",
        "type": "line_chart",
        "api": "/api/admin/analytics/user-growth",
        "height": 400
      },
      {
        "title": "用户地域分布",
        "type": "map_chart",
        "api": "/api/admin/analytics/user-distribution",
        "height": 400
      },
      {
        "title": "用户行为分析",
        "type": "funnel_chart",
        "api": "/api/admin/analytics/user-funnel",
        "height": 300
      }
    ]
  }
}
```

---

## 📝 总结

这份后端管理系统界面功能规范文档提供了：

### 🎯 **完整的界面规范**
- **8个主要功能模块** - 覆盖所有管理需求
- **详细的页面配置** - 可直接用于前端开发
- **标准化的组件定义** - 统一的UI组件规范
- **完整的交互逻辑** - 用户操作流程定义

### 🔧 **技术实现指导**
- **API接口映射** - 与后端API完美对接
- **权限控制规范** - 细粒度的权限管理
- **数据展示配置** - 灵活的数据可视化
- **表单验证规则** - 完整的数据验证

### 📊 **管理功能覆盖**
- **用户管理** - 用户信息、状态、权限管理
- **内容管理** - 知识库、分析记录管理
- **系统配置** - 功能开关、安全设置
- **数据分析** - 用户行为、业务指标分析

这份规范可以直接用于前端开发团队进行后台管理系统的开发，确保界面功能的完整性和一致性！
