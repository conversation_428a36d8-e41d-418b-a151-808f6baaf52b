from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
from app.models.ai.model_handler import model_handler
from app.models.ai.model_downloader import model_downloader
from pydantic import BaseModel

router = APIRouter(
    prefix="/api/model",
    tags=["Model Management"],
    responses={404: {"description": "Not found"}},
)

class GenerateRequest(BaseModel):
    prompt: str
    max_length: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50

    class Config:
        schema_extra = {
            "example": {
                "prompt": "你好，请介绍一下你自己",
                "max_length": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50
            }
        }

class BatchGenerateRequest(BaseModel):
    prompts: list[str]
    max_length: Optional[int] = None
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50

    class Config:
        schema_extra = {
            "example": {
                "prompts": ["你好，请介绍一下你自己", "今天天气怎么样？"],
                "max_length": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50
            }
        }

class DownloadModelRequest(BaseModel):
    model_name: str = "THUDM/chatglm3-6b"
    revision: str = "main"
    local_dir: Optional[str] = None

    class Config:
        schema_extra = {
            "example": {
                "model_name": "THUDM/chatglm3-6b",
                "revision": "main",
                "local_dir": None
            }
        }

@router.post("/generate", response_model=Dict[str, str])
async def generate_response(request: GenerateRequest) -> Dict[str, str]:
    """
    生成AI回复
    
    Args:
        request: 包含提示词和生成参数的请求体
        
    Returns:
        包含生成回复的字典
        
    Raises:
        HTTPException: 当模型未初始化或生成失败时
    """
    try:
        response = await model_handler.generate(
            prompt=request.prompt,
            max_length=request.max_length,
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k
        )
        return {"response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-generate", response_model=Dict[str, list[str]])
async def batch_generate_response(request: BatchGenerateRequest) -> Dict[str, list[str]]:
    """
    批量生成AI回复
    
    Args:
        request: 包含多个提示词和生成参数的请求体
        
    Returns:
        包含多个生成回复的字典
        
    Raises:
        HTTPException: 当模型未初始化或生成失败时
    """
    try:
        responses = await model_handler.batch_generate(
            prompts=request.prompts,
            max_length=request.max_length,
            temperature=request.temperature,
            top_p=request.top_p,
            top_k=request.top_k
        )
        return {"responses": responses}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status", response_model=Dict[str, Any])
async def get_model_status() -> Dict[str, Any]:
    """
    获取模型状态
    
    Returns:
        包含模型加载状态和下载状态的字典
    """
    download_status = model_downloader.get_download_status()
    load_status = model_handler.get_load_status()
    return {
        "download": download_status,
        "load": load_status
    }

@router.post("/download", response_model=Dict[str, Any])
async def download_model(request: DownloadModelRequest) -> Dict[str, Any]:
    """
    下载AI模型
    
    Args:
        request: 包含模型名称和版本信息的请求体
        
    Returns:
        下载结果信息
        
    Raises:
        HTTPException: 当下载失败时
    """
    result = await model_downloader.download_model(
        model_name=request.model_name,
        revision=request.revision,
        local_dir=request.local_dir
    )
    if result["status"] == "error":
        raise HTTPException(status_code=500, detail=result["error"])
    return result

@router.get("/available-models", response_model=Dict[str, Dict[str, str]])
async def list_available_models() -> Dict[str, Dict[str, str]]:
    """
    列出可用的模型
    
    Returns:
        可用模型列表，按类别分组
    """
    return model_downloader.list_available_models()

@router.post("/warmup")
async def warmup_model():
    """
    预热模型
    
    Returns:
        成功信息
        
    Raises:
        HTTPException: 当预热失败时
    """
    try:
        await model_handler.warmup()
        return {"status": "success", "message": "Model warmup completed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 