# 监控指南

## 性能监控

本项目使用 Prometheus + Grafana 进行性能监控，集成了完整的日志系统。

### 监控指标

#### 1. 请求指标
- `model_requests_total`: 请求总数
- `model_response_time_seconds`: 响应时间
- `model_memory_usage_bytes`: 内存使用
- `model_inference_batch_size`: 推理批次大小

#### 2. 模型性能指标
```python
from app.core.metrics import ModelMetrics

# 记录推理指标
metrics = ModelMetrics.log_inference_metrics(
    response_time=1.23,
    input_length=100,
    output_length=500,
    success=True
)

# 监控端点性能
@ModelMetrics.monitor_performance("chat")
async def chat_endpoint():
    # 处理逻辑
    pass

# 跟踪内存使用
ModelMetrics.track_memory_usage(1024 * 1024 * 100)  # 100MB
```

### Prometheus 配置

```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'chatglm'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

### Grafana 仪表板

1. 系统概览面板
```json
{
  "title": "ChatGLM System Overview",
  "panels": [
    {
      "title": "Request Rate",
      "type": "graph",
      "metrics": ["rate(model_requests_total[5m])"]
    },
    {
      "title": "Response Time",
      "type": "heatmap",
      "metrics": ["model_response_time_seconds"]
    }
  ]
}
```

2. 性能监控面板
```json
{
  "title": "Model Performance",
  "panels": [
    {
      "title": "Memory Usage",
      "type": "gauge",
      "metrics": ["model_memory_usage_bytes"]
    },
    {
      "title": "Batch Size Distribution",
      "type": "histogram",
      "metrics": ["model_inference_batch_size"]
    }
  ]
}
```

## 日志系统

### 日志配置

```python
from app.core.logging import setup_logger

# 创建日志记录器
training_logger = setup_logger('training', 'training.log')
test_logger = setup_logger('testing', 'test_data.log')
metrics_logger = setup_logger('metrics', 'logs/model_metrics.log')
```

### 日志级别

- DEBUG: 调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误

### 使用示例

```python
# 记录训练信息
training_logger.info("Starting training epoch %d", epoch)
training_logger.debug("Batch loss: %.4f", loss)

# 记录测试信息
test_logger.info("Model evaluation started")
test_logger.error("Error during model inference", exc_info=True)

# 记录性能指标
metrics_logger.info("Inference metrics: %s", metrics)
```

### 日志轮转

- 最大文件大小：10MB
- 备份文件数：5
- 编码：UTF-8

```python
# 日志轮转配置
file_handler = RotatingFileHandler(
    'app.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,
    encoding='utf-8'
)
```

## 告警配置

### Prometheus 告警规则

```yaml
# prometheus/rules/alert.rules
groups:
- name: chatglm_alerts
  rules:
  - alert: HighResponseTime
    expr: avg(model_response_time_seconds) > 1.0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      
  - alert: HighMemoryUsage
    expr: model_memory_usage_bytes > 1024*1024*1024*8  # 8GB
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage detected"
```

### AlertManager 配置

```yaml
# alertmanager/alertmanager.yml
route:
  group_by: ['alertname']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 1h
  receiver: 'team-email'

receivers:
- name: 'team-email'
  email_configs:
  - to: '<EMAIL>'
    from: '<EMAIL>'
    smarthost: 'smtp.example.com:587'
```

## 监控最佳实践

1. 定期检查
   - 每日查看性能仪表板
   - 分析响应时间趋势
   - 监控内存使用情况

2. 告警阈值设置
   - 响应时间 > 1s
   - 内存使用 > 80%
   - 错误率 > 1%

3. 日志分析
   - 使用ELK堆栈分析日志
   - 设置日志保留期限
   - 定期备份重要日志

4. 性能优化
   - 根据监控指标调整批处理大小
   - 优化内存使用
   - 调整缓存策略 