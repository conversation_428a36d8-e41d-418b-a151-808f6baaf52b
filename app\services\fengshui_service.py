"""
风水分析服务
提供专业的风水布局分析和建议
"""
import asyncio
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class FengshuiService:
    """风水分析服务类"""
    
    def __init__(self):
        # 八个方位的基本属性
        self.directions = {
            "north": {"element": "水", "color": ["蓝色", "黑色"], "number": [1, 6], "attribute": "智慧"},
            "northeast": {"element": "土", "color": ["黄色", "咖啡色"], "number": [2, 8], "attribute": "学习"},
            "east": {"element": "木", "color": ["绿色", "青色"], "number": [3, 4], "attribute": "健康"},
            "southeast": {"element": "木", "color": ["绿色", "紫色"], "number": [4, 9], "attribute": "财富"},
            "south": {"element": "火", "color": ["红色", "橙色"], "number": [9], "attribute": "名声"},
            "southwest": {"element": "土", "color": ["黄色", "粉色"], "number": [2, 5], "attribute": "感情"},
            "west": {"element": "金", "color": ["白色", "金色"], "number": [6, 7], "attribute": "子女"},
            "northwest": {"element": "金", "color": ["白色", "银色"], "number": [6, 1], "attribute": "贵人"}
        }
        
        # 房间类型与功能匹配
        self.room_functions = {
            "living_room": {"name": "客厅", "best_directions": ["south", "southeast"], "avoid": ["northeast"]},
            "bedroom": {"name": "卧室", "best_directions": ["southwest", "west"], "avoid": ["north"]},
            "kitchen": {"name": "厨房", "best_directions": ["east", "southeast"], "avoid": ["northwest"]},
            "bathroom": {"name": "卫生间", "best_directions": ["north", "northeast"], "avoid": ["south", "southeast"]},
            "study": {"name": "书房", "best_directions": ["northeast", "east"], "avoid": ["southwest"]},
            "dining_room": {"name": "餐厅", "best_directions": ["east", "south"], "avoid": ["west"]},
            "office": {"name": "办公室", "best_directions": ["northwest", "north"], "avoid": ["southwest"]}
        }
        
        # 五行相生相克关系
        self.five_elements = {
            "generation": {
                "水": "木", "木": "火", "火": "土", "土": "金", "金": "水"
            },
            "restriction": {
                "水": "火", "火": "金", "金": "木", "木": "土", "土": "水"
            }
        }
    
    async def analyze(self, house_type: str, direction: str, rooms: List[Dict], 
                     birth_info: Optional[Dict] = None) -> Dict[str, Any]:
        """
        执行风水分析
        
        Args:
            house_type: 房屋类型 (apartment/house/office)
            direction: 房屋朝向
            rooms: 房间布局信息
            birth_info: 主人生辰信息
            
        Returns:
            风水分析结果
        """
        try:
            logger.info(f"开始风水分析：{house_type} 朝向{direction}")
            
            # 模拟分析时间
            await asyncio.sleep(1.0)
            
            # 分析整体朝向
            direction_analysis = self._analyze_direction(direction)
            
            # 分析房间布局
            room_analysis = await self._analyze_rooms(rooms, direction)
            
            # 生成改善建议
            suggestions = await self._generate_suggestions(direction, rooms, birth_info)
            
            # 确定吉位方向
            lucky_directions = self._get_lucky_directions(direction, birth_info)
            
            # 推荐颜色和物品
            colors, items = self._get_recommendations(direction, birth_info)
            
            # 计算整体评分
            overall_score = self._calculate_overall_score(direction, room_analysis)
            
            result = {
                "overall_score": overall_score,
                "direction_analysis": direction_analysis,
                "room_analysis": room_analysis,
                "suggestions": suggestions,
                "lucky_directions": lucky_directions,
                "colors": colors,
                "items": items
            }
            
            logger.info(f"风水分析完成，整体评分：{overall_score}")
            return result
            
        except Exception as e:
            logger.error(f"风水分析失败：{e}")
            # 返回基础分析结果
            return await self._get_fallback_analysis(house_type, direction)
    
    def _analyze_direction(self, direction: str) -> Dict[str, Any]:
        """分析房屋朝向"""
        
        direction_info = self.directions.get(direction, self.directions["south"])
        
        # 朝向优劣分析
        direction_scores = {
            "south": {"score": 95, "desc": "坐北朝南，采光充足，是最理想的朝向"},
            "southeast": {"score": 90, "desc": "东南朝向，阳光充足，通风良好"},
            "east": {"score": 85, "desc": "坐西朝东，朝气蓬勃，适合事业发展"},
            "southwest": {"score": 80, "desc": "西南朝向，下午阳光较强，需注意遮阳"},
            "north": {"score": 70, "desc": "坐南朝北，采光较弱，但冬暖夏凉"},
            "northwest": {"score": 75, "desc": "西北朝向，贵人运强，但需注意保暖"},
            "west": {"score": 65, "desc": "坐东朝西，西晒较重，夏季炎热"},
            "northeast": {"score": 60, "desc": "东北朝向，阳光不足，湿气较重"}
        }
        
        direction_result = direction_scores.get(direction, {"score": 70, "desc": "朝向适中"})
        
        return {
            "direction": direction,
            "element": direction_info["element"],
            "score": direction_result["score"],
            "description": direction_result["desc"],
            "suitable_colors": direction_info["color"],
            "lucky_numbers": direction_info["number"],
            "main_attribute": direction_info["attribute"]
        }
    
    async def _analyze_rooms(self, rooms: List[Dict], house_direction: str) -> List[Dict]:
        """分析各房间风水"""
        
        analysis_results = []
        
        for room in rooms:
            room_type = room.get("type", "living_room")
            room_direction = room.get("direction", house_direction)
            room_size = room.get("size", "medium")
            
            # 获取房间功能信息
            room_info = self.room_functions.get(room_type, self.room_functions["living_room"])
            
            # 分析房间方位适配度
            direction_match = self._check_direction_match(room_type, room_direction)
            
            # 计算房间评分
            room_score = self._calculate_room_score(room_type, room_direction, room_size)
            
            # 生成具体建议
            room_suggestions = self._get_room_suggestions(room_type, room_direction)
            
            analysis_results.append({
                "room_type": room_type,
                "room_name": room_info["name"],
                "direction": room_direction,
                "score": room_score,
                "direction_match": direction_match,
                "suggestions": room_suggestions,
                "optimal_layout": self._get_optimal_layout(room_type)
            })
        
        return analysis_results
    
    def _check_direction_match(self, room_type: str, room_direction: str) -> Dict[str, Any]:
        """检查房间方位匹配度"""
        
        room_info = self.room_functions.get(room_type, self.room_functions["living_room"])
        
        if room_direction in room_info["best_directions"]:
            match_level = "最佳"
            match_score = 90
        elif room_direction in room_info["avoid"]:
            match_level = "不宜"
            match_score = 30
        else:
            match_level = "一般"
            match_score = 60
        
        return {
            "level": match_level,
            "score": match_score,
            "reason": f"{room_info['name']}在{room_direction}方位{match_level}"
        }
    
    def _calculate_room_score(self, room_type: str, room_direction: str, room_size: str) -> int:
        """计算房间风水评分"""
        
        # 基础分数
        base_score = 60
        
        # 方位匹配度调整
        direction_match = self._check_direction_match(room_type, room_direction)
        base_score += (direction_match["score"] - 60) * 0.4
        
        # 房间大小调整
        size_adjustments = {
            "small": -5,
            "medium": 0,
            "large": 5,
            "extra_large": 10
        }
        base_score += size_adjustments.get(room_size, 0)
        
        # 特殊房间类型调整
        if room_type == "bedroom":
            base_score += 5  # 卧室重要性加分
        elif room_type == "kitchen":
            base_score += 3  # 厨房重要性加分
        
        return max(10, min(100, int(base_score)))
    
    def _get_room_suggestions(self, room_type: str, room_direction: str) -> List[str]:
        """获取房间具体建议"""
        
        suggestions = []
        direction_info = self.directions.get(room_direction, self.directions["south"])
        
        # 基于房间类型的通用建议
        room_suggestions = {
            "living_room": [
                "客厅宜保持明亮整洁，是全家气场汇聚之所",
                "沙发背后要有靠山，不宜悬空",
                "茶几不宜过大，要与沙发保持适当距离"
            ],
            "bedroom": [
                "床头要靠实墙，不宜正对房门或镜子",
                "卧室宜温馨安静，避免摆放过多电器",
                "窗帘要能完全遮光，保证睡眠质量"
            ],
            "kitchen": [
                "炉灶不宜正对水槽，水火相冲",
                "厨房要保持通风良好，及时清理油烟",
                "刀具要妥善收纳，不宜外露"
            ],
            "bathroom": [
                "卫生间门要常关，避免秽气扩散",
                "保持干燥清洁，定期通风换气",
                "镜子不宜正对马桶或房门"
            ],
            "study": [
                "书桌要面向吉位，背后有靠",
                "书房宜安静明亮，避免嘈杂环境",
                "书籍摆放整齐，营造良好学习氛围"
            ]
        }
        
        # 获取房间类型建议
        suggestions.extend(room_suggestions.get(room_type, ["保持整洁有序，营造良好气场"]))
        
        # 基于方位的颜色建议
        suggestions.append(f"建议多使用{direction_info['color'][0]}、{direction_info['color'][1]}等颜色")
        
        # 基于五行的布局建议
        element = direction_info["element"]
        if element == "水":
            suggestions.append("可摆放鱼缸、水培植物等水元素装饰")
        elif element == "木":
            suggestions.append("适合摆放绿植，增强生命力")
        elif element == "火":
            suggestions.append("可用暖色调灯光，增强活力")
        elif element == "土":
            suggestions.append("适合用陶瓷、石材等土属性装饰")
        elif element == "金":
            suggestions.append("可用金属装饰品，增强贵气")
        
        return suggestions[:4]  # 最多返回4个建议
    
    def _get_optimal_layout(self, room_type: str) -> Dict[str, str]:
        """获取房间最佳布局建议"""
        
        layouts = {
            "living_room": {
                "沙发位置": "背靠实墙，面向入门",
                "茶几摆放": "居中偏向沙发，不阻挡行走",
                "电视位置": "避免正对窗户，减少反光",
                "植物摆放": "选择生命力强的绿植，放在明亮处"
            },
            "bedroom": {
                "床的位置": "床头靠墙，避免正对门窗",
                "衣柜摆放": "不宜正对床铺，保持空间流畅",
                "梳妆台": "镜子不要直接照床，可设置侧面",
                "照明设计": "主灯柔和，床头设置阅读灯"
            },
            "kitchen": {
                "炉灶位置": "背靠实墙，面向餐厅或客厅",
                "水槽布置": "与炉灶保持距离，避免水火相冲",
                "储物规划": "调料香料就近存放，保持台面整洁",
                "通风设计": "抽油烟机功率要足够，保持空气清新"
            },
            "study": {
                "书桌朝向": "面向文昌位，背后有靠",
                "书柜摆放": "书本分类整齐，便于查找",
                "座椅选择": "舒适稳固，支撑良好",
                "光线布置": "自然光充足，配备护眼台灯"
            }
        }
        
        return layouts.get(room_type, {
            "整体布局": "保持空间整洁有序",
            "物品摆放": "各归其位，便于使用",
            "空气流通": "保持良好通风",
            "光线充足": "确保采光明亮"
        })
    
    async def _generate_suggestions(self, direction: str, rooms: List[Dict], birth_info: Optional[Dict]) -> List[Dict]:
        """生成改善建议"""
        
        suggestions = []
        
        # 基于朝向的基础建议
        direction_suggestions = {
            "south": {
                "type": "布局优化",
                "priority": "高",
                "description": "充分利用南向采光优势",
                "actions": ["在南侧设置主要活动区域", "使用透光性好的窗帘", "摆放向阳植物"]
            },
            "north": {
                "type": "采光改善",
                "priority": "高", 
                "description": "加强室内照明和保温",
                "actions": ["增加暖色调灯光", "使用浅色装修材料", "加强墙体保温"]
            },
            "east": {
                "type": "健康布局",
                "priority": "中",
                "description": "利用东方生气提升健康运",
                "actions": ["在东侧摆放绿植", "设置晨练空间", "保持东向窗户清洁"]
            },
            "west": {
                "type": "遮阳降温",
                "priority": "中",
                "description": "减少西晒影响",
                "actions": ["安装遮阳设施", "使用隔热窗帘", "种植遮阳植物"]
            }
        }
        
        base_suggestion = direction_suggestions.get(direction, direction_suggestions["south"])
        suggestions.append(base_suggestion)
        
        # 基于房间分析的建议
        for room in rooms:
            room_type = room.get("type")
            if room_type in ["bedroom", "kitchen", "living_room"]:
                suggestions.append({
                    "type": f"{self.room_functions[room_type]['name']}优化",
                    "priority": "中",
                    "description": f"改善{self.room_functions[room_type]['name']}风水布局",
                    "actions": self._get_room_suggestions(room_type, room.get("direction", direction))[:2]
                })
        
        # 基于个人八字的建议（如果有生辰信息）
        if birth_info:
            personal_suggestion = self._get_personal_suggestions(birth_info, direction)
            if personal_suggestion:
                suggestions.append(personal_suggestion)
        
        # 通用风水改善建议
        suggestions.append({
            "type": "整体提升",
            "priority": "低",
            "description": "提升整体风水气场",
            "actions": [
                "定期清洁，保持空间整洁",
                "合理摆放风水植物",
                "避免尖角对冲",
                "保持空气流通"
            ]
        })
        
        return suggestions[:5]  # 最多返回5个建议
    
    def _get_personal_suggestions(self, birth_info: Dict, direction: str) -> Optional[Dict]:
        """基于个人八字生成建议"""
        
        if not birth_info.get("birth_year"):
            return None
        
        # 简化的个人五行分析
        year = birth_info["birth_year"]
        year_element = self._get_year_element(year)
        
        return {
            "type": "个人定制",
            "priority": "高",
            "description": f"基于您的{year_element}年生人特质优化",
            "actions": [
                f"增强{year_element}元素的装饰",
                f"避免与{self.five_elements['restriction'].get(year_element, '火')}相冲的布局",
                "在吉位摆放个人物品"
            ]
        }
    
    def _get_year_element(self, year: int) -> str:
        """根据出生年份获取年柱五行"""
        # 简化版天干地支五行对照
        elements_cycle = ["金", "水", "木", "火", "土"]
        return elements_cycle[year % 5]
    
    def _get_lucky_directions(self, direction: str, birth_info: Optional[Dict]) -> List[str]:
        """确定吉位方向"""
        
        # 基于房屋朝向的基础吉位
        base_lucky = {
            "south": ["southeast", "east", "northeast"],
            "north": ["northwest", "west", "southwest"], 
            "east": ["south", "southeast", "northeast"],
            "west": ["north", "northwest", "southwest"],
            "southeast": ["south", "east", "northeast"],
            "northeast": ["east", "southeast", "south"],
            "southwest": ["west", "northwest", "north"],
            "northwest": ["north", "west", "southwest"]
        }
        
        lucky_dirs = base_lucky.get(direction, ["south", "east", "southeast"])
        
        # 如果有个人信息，可以添加个人吉位
        if birth_info and birth_info.get("birth_year"):
            year = birth_info["birth_year"]
            personal_lucky = self._get_personal_lucky_direction(year)
            if personal_lucky and personal_lucky not in lucky_dirs:
                lucky_dirs.insert(0, personal_lucky)
        
        return lucky_dirs[:3]  # 返回前3个吉位
    
    def _get_personal_lucky_direction(self, year: int) -> str:
        """根据出生年份获取个人吉位"""
        # 简化版个人吉位计算
        directions_list = ["north", "northeast", "east", "southeast", "south", "southwest", "west", "northwest"]
        return directions_list[year % 8]
    
    def _get_recommendations(self, direction: str, birth_info: Optional[Dict]) -> tuple:
        """获取颜色和物品推荐"""
        
        direction_info = self.directions.get(direction, self.directions["south"])
        
        # 基础推荐颜色
        colors = direction_info["color"].copy()
        
        # 基础推荐物品
        items = []
        element = direction_info["element"]
        
        element_items = {
            "水": ["水晶球", "鱼缸", "流水摆件", "蓝色装饰"],
            "木": ["绿色植物", "木质家具", "竹制品", "花卉"],
            "火": ["红色装饰", "蜡烛", "暖色灯具", "向日葵"],
            "土": ["陶瓷摆件", "黄色水晶", "石头装饰", "方形物品"],
            "金": ["金属摆件", "白色装饰", "圆形物品", "铜制品"]
        }
        
        items.extend(element_items.get(element, ["装饰品", "植物"]))
        
        # 如果有个人信息，添加个人化推荐
        if birth_info and birth_info.get("birth_year"):
            year_element = self._get_year_element(birth_info["birth_year"])
            if year_element != element:
                # 添加个人五行对应的颜色和物品
                personal_colors = {
                    "金": ["白色", "银色"],
                    "木": ["绿色", "青色"], 
                    "水": ["蓝色", "黑色"],
                    "火": ["红色", "紫色"],
                    "土": ["黄色", "咖啡色"]
                }
                colors.extend(personal_colors.get(year_element, []))
                items.extend(element_items.get(year_element, []))
        
        # 去重
        colors = list(dict.fromkeys(colors))[:4]  # 最多4种颜色
        items = list(dict.fromkeys(items))[:6]    # 最多6种物品
        
        return colors, items
    
    def _calculate_overall_score(self, direction: str, room_analysis: List[Dict]) -> int:
        """计算整体风水评分"""
        
        # 朝向基础分数
        direction_scores = {
            "south": 85, "southeast": 80, "east": 75, "southwest": 70,
            "north": 65, "northwest": 70, "west": 60, "northeast": 55
        }
        
        base_score = direction_scores.get(direction, 65)
        
        # 房间平均分
        if room_analysis:
            room_scores = [room["score"] for room in room_analysis]
            avg_room_score = sum(room_scores) / len(room_scores)
            
            # 综合计算（朝向占40%，房间占60%）
            overall_score = int(base_score * 0.4 + avg_room_score * 0.6)
        else:
            overall_score = base_score
        
        # 确保分数在合理范围
        return max(30, min(100, overall_score))
    
    async def _get_fallback_analysis(self, house_type: str, direction: str) -> Dict[str, Any]:
        """获取备用分析结果"""
        
        return {
            "overall_score": 70,
            "direction_analysis": {
                "direction": direction,
                "element": self.directions.get(direction, self.directions["south"])["element"],
                "score": 70,
                "description": "整体风水布局尚可，有改善空间",
                "suitable_colors": ["红色", "黄色"],
                "lucky_numbers": [6, 8],
                "main_attribute": "平衡"
            },
            "room_analysis": [
                {
                    "room_type": "living_room",
                    "room_name": "客厅",
                    "direction": direction,
                    "score": 75,
                    "direction_match": {"level": "一般", "score": 60, "reason": "位置适中"},
                    "suggestions": ["保持明亮整洁", "合理摆放家具"],
                    "optimal_layout": {"整体布局": "保持空间整洁有序"}
                }
            ],
            "suggestions": [
                {
                    "type": "基础改善",
                    "priority": "中",
                    "description": "改善基础风水布局",
                    "actions": ["保持清洁", "增加绿植", "改善照明"]
                }
            ],
            "lucky_directions": ["south", "east", "southeast"],
            "colors": ["红色", "黄色", "绿色"],
            "items": ["绿色植物", "水晶摆件", "温暖照明"]
        }