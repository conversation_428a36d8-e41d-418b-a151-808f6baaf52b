# ChatGLM Knowledge Base QA System

基于 ChatGLM-6B 和 Haystack 的智能问答系统

## 项目概述

本项目是一个基于 ChatGLM-6B 大规模语言模型的智能问答系统，集成了知识库检索、实时问答、性能监控和用户反馈等功能。

### 主要特性

- 基于 ChatGLM-6B 的智能问答
- 知识库检索和管理
- 实时性能监控
- 用户反馈收集
- 完整的日志系统
- Docker 容器化部署
- Prometheus + Grafana 监控

### 技术栈

- 后端框架：FastAPI
- 模型：ChatGLM-6B
- 检索框架：Haystack
- 向量存储：FAISS
- 监控：Prometheus + Grafana
- 数据库：MySQL + Redis
- 容器化：Docker

## 硬件要求

运行此AI系统需要较高的硬件配置，特别是对于模型推理和训练：
- **CPU**：至少8核
- **RAM**：至少32GB
- **GPU**：建议使用具有至少16GB VRAM的GPU（如NVIDIA RTX 3090或A100）以加速模型推理
- **磁盘空间**：至少50GB空闲空间用于存储模型文件和数据

如果您的硬件不满足这些要求，系统可能无法正常运行或性能会受到严重影响。

## 快速开始

### 环境要求

- Python 3.8+
- CUDA 11.6+ (GPU 版本)
- 16GB+ RAM
- 50GB+ 磁盘空间

### 安装

1. 克隆项目：
```bash
git clone [项目地址]
cd chatglm-qa-system
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 启动服务：
```bash
# 开发环境
./start-dev.bat  # Windows
./start-dev.ps1  # PowerShell
./run.sh         # Linux/Mac

# 生产环境
docker-compose up -d
```

### 目录结构

```
chatbot_project/
├── app/                    # 应用主目录
│   ├── api/               # API 接口
│   ├── core/              # 核心功能
│   └── models/            # 模型定义
├── config/                # 配置文件
├── data/                  # 数据文件
├── docs/                  # 文档
├── logs/                  # 日志文件
├── tests/                 # 测试文件
├── docker/                # Docker 配置
├── scripts/              # 脚本文件
└── alembic/              # 数据库迁移
```

## 文档索引

- [API文档](./api.md)
- [部署指南](./deployment.md)
- [开发指南](./development.md)
- [监控指南](./monitoring.md)
- [API示例](./examples.md)

## 许可证

[许可证类型]

## 贡献指南

请查看 [CONTRIBUTING.md](./CONTRIBUTING.md) 了解如何参与项目开发。 