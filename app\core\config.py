"""
AI占卜项目配置文件

该文件包含项目的所有配置参数，包括：
1. 数据库配置
2. 安全配置  
3. 模型配置
4. 缓存配置
5. 监控配置
6. 日志配置
7. 服务配置
"""

from typing import Dict, List, Optional, Any, Union
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator, Field
import os
from pathlib import Path
import json
import logging
from logging.handlers import RotatingFileHandler
import torch
from dotenv import load_dotenv
import secrets

# 加载环境变量
load_dotenv()

class DatabaseSettings(BaseSettings):
    """数据库配置类"""
    
    # PostgreSQL配置
    POSTGRES_SERVER: str = Field(
        default=os.getenv("POSTGRES_SERVER", "localhost"),
        description="PostgreSQL服务器地址"
    )
    POSTGRES_USER: str = Field(
        default=os.getenv("POSTGRES_USER", "postgres"),
        description="PostgreSQL用户名"
    )
    POSTGRES_PASSWORD: str = Field(
        default=os.getenv("POSTGRES_PASSWORD", "postgres"),
        description="PostgreSQL密码"
    )
    POSTGRES_DB: str = Field(
        default=os.getenv("POSTGRES_DB", "fortune"),
        description="PostgreSQL数据库名"
    )
    POSTGRES_PORT: int = Field(
        default=int(os.getenv("POSTGRES_PORT", "5432")),
        description="PostgreSQL端口"
    )
    
    # Redis配置
    REDIS_HOST: str = Field(
        default=os.getenv("REDIS_HOST", "localhost"),
        description="Redis服务器地址"
    )
    REDIS_PORT: int = Field(
        default=int(os.getenv("REDIS_PORT", "6379")),
        description="Redis端口"
    )
    REDIS_DB: int = Field(
        default=int(os.getenv("REDIS_DB", "0")),
        description="Redis数据库编号"
    )
    REDIS_PASSWORD: str = Field(
        default=os.getenv("REDIS_PASSWORD", ""),
        description="Redis密码"
    )

    @property
    def database_uri(self) -> str:
        """构建数据库连接字符串"""
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    @property
    def redis_uri(self) -> str:
        """构建Redis连接字符串"""
        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

class SecuritySettings(BaseSettings):
    """安全配置类"""
    
    SECRET_KEY: str = Field(
        default=os.getenv("SECRET_KEY", secrets.token_urlsafe(32)),
        description="应用程序密钥"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "11520")),
        description="访问令牌过期时间（分钟）"
    )
    ALGORITHM: str = Field(
        default="HS256",
        description="JWT算法"
    )
    
    # CORS设置
    ALLOWED_ORIGINS: List[str] = Field(
        default=["*"],
        description="允许的跨域源"
    )
    ALLOWED_METHODS: List[str] = Field(
        default=["*"],
        description="允许的HTTP方法"
    )
    ALLOWED_HEADERS: List[str] = Field(
        default=["*"],
        description="允许的HTTP头"
    )
    
    # 安全响应头
    SECURITY_HEADERS: Dict[str, str] = Field(
        default={
            "X-Frame-Options": "DENY",
            "X-Content-Type-Options": "nosniff",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline';"
            ),
            "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        description="安全响应头配置"
    )

class ModelSettings(BaseSettings):
    """模型配置类"""
    
    MODEL_PATH: str = Field(
        default=os.getenv("MODEL_PATH", str(Path(__file__).parent.parent.parent / "models" / "chatglm-6b")),
        description="模型文件路径"
    )
    MODEL_DEVICE: str = Field(
        default=os.getenv("MODEL_DEVICE", "cpu"),
        description="模型运行设备"
    )
    MODEL_PRECISION: str = Field(
        default=os.getenv("MODEL_PRECISION", "fp32"),
        description="模型精度"
    )
    
    # 生成参数
    MAX_LENGTH: int = Field(default=2048, description="最大生成长度")
    TOP_P: float = Field(default=0.7, ge=0.0, le=1.0, description="Top-p采样参数")
    TEMPERATURE: float = Field(default=0.95, ge=0.0, le=2.0, description="生成温度")
    
    # 优化配置
    OPTIMIZATION: Dict[str, Any] = Field(
        default={
            "quantization": {
                "type": "dynamic",
                "bits": 4,
                "enabled": True,
                "compute_dtype": "float16",
                "quant_type": "nf4"
            },
            "pruning": {
                "method": "magnitude",
                "sparsity": 0.3,
                "enabled": True,
                "schedule": "cubic",
                "n_iterations": 100
            },
            "knowledge_distillation": {
                "enabled": True,
                "temperature": 2.0,
                "alpha": 0.5,
                "teacher_model": "THUDM/chatglm2-6b",
                "student_model": "THUDM/chatglm-6b"
            }
        },
        description="模型优化配置"
    )

    @validator("MODEL_DEVICE")
    def validate_model_device(cls, v: str) -> str:
        """验证模型设备配置"""
        if v == "cuda" and not torch.cuda.is_available():
            logging.warning("CUDA不可用，自动切换到CPU")
            return "cpu"
        return v

    @validator("MODEL_PRECISION")
    def validate_model_precision(cls, v: str, values: dict) -> str:
        """验证模型精度配置"""
        if values.get("MODEL_DEVICE") == "cpu" and v == "fp16":
            logging.warning("CPU不支持fp16，自动切换到fp32")
            return "fp32"
        return v

class CacheSettings(BaseSettings):
    """缓存配置类"""
    
    CACHE_TTL: int = Field(default=3600, description="缓存默认TTL（秒）")
    CACHE_DIR: Path = Field(
        default=Path(__file__).parent.parent.parent / ".cache",
        description="缓存目录"
    )
    
    # 模型缓存配置
    MODEL_CACHE_SIZE: str = Field(default="20GB", description="模型缓存大小")
    KV_CACHE_SIZE: str = Field(default="10GB", description="KV缓存大小")
    GRADIENT_CHECKPOINTING: bool = Field(default=True, description="梯度检查点")
    
    # 批处理优化
    ADAPTIVE_BATCH_SIZING: Dict[str, Any] = Field(
        default={
            "enabled": True,
            "min_batch_size": 1,
            "max_batch_size": 128,
            "target_memory_usage": 0.85,
            "growth_factor": 1.5,
            "shrink_factor": 0.5
        },
        description="自适应批处理配置"
    )

class MonitoringSettings(BaseSettings):
    """监控配置类"""
    
    ENABLE_METRICS: bool = Field(
        default=os.getenv("ENABLE_METRICS", "true").lower() == "true",
        description="启用指标监控"
    )
    PROMETHEUS_PORT: int = Field(
        default=int(os.getenv("PROMETHEUS_PORT", "9090")),
        description="Prometheus端口"
    )
    
    # 性能阈值
    PERFORMANCE_THRESHOLDS: Dict[str, float] = Field(
        default={
            "max_latency": 2.0,
            "min_throughput": 10,
            "max_memory_usage": 0.9,
            "max_error_rate": 0.01
        },
        description="性能监控阈值"
    )
    
    # 告警配置
    ALERT_THRESHOLDS: Dict[str, float] = Field(
        default={
            "error_rate": 0.1,
            "latency_p95": 2.0,
            "memory_usage": 0.9
        },
        description="告警阈值配置"
    )

class LoggingSettings(BaseSettings):
    """日志配置类"""
    
    LOG_LEVEL: str = Field(
        default=os.getenv("LOG_LEVEL", "INFO"),
        description="日志级别"
    )
    LOG_FORMAT: str = Field(
        default='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        description="日志格式"
    )
    LOG_FILE: str = Field(
        default="logs/app.log",
        description="日志文件路径"
    )
    MAX_LOG_SIZE: int = Field(
        default=10485760,  # 10MB
        description="单个日志文件最大大小"
    )
    BACKUP_COUNT: int = Field(
        default=5,
        description="日志备份文件数量"
    )

class Settings(BaseSettings):
    """
    项目主配置类
    
    整合所有子配置类，提供统一的配置管理接口
    """
    
    # 项目基础设置
    PROJECT_NAME: str = Field(default="AI Fortune Telling System", description="项目名称")
    VERSION: str = Field(default="1.0.0", description="项目版本")
    API_V1_STR: str = Field(default="/api/v1", description="API版本前缀")
    DEBUG: bool = Field(default=False, description="调试模式")
    
    # 服务器设置
    HOST: str = Field(default="0.0.0.0", description="服务器主机")
    PORT: int = Field(default=8000, description="服务器端口")
    RELOAD: bool = Field(default=True, description="自动重载")
    
    # 文件路径
    BASE_DIR: Path = Field(
        default=Path(__file__).resolve().parent.parent.parent,
        description="项目根目录"
    )
    
    # 各子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    model: ModelSettings = Field(default_factory=ModelSettings)
    cache: CacheSettings = Field(default_factory=CacheSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        arbitrary_types_allowed = True

    def __init__(self, **kwargs):
        """初始化配置并创建必要目录"""
        super().__init__(**kwargs)
        self._create_directories()
        self._setup_logging()

    def _create_directories(self) -> None:
        """创建必要的目录"""
        directories = [
            self.BASE_DIR / "logs",
            self.BASE_DIR / "data",
            self.BASE_DIR / "models",
            self.cache.CACHE_DIR,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def _setup_logging(self) -> None:
        """设置日志系统"""
        log_dir = self.BASE_DIR / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置根日志记录器
        logging.basicConfig(
            level=getattr(logging, self.logging.LOG_LEVEL),
            format=self.logging.LOG_FORMAT,
            handlers=[
                logging.StreamHandler(),
                RotatingFileHandler(
                    self.logging.LOG_FILE,
                    maxBytes=self.logging.MAX_LOG_SIZE,
                    backupCount=self.logging.BACKUP_COUNT
                )
            ]
        )

    def get_database_uri(self) -> str:
        """获取数据库连接字符串"""
        return self.database.database_uri

    def get_redis_uri(self) -> str:
        """获取Redis连接字符串"""
        return self.database.redis_uri

    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return not self.DEBUG

    def get_model_config(self) -> Dict[str, Any]:
        """获取模型配置"""
        return {
            "model_path": self.model.MODEL_PATH,
            "device": self.model.MODEL_DEVICE,
            "precision": self.model.MODEL_PRECISION,
            "max_length": self.model.MAX_LENGTH,
            "top_p": self.model.TOP_P,
            "temperature": self.model.TEMPERATURE,
            "optimization": self.model.OPTIMIZATION
        }

# 创建全局配置实例
settings = Settings()

# 向后兼容的配置导出
DATABASE_URI = settings.get_database_uri()
REDIS_URI = settings.get_redis_uri()
MODEL_CONFIG = settings.get_model_config()

# ... existing code ...