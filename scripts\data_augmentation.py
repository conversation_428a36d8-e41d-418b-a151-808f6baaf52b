#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据增强脚本 - 改善训练数据质量和多样性
"""

import json
import random
import re
from typing import List, Dict, Tuple
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAugmentation:
    """数据增强类"""
    
    def __init__(self):
        self.fortune_templates = {
            "八字分析": [
                "根据您的八字{bazi}，我为您分析如下：{analysis}",
                "从您的生辰八字{bazi}来看，{analysis}",
                "您的八字{bazi}显示，{analysis}"
            ],
            "塔罗占卜": [
                "您抽到的是{card}，这张牌表示{meaning}",
                "塔罗牌{card}为您指引：{meaning}",
                "{card}这张牌告诉我们：{meaning}"
            ],
            "星座运势": [
                "作为{constellation}座的您，{fortune}",
                "{constellation}座本周运势：{fortune}",
                "对于{constellation}座来说，{fortune}"
            ],
            "风水建议": [
                "从风水角度来看，{suggestion}",
                "建议您在风水布局上{suggestion}",
                "风水学认为{suggestion}"
            ]
        }
        
        self.response_patterns = {
            "积极": ["运势不错", "前景光明", "机遇很多", "发展顺利"],
            "中性": ["需要注意", "保持谨慎", "稳中求进", "循序渐进"],
            "建议": ["建议您", "可以考虑", "不妨尝试", "值得关注"]
        }
    
    def load_data(self, file_path: str) -> List[Dict]:
        """加载训练数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"加载了 {len(data)} 条数据")
            return data
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return []
    
    def clean_data(self, data: List[Dict]) -> List[Dict]:
        """清理数据，移除低质量样本"""
        cleaned_data = []
        
        for item in data:
            if not isinstance(item, dict) or 'input' not in item or 'response' not in item:
                continue
                
            input_text = str(item['input']).strip()
            response_text = str(item['response']).strip()
            
            # 过滤条件
            if (len(input_text) < 2 or len(response_text) < 2 or
                len(input_text) > 500 or len(response_text) > 1000 or
                input_text == response_text or
                self._is_low_quality(input_text, response_text)):
                continue
                
            cleaned_data.append({
                'input': input_text,
                'response': response_text
            })
        
        logger.info(f"清理后保留 {len(cleaned_data)} 条高质量数据")
        return cleaned_data
    
    def _is_low_quality(self, input_text: str, response_text: str) -> bool:
        """判断是否为低质量数据"""
        # 检查是否只是简单的名字替换
        if len(input_text) <= 10 and len(response_text) <= 10:
            return True
            
        # 检查是否包含无意义的字符
        if re.search(r'[^\u4e00-\u9fff\w\s\.,!?，。！？]', input_text + response_text):
            return True
            
        return False
    
    def generate_fortune_data(self, count: int = 100) -> List[Dict]:
        """生成命理相关的训练数据"""
        generated_data = []
        
        # 八字分析数据
        for _ in range(count // 4):
            bazi_elements = ["甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳"]
            bazi = random.choice(bazi_elements)
            analysis = random.choice([
                "您的五行平衡，性格温和，适合从事文职工作",
                "木旺需要金来修剪，建议佩戴金属饰品",
                "财运较旺，但需要注意理财规划"
            ])
            
            template = random.choice(self.fortune_templates["八字分析"])
            response = template.format(bazi=bazi, analysis=analysis)
            
            generated_data.append({
                "input": f"请分析我的八字{bazi}",
                "response": response
            })
        
        # 塔罗占卜数据
        tarot_cards = ["愚者", "魔术师", "女祭司", "皇后", "皇帝", "教皇", "恋人", "战车"]
        for _ in range(count // 4):
            card = random.choice(tarot_cards)
            meaning = random.choice([
                "新的开始即将到来，保持开放的心态",
                "需要更多的耐心和智慧来处理当前的问题",
                "爱情运势上升，单身者有望遇到心仪对象"
            ])
            
            template = random.choice(self.fortune_templates["塔罗占卜"])
            response = template.format(card=card, meaning=meaning)
            
            generated_data.append({
                "input": f"请为我解读{card}这张塔罗牌",
                "response": response
            })
        
        # 星座运势数据
        constellations = ["白羊", "金牛", "双子", "巨蟹", "狮子", "处女", "天秤", "天蝎", "射手", "摩羯", "水瓶", "双鱼"]
        for _ in range(count // 4):
            constellation = random.choice(constellations)
            fortune = random.choice([
                "本周工作运势不错，有机会获得上司认可",
                "感情方面需要多沟通，避免误会",
                "财运平稳，适合进行稳健投资"
            ])
            
            template = random.choice(self.fortune_templates["星座运势"])
            response = template.format(constellation=constellation, fortune=fortune)
            
            generated_data.append({
                "input": f"{constellation}座本周运势如何",
                "response": response
            })
        
        # 风水建议数据
        for _ in range(count // 4):
            suggestion = random.choice([
                "在客厅摆放绿色植物有助于提升家庭和谐",
                "卧室床头不宜对着镜子，影响睡眠质量",
                "办公桌上放置水晶球可以增强事业运"
            ])
            
            template = random.choice(self.fortune_templates["风水建议"])
            response = template.format(suggestion=suggestion)
            
            generated_data.append({
                "input": "请给我一些风水建议",
                "response": response
            })
        
        logger.info(f"生成了 {len(generated_data)} 条命理数据")
        return generated_data
    
    def augment_existing_data(self, data: List[Dict]) -> List[Dict]:
        """对现有数据进行增强"""
        augmented_data = []
        
        for item in data:
            original_input = item['input']
            original_response = item['response']
            
            # 保留原始数据
            augmented_data.append(item)
            
            # 生成同义词替换版本
            if len(original_input) > 10:  # 只对较长的输入进行增强
                synonyms = {
                    "分析": ["解读", "解析", "研究"],
                    "运势": ["运程", "运气", "命运"],
                    "建议": ["意见", "推荐", "指导"],
                    "如何": ["怎么", "怎样", "如何才能"]
                }
                
                augmented_input = original_input
                for word, syns in synonyms.items():
                    if word in augmented_input:
                        augmented_input = augmented_input.replace(word, random.choice(syns))
                
                if augmented_input != original_input:
                    augmented_data.append({
                        'input': augmented_input,
                        'response': original_response
                    })
        
        logger.info(f"数据增强后共有 {len(augmented_data)} 条数据")
        return augmented_data
    
    def save_data(self, data: List[Dict], file_path: str):
        """保存数据到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到 {file_path}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")

def main():
    """主函数"""
    augmenter = DataAugmentation()
    
    # 加载原始数据
    original_data = augmenter.load_data("data/train.json")
    
    # 清理数据
    cleaned_data = augmenter.clean_data(original_data)
    
    # 生成新的命理数据
    generated_data = augmenter.generate_fortune_data(200)
    
    # 增强现有数据
    augmented_data = augmenter.augment_existing_data(cleaned_data)
    
    # 合并所有数据
    final_data = augmented_data + generated_data
    
    # 随机打乱数据
    random.shuffle(final_data)
    
    # 保存增强后的数据
    augmenter.save_data(final_data, "data/train_augmented.json")
    
    # 创建验证集
    eval_size = min(len(final_data) // 10, 100)  # 10%作为验证集，最多100条
    eval_data = final_data[:eval_size]
    train_data = final_data[eval_size:]
    
    augmenter.save_data(train_data, "data/train_final.json")
    augmenter.save_data(eval_data, "data/eval_final.json")
    
    logger.info(f"最终训练集: {len(train_data)} 条")
    logger.info(f"最终验证集: {len(eval_data)} 条")

if __name__ == "__main__":
    main()
