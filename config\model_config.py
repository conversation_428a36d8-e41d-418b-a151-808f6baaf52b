from dataclasses import dataclass, field
from typing import Optional, List

def get_default_metrics() -> List[str]:
    return ["accuracy", "f1", "precision", "recall"]

@dataclass
class ModelConfig:
    # Model Architecture
    model_name: str = "microsoft/DialoGPT-medium"  # 更适合对话的基础模型
    model_type: str = "causal_lm"  # 模型类型：causal_lm, seq2seq, classification
    max_length: int = 1024  # 增加最大序列长度以支持更长对话
    vocab_size: int = 50257  # 词表大小
    hidden_size: int = 1024  # 增加隐藏层大小
    num_attention_heads: int = 16  # 增加注意力头数
    num_hidden_layers: int = 24  # 增加隐藏层数量

    # Training Parameters
    batch_size: int = 4  # 减小批次大小以适应更大模型
    learning_rate: float = 5e-5  # 调整学习率
    weight_decay: float = 0.01  # 权重衰减
    max_epochs: int = 20  # 增加训练轮数
    warmup_steps: int = 1000  # 增加预热步数
    gradient_accumulation_steps: int = 4  # 增加梯度累积步数
    max_grad_norm: float = 1.0  # 梯度裁剪

    # Advanced Training Parameters
    lr_scheduler_type: str = "cosine"  # 学习率调度器类型
    save_strategy: str = "steps"  # 保存策略
    evaluation_strategy: str = "steps"  # 评估策略
    eval_steps: int = 500  # 评估步数
    save_steps: int = 1000  # 保存步数
    load_best_model_at_end: bool = True  # 训练结束时加载最佳模型
    metric_for_best_model: str = "eval_loss"  # 最佳模型的评估指标
    greater_is_better: bool = False  # 指标越小越好
    
    # Data Processing
    train_data_path: str = "data/train.json"  # 训练数据路径
    eval_data_path: str = "data/eval.json"  # 验证数据路径
    test_data_path: str = "data/test.json"  # 测试数据路径
    preprocessing_num_workers: int = 4  # 数据预处理的工作进程数
    
    # Optimization
    fp16: bool = True  # 是否使用混合精度训练
    use_8bit_quantization: bool = False  # 是否使用8位量化
    
    # Logging and Saving
    logging_steps: int = 100  # 日志记录步数
    save_steps: int = 1000  # 模型保存步数
    eval_steps: int = 500  # 评估步数
    save_total_limit: int = 3  # 保存的检查点数量限制
    output_dir: str = "models/ai/chatbot/checkpoints"  # 输出目录
    
    # Evaluation Metrics
    metrics: List[str] = field(default_factory=get_default_metrics)  # 评估指标
    
    # Early Stopping
    early_stopping_patience: int = 3  # 早停耐心值
    early_stopping_threshold: float = 0.01  # 早停阈值

@dataclass
class InferenceConfig:
    # Inference Parameters
    temperature: float = 0.7  # 生成温度
    top_p: float = 0.9  # 核采样参数
    top_k: int = 50  # top-k采样参数
    num_beams: int = 4  # 束搜索束宽
    max_new_tokens: int = 100  # 最大生成长度
    repetition_penalty: float = 1.2  # 重复惩罚因子
    
    # Hardware
    device: str = "cuda"  # 推理设备
    batch_size: int = 1  # 推理批次大小 