from dataclasses import dataclass, field
from typing import Optional, List

def get_default_metrics() -> List[str]:
    return ["accuracy", "f1", "precision", "recall"]

@dataclass
class ModelConfig:
    # Model Architecture
    model_name: str = "gpt2"  # 基础模型名称
    model_type: str = "causal_lm"  # 模型类型：causal_lm, seq2seq, classification
    max_length: int = 512  # 最大序列长度
    vocab_size: int = 50257  # 词表大小
    hidden_size: int = 768  # 隐藏层大小
    num_attention_heads: int = 12  # 注意力头数
    num_hidden_layers: int = 12  # 隐藏层数量
    
    # Training Parameters
    batch_size: int = 8  # 批次大小
    learning_rate: float = 2e-5  # 学习率
    weight_decay: float = 0.01  # 权重衰减
    max_epochs: int = 10  # 最大训练轮数
    warmup_steps: int = 500  # 预热步数
    gradient_accumulation_steps: int = 1  # 梯度累积步数
    max_grad_norm: float = 1.0  # 梯度裁剪
    
    # Data Processing
    train_data_path: str = "data/train.json"  # 训练数据路径
    eval_data_path: str = "data/eval.json"  # 验证数据路径
    test_data_path: str = "data/test.json"  # 测试数据路径
    preprocessing_num_workers: int = 4  # 数据预处理的工作进程数
    
    # Optimization
    fp16: bool = True  # 是否使用混合精度训练
    use_8bit_quantization: bool = False  # 是否使用8位量化
    
    # Logging and Saving
    logging_steps: int = 100  # 日志记录步数
    save_steps: int = 1000  # 模型保存步数
    eval_steps: int = 500  # 评估步数
    save_total_limit: int = 3  # 保存的检查点数量限制
    output_dir: str = "models/ai/chatbot/checkpoints"  # 输出目录
    
    # Evaluation Metrics
    metrics: List[str] = field(default_factory=get_default_metrics)  # 评估指标
    
    # Early Stopping
    early_stopping_patience: int = 3  # 早停耐心值
    early_stopping_threshold: float = 0.01  # 早停阈值

@dataclass
class InferenceConfig:
    # Inference Parameters
    temperature: float = 0.7  # 生成温度
    top_p: float = 0.9  # 核采样参数
    top_k: int = 50  # top-k采样参数
    num_beams: int = 4  # 束搜索束宽
    max_new_tokens: int = 100  # 最大生成长度
    repetition_penalty: float = 1.2  # 重复惩罚因子
    
    # Hardware
    device: str = "cuda"  # 推理设备
    batch_size: int = 1  # 推理批次大小 