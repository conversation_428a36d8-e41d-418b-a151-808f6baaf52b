from typing import Dict, List, Any, Optional, Callable
import yaml
import json
from datetime import datetime
import logging
from pathlib import Path
from pydantic import BaseModel
import re

class Rule(BaseModel):
    """规则模型"""
    id: str
    type: str
    condition: Dict[str, Any]
    action: Dict[str, Any]
    priority: int
    description: str
    enabled: bool = True
    metadata: Dict[str, Any] = {}

class RuleEngine:
    """算命规则引擎"""
    
    def __init__(self, rules_dir: str = "config/rules"):
        """
        初始化规则引擎
        
        Args:
            rules_dir: 规则配置目录
        """
        self.rules_dir = Path(rules_dir)
        self.rules: Dict[str, Rule] = {}
        self.logger = logging.getLogger(__name__)
        self._load_rules()
        
        # 注册内置操作
        self.actions = {
            "validate_terminology": self._validate_terminology,
            "check_compatibility": self._check_compatibility,
            "apply_fortune_logic": self._apply_fortune_logic,
            "format_response": self._format_response
        }
    
    def _load_rules(self):
        """加载规则配置"""
        if not self.rules_dir.exists():
            self.logger.warning(f"Rules directory {self.rules_dir} does not exist")
            return
            
        for file in self.rules_dir.glob("*.yaml"):
            try:
                with open(file, "r", encoding="utf-8") as f:
                    rules_data = yaml.safe_load(f)
                    for rule_data in rules_data.get("rules", []):
                        rule = Rule(**rule_data)
                        self.rules[rule.id] = rule
            except Exception as e:
                self.logger.error(f"Failed to load rules from {file}: {e}")
    
    def _validate_terminology(self, content: str, context: Dict[str, Any]) -> bool:
        """
        验证专业术语使用
        
        Args:
            content: 待验证内容
            context: 上下文信息
            
        Returns:
            bool: 是否验证通过
        """
        # 加载术语表
        terms_file = self.rules_dir / "terminology.json"
        if not terms_file.exists():
            return True
            
        with open(terms_file, "r", encoding="utf-8") as f:
            terms = json.load(f)
            
        # 检查术语使用
        for term in terms:
            if term["name"] in content:
                # 检查上下文是否适合使用该术语
                if not self._check_term_context(term, context):
                    return False
        return True
    
    def _check_term_context(self, term: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """
        检查术语使用上下文是否合适
        
        Args:
            term: 术语信息
            context: 上下文信息
            
        Returns:
            bool: 是否适合使用
        """
        # 检查类别匹配
        if "category" in term and "category" in context:
            if term["category"] != context["category"]:
                return False
                
        # 检查使用条件
        if "conditions" in term:
            for condition in term["conditions"]:
                if not self._evaluate_condition(condition, context):
                    return False
        
        return True
    
    def _check_compatibility(self, elements: List[str], context: Dict[str, Any]) -> bool:
        """
        检查元素兼容性
        
        Args:
            elements: 待检查元素列表
            context: 上下文信息
            
        Returns:
            bool: 是否兼容
        """
        # 加载兼容性规则
        compat_file = self.rules_dir / "compatibility.json"
        if not compat_file.exists():
            return True
            
        with open(compat_file, "r", encoding="utf-8") as f:
            compat_rules = json.load(f)
            
        # 检查每对元素的兼容性
        for i, elem1 in enumerate(elements):
            for elem2 in elements[i+1:]:
                rule_key = f"{elem1}_{elem2}"
                if rule_key in compat_rules:
                    if not compat_rules[rule_key]["compatible"]:
                        return False
        return True
    
    def _apply_fortune_logic(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用算命逻辑
        
        Args:
            data: 输入数据
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        result = data.copy()
        
        # 加载算命规则
        logic_file = self.rules_dir / "fortune_logic.json"
        if not logic_file.exists():
            return result
            
        with open(logic_file, "r", encoding="utf-8") as f:
            logic_rules = json.load(f)
            
        # 应用规则
        for rule in logic_rules:
            if self._evaluate_condition(rule["condition"], context):
                result = self._apply_transformations(result, rule["transformations"])
                
        return result
    
    def _format_response(self, content: Dict[str, Any], format_type: str) -> str:
        """
        格式化响应
        
        Args:
            content: 响应内容
            format_type: 格式类型
            
        Returns:
            str: 格式化后的响应
        """
        # 加载格式模板
        template_file = self.rules_dir / f"templates/{format_type}.json"
        if not template_file.exists():
            return str(content)
            
        with open(template_file, "r", encoding="utf-8") as f:
            template = json.load(f)
            
        # 应用模板
        return self._apply_template(content, template)
    
    def _evaluate_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """
        评估条件
        
        Args:
            condition: 条件定义
            context: 上下文信息
            
        Returns:
            bool: 条件是否满足
        """
        operator = condition.get("operator", "eq")
        field = condition.get("field")
        value = condition.get("value")
        
        if not field or field not in context:
            return False
            
        actual_value = context[field]
        
        if operator == "eq":
            return actual_value == value
        elif operator == "ne":
            return actual_value != value
        elif operator == "gt":
            return actual_value > value
        elif operator == "lt":
            return actual_value < value
        elif operator == "in":
            return actual_value in value
        elif operator == "contains":
            return value in actual_value
        elif operator == "regex":
            return bool(re.match(value, str(actual_value)))
        
        return False
    
    def _apply_transformations(self, data: Dict[str, Any], transformations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        应用转换规则
        
        Args:
            data: 输入数据
            transformations: 转换规则列表
            
        Returns:
            Dict[str, Any]: 转换后的数据
        """
        result = data.copy()
        
        for transform in transformations:
            field = transform.get("field")
            action = transform.get("action")
            value = transform.get("value")
            
            if not field or not action:
                continue
                
            if action == "set":
                result[field] = value
            elif action == "append":
                if field not in result:
                    result[field] = []
                result[field].append(value)
            elif action == "remove":
                if field in result:
                    del result[field]
            elif action == "modify":
                if field in result:
                    result[field] = self._modify_value(result[field], value)
                    
        return result
    
    def _apply_template(self, content: Dict[str, Any], template: Dict[str, Any]) -> str:
        """
        应用响应模板
        
        Args:
            content: 响应内容
            template: 模板定义
            
        Returns:
            str: 格式化后的响应
        """
        result = template.get("base", "")
        
        for section in template.get("sections", []):
            section_content = content.get(section["field"], "")
            if section_content:
                formatted = section["format"].format(content=section_content)
                result += formatted
                
        return result
    
    def _modify_value(self, original: Any, modification: Dict[str, Any]) -> Any:
        """
        修改值
        
        Args:
            original: 原始值
            modification: 修改规则
            
        Returns:
            Any: 修改后的值
        """
        mod_type = modification.get("type")
        
        if mod_type == "replace":
            return modification.get("value", original)
        elif mod_type == "append":
            return str(original) + str(modification.get("value", ""))
        elif mod_type == "prepend":
            return str(modification.get("value", "")) + str(original)
        
        return original
    
    def apply_rules(self, data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用规则
        
        Args:
            data: 输入数据
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        result = data.copy()
        
        # 按优先级排序规则
        sorted_rules = sorted(self.rules.values(), key=lambda x: x.priority)
        
        for rule in sorted_rules:
            if not rule.enabled:
                continue
                
            # 检查条件
            if not self._evaluate_condition(rule.condition, context):
                continue
                
            # 执行动作
            action_type = rule.action.get("type")
            action_params = rule.action.get("params", {})
            
            if action_type in self.actions:
                try:
                    action_result = self.actions[action_type](**action_params, context=context)
                    if isinstance(action_result, dict):
                        result.update(action_result)
                except Exception as e:
                    self.logger.error(f"Failed to execute action {action_type}: {e}")
                    
        return result

# 使用示例
if __name__ == "__main__":
    # 初始化规则引擎
    engine = RuleEngine()
    
    # 示例数据
    data = {
        "birth_year": 1990,
        "birth_month": 1,
        "birth_day": 1,
        "question": "我的事业运势如何？"
    }
    
    context = {
        "category": "career",
        "analysis_type": "bazi",
        "user_level": "basic"
    }
    
    # 应用规则
    result = engine.apply_rules(data, context)
    print(json.dumps(result, ensure_ascii=False, indent=2)) 