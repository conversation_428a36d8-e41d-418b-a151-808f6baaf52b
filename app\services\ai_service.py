from typing import Optional, List, Dict, Any
import openai
from app.core.cache_manager import Cache<PERSON>anager
from app.core.resilience import with_resilience, CircuitBreaker, RetryWithBackoff
from app.core.monitoring import monitor_model_inference
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)

class AIService:
    def __init__(
        self,
        cache_manager: CacheManager,
        openai_api_key: str,
        model: str = "gpt-3.5-turbo"
    ):
        """
        初始化AI服务
        
        Args:
            cache_manager: 缓存管理器实例
            openai_api_key: OpenAI API密钥
            model: 使用的模型名称，默认为gpt-3.5-turbo
        """
        self.cache_manager = cache_manager
        self.model = model
        openai.api_key = openai_api_key

    @with_resilience(
        circuit_breaker=CircuitBreaker(failure_threshold=3),
        retry=RetryWithBackoff(max_retries=2)
    )
    @monitor_model_inference()
    async def get_ai_response(
        self,
        question: str,
        user_id: str,
        context: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> Dict[str, Any]:
        """
        获取AI回答
        
        Args:
            question: 用户问题
            user_id: 用户ID
            context: 对话上下文，格式为[{"role": "user", "content": "xxx"}, ...]
            temperature: 温度参数，控制回答的随机性，范围0-1
            max_tokens: 最大令牌数
        
        Returns:
            Dict包含回答内容和元数据
        """
        # 检查缓存
        cache_key = f"ai_response:{user_id}:{hash(question)}"
        cached_response = await self.cache_manager.get_or_set(
            cache_key,
            lambda: self._generate_response(question, context, temperature, max_tokens),
            ttl=3600  # 缓存1小时
        )
        return cached_response

    async def _generate_response(
        self,
        question: str,
        context: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> Dict[str, Any]:
        """
        生成AI回答
        
        Args:
            question: 用户问题
            context: 对话上下文
            temperature: 温度参数
            max_tokens: 最大令牌数
        
        Returns:
            Dict包含回答内容和元数据
        """
        messages = context or []
        messages.append({"role": "user", "content": question})

        try:
            response = await openai.ChatCompletion.acreate(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )

            answer = response.choices[0].message.content
            return {
                "answer": answer,
                "metadata": {
                    "model": self.model,
                    "timestamp": datetime.utcnow().isoformat(),
                    "tokens_used": response.usage.total_tokens
                }
            }
        except Exception as e:
            logger.error(f"Error generating AI response: {str(e)}")
            raise

    async def get_conversation_history(
        self,
        user_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取用户对话历史
        
        Args:
            user_id: 用户ID
            limit: 返回的最大对话数量
        
        Returns:
            List[Dict]包含对话历史记录
        """
        cache_key = f"conversation_history:{user_id}"
        return await self.cache_manager.get_or_set(
            cache_key,
            lambda: self._fetch_conversation_history(user_id, limit),
            ttl=3600
        )

    async def _fetch_conversation_history(
        self,
        user_id: str,
        limit: int
    ) -> List[Dict[str, Any]]:
        """从数据库获取对话历史"""
        # TODO: 实现数据库查询
        return []

    async def clear_conversation_history(self, user_id: str) -> None:
        """
        清除用户对话历史
        
        Args:
            user_id: 用户ID
        """
        cache_key = f"conversation_history:{user_id}"
        await self.cache_manager.invalidate(cache_key)
        # TODO: 实现数据库清除 