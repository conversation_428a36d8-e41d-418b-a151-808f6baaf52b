-- 创建数据库
CREATE DATABASE IF NOT EXISTS fortune_telling DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE fortune_telling;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(64) UNIQUE NOT NULL COMMENT '微信openid',
    nickname VARCHAR(64) COMMENT '昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    gender TINYINT COMMENT '性别：0未知，1男，2女',
    points INT DEFAULT 0 COMMENT '积分',
    level INT DEFAULT 1 COMMENT '用户等级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_openid (openid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户出生信息表
CREATE TABLE IF NOT EXISTS birth_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(64) COMMENT '姓名',
    birth_date DATE NOT NULL COMMENT '出生日期',
    birth_time TIME COMMENT '出生时间',
    lunar_date DATE COMMENT '农历日期',
    lunar_time TIME COMMENT '农历时间',
    zodiac VARCHAR(8) COMMENT '生肖',
    constellation VARCHAR(16) COMMENT '星座',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户出生信息表';

-- 命理知识分类表
CREATE TABLE IF NOT EXISTS knowledge_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(32) NOT NULL COMMENT '分类名称',
    code VARCHAR(32) NOT NULL UNIQUE COMMENT '分类代码',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='命理知识分类表';

-- 命理知识条目表
CREATE TABLE IF NOT EXISTS knowledge_entries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL COMMENT '分类ID',
    title VARCHAR(128) NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    keywords VARCHAR(255) COMMENT '关键词，逗号分隔',
    source VARCHAR(255) COMMENT '来源',
    confidence DECIMAL(3,2) DEFAULT 1.00 COMMENT '可信度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES knowledge_categories(id),
    INDEX idx_category (category_id),
    FULLTEXT INDEX idx_content (content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='命理知识条目表';

-- 专业术语表
CREATE TABLE IF NOT EXISTS terminology (
    id INT PRIMARY KEY AUTO_INCREMENT,
    term VARCHAR(64) NOT NULL COMMENT '术语',
    category_id INT NOT NULL COMMENT '分类ID',
    description TEXT NOT NULL COMMENT '描述',
    aliases TEXT COMMENT '别名，逗号分隔',
    usage_examples TEXT COMMENT '使用示例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES knowledge_categories(id),
    INDEX idx_term (term)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业术语表';

-- 社区帖子表
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '作者ID',
    title VARCHAR(128) NOT NULL COMMENT '标题',
    content TEXT NOT NULL COMMENT '内容',
    category_id INT COMMENT '分类ID',
    images TEXT COMMENT '图片URL，逗号分隔',
    likes_count INT DEFAULT 0 COMMENT '点赞数',
    comments_count INT DEFAULT 0 COMMENT '评论数',
    views_count INT DEFAULT 0 COMMENT '浏览量',
    status TINYINT DEFAULT 1 COMMENT '状态：0删除，1正常，2置顶',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES knowledge_categories(id),
    INDEX idx_user (user_id),
    INDEX idx_category (category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社区帖子表';

-- 帖子评论表
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    user_id BIGINT NOT NULL COMMENT '评论用户ID',
    content TEXT NOT NULL COMMENT '评论内容',
    parent_id BIGINT DEFAULT NULL COMMENT '父评论ID',
    likes_count INT DEFAULT 0 COMMENT '点赞数',
    status TINYINT DEFAULT 1 COMMENT '状态：0删除，1正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES posts(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_id) REFERENCES comments(id),
    INDEX idx_post (post_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子评论表';

-- 用户收藏表
CREATE TABLE IF NOT EXISTS favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    post_id BIGINT NOT NULL COMMENT '帖子ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (post_id) REFERENCES posts(id),
    UNIQUE KEY uk_user_post (user_id, post_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 用户关注表
CREATE TABLE IF NOT EXISTS follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_id BIGINT NOT NULL COMMENT '关注者ID',
    following_id BIGINT NOT NULL COMMENT '被关注者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id),
    FOREIGN KEY (following_id) REFERENCES users(id),
    UNIQUE KEY uk_follower_following (follower_id, following_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 测算记录表
CREATE TABLE IF NOT EXISTS divination_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type VARCHAR(32) NOT NULL COMMENT '测算类型：bazi/ziwei/yijing/fengshui',
    input_data JSON COMMENT '输入数据',
    result_data JSON COMMENT '结果数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_type (user_id, type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测算记录表';

-- AI对话记录表
CREATE TABLE IF NOT EXISTS ai_chat_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question TEXT NOT NULL COMMENT '用户问题',
    answer TEXT NOT NULL COMMENT 'AI回答',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话记录表';

-- 系统消息表
CREATE TABLE IF NOT EXISTS system_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '接收用户ID',
    type VARCHAR(32) NOT NULL COMMENT '消息类型',
    title VARCHAR(128) NOT NULL COMMENT '消息标题',
    content TEXT NOT NULL COMMENT '消息内容',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读：0未读，1已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统消息表';

-- 知识分类表的初始数据
INSERT INTO knowledge_categories (id, name, code, description, sort_order, created_at) VALUES
(1, '八字命理', 'bazi', '以生辰八字为基础的传统命理学说，研究天干地支、五行生克、十神关系等', 1, NOW()),
(2, '紫微斗数', 'ziwei', '以紫微星为主的星盘预测体系，研究命宫、身宫、星耀等', 2, NOW()),
(3, '五行命理', 'wuxing', '研究金木水火土五行相生相克的规律及其在环境中的应用', 3, NOW()),
(4, '八卦易经', 'bagua', '研究八卦卦象、爻辞及其变化规律', 4, NOW()),
(5, '易经预测', 'yijing', '基于六十四卦的占卜预测系统', 5, NOW()),
(6, '阳宅风水', 'fengshui', '研究住宅布局、方位、气场等对人的影响', 6, NOW()),
(7, '生肖命理', 'zodiac', '十二生肖与人的性格、命运关系研究', 7, NOW()),
(8, '星座运势', 'astro', '西方占星学对性格和运势的影响研究', 8, NOW()),
(9, '传统命理', 'fortune', '集合各家学说的综合命理体系', 9, NOW()),
(10, '数字算卦', 'number_divination', '基于1-62数字的算卦方法，结合卦象解读的预测体系', 10, NOW());

-- 专业术语表的初始数据
INSERT INTO terminology (id, category_id, term, definition, aliases, usage_examples, created_at) VALUES
(1, 1, '天干', '甲、乙、丙、丁、戊、己、庚、辛、壬、癸十个字的统称', '十天干', '甲木生阳、乙木生阴', NOW()),
(2, 1, '地支', '子、丑、寅、卯、辰、巳、午、未、申、酉、戌、亥十二个地支的统称', '十二地支', '子水旺运、寅木当令', NOW()),
(3, 1, '纳音五行', '将六十甲子配合五行所形成的三十种五行属性', '纳音', '甲子乙丑海中金、丙寅丁卯炉中火', NOW()),
(4, 2, '紫微星系', '紫微斗数中的主星系统，包括紫微、天机、太阳、武曲等星耀', '主星', '紫微星入命宫主贵气', NOW()),
(5, 2, '命宫', '紫微斗数排盘的起点，代表人的先天命格', '命垣', '寅时生人寅宫起命', NOW()),
(6, 3, '五行生克制化', '五行相生为：木生火、火生土、土生金、金生水、水生木；相克为：金克木、木克土、土克水、水克火、火克金。此外还有相制、相化等关系。', '五行,相生,相克', '《五行大义》', NOW()),
(7, 3, '五行属性', '事物所属的金木水火土五种属性类别', '五行类别', '东方属木、南方属火', NOW()),
(8, 4, '先天八卦', '伏羲氏所创的八卦排列，体现宇宙自然规律', '伏羲八卦', '乾三连、坤六断', NOW()),
(9, 4, '后天八卦', '文王所创的八卦排列，体现人事规律', '文王八卦', '离火南方、坎水北方', NOW()),
(10, 5, '六十四卦', '八卦两两重叠组合而成的六十四种卦象', '重卦', '乾卦第一、坤卦第二', NOW()),
(11, 6, '玄空飞星', '风水学中研究九宫飞星方位吉凶的体系', '飞星风水', '一白贪狼、二黑巨门', NOW()),
(12, 7, '生肖五行', '十二生肖与五行的对应关系', '属相五行', '鼠属水、牛属土', NOW());

-- 知识条目表的初始数据
INSERT INTO knowledge_entries (id, category_id, title, content, keywords, source, created_at) VALUES
(1, 1, '八字基础理论', '八字是由年、月、日、时四柱组成，每柱包含天干和地支，共八个字，故称八字。这八个字展现了一个人出生时的宇宙能量状态，可用来分析人的命运。', '生辰八字,命理,算命', '《子平真诠》', NOW()),
(2, 1, '十神论命法', '十神是指日主所生之五行，阳干为印、比、劫、食、伤，阴干为枭、姐、才、官、杀，用来判断八字中五行的关系和作用。', '日主,十神,五行', '《命理约言》', NOW()),
(3, 2, '紫微斗数星耀', '紫微斗数以紫微星为核心，配以天机、太阳、武曲、天同、廉贞等主星，以及众多辅星，构成复杂而精确的预测体系。', '紫微星,命盘,星耀', '《紫微斗数全书》', NOW()),
(4, 2, '紫微命盘排法', '紫微斗数排盘首先定命宫，然后安紫微星系，再配以地支六十甲子，形成独特的星盘体系。', '命宫,紫微星系,排盘', '《斗数精要》', NOW()),
(5, 3, '五行生克制化', '五行相生为：木生火、火生土、土生金、金生水、水生木；相克为：金克木、木克土、土克水、水克火、火克金。此外还有相制、相化等关系。', '五行,相生,相克', '《五行大义》', NOW()),
(6, 3, '纳音五行精解', '纳音五行是将六十甲子配合五行，形成三十种五行属性，如甲子乙丑海中金、丙寅丁卯炉中火等，用于细化八字分析。', '纳音,甲子,五行', '《纳音辨微》', NOW()),
(7, 4, '八卦方位属性', '八卦有先天后天之分：先天八卦体现宇宙自然规律，后天八卦体现人事规律。每卦有其方位、五行、性质等属性。', '先天八卦,后天八卦,方位', '《周易》', NOW()),
(8, 4, '易经卦象解读', '六十四卦由八卦两两重叠而成，每卦六爻，爻有阴阳，可占事物的吉凶祸福。', '卦象,爻辞,占卜', '《易经》', NOW()),
(9, 5, '梅花易数', '梅花易数是由邵雍创立的数理预测体系，结合八卦、五行、天干地支等，用于预测吉凶。', '数理,预测,占卜', '《梅花易数》', NOW()),
(10, 6, '阳宅风水布局', '阳宅风水研究住宅的方位、布局、装饰等对人的影响，包括八宅派、玄空派、形势派等不同流派。', '住宅风水,八宅,玄空', '《阳宅三要》', NOW()),
(11, 7, '十二生肖性格', '十二生肖代表十二种不同的性格特征：鼠机灵、牛踏实、虎勇敢、兔温和、龙豪迈、蛇智慧、马活泼、羊善良、猴灵活、鸡勤劳、狗忠诚、猪厚道。', '生肖,性格,命理', '《生肖性格学》', NOW()),
(12, 8, '星座与命理', '西方占星学将黄道十二宫与东方命理相结合，研究星座对性格和命运的影响。', '星座,占星,命理', '《东西方星命学》', NOW()),
(13, 10, '数字算卦基础', '数字算卦是通过1-62个数字对应不同卦象的占卜方法。每个数字都对应特定的卦象和含义，通过求卦人的选数来预测吉凶。', '数字算卦,占卜,卦象', '《数字算卦精解》', NOW()),
(14, 10, '六十二数卦对应', '1-62数字各自对应不同卦象：如1数为乾卦、2数为坤卦、3数为屯卦等，每个数字都有其特定的卦象解释和预测意义。', '六十二数,卦象对应,占卜', '《数字卦象全书》', NOW()),
(15, 10, '数字卦象解读', '数字卦象解读需要考虑数字本身的属性、对应的卦象含义、动爻变化等多个方面，综合判断吉凶。', '卦象解读,变爻,吉凶', '《数字算命精要》', NOW()),
(16, 10, '求卦方法', '数字算卦的基本步骤：1.心中默念所求事项 2.选择1-62之间的数字 3.查找对应卦象 4.解读卦象含义 5.参考变爻 6.得出结论。', '求卦步骤,选数,解卦', '《数字算卦入门》', NOW()),
(17, 10, '数字五行属性', '62个数字除了对应卦象外，还具有五行属性，如1-4属金、5-8属木等，在解卦时需要综合考虑。', '数字五行,属性,解卦', '《数字五行论》', NOW()),
(18, 10, '吉凶数字', '在62数中，某些数字天生带有吉利或凶险的含义，如9、19、23等为吉数，7、14、28等为凶数，在占卜时需要特别注意。', '吉数,凶数,占卜', '《数字吉凶精解》', NOW()),
(19, 4, '六十四卦详解', '六十四卦包含乾、坤、屯、蒙、需、讼等卦象，每一卦都有其特定的卦辞、爻辞和变化规律。', '六十四卦,卦辞,爻辞', '《周易》', NOW()),
(20, 4, '卦象变化规律', '卦象的变化遵循特定规律，包括互卦、变卦、综卦等，每种变化都暗含着事物发展的趋势。', '互卦,变卦,综卦', '《易经解读》', NOW());

-- 五行关系表的初始数据
INSERT INTO five_elements_relations (id, element, generating, controlling, weakening, created_at) VALUES
(1, '金', '水', '木', '火', NOW()),
(2, '木', '火', '土', '金', NOW()),
(3, '水', '木', '火', '土', NOW()),
(4, '火', '土', '金', '水', NOW()),
(5, '土', '金', '水', '木', NOW());

-- 生肖关系表的初始数据
INSERT INTO zodiac_relations (id, zodiac, best_match, worst_match, created_at) VALUES
(1, '鼠', '龙,猴,牛', '马,羊', NOW()),
(2, '牛', '鼠,蛇,鸡', '羊,马', NOW()),
(3, '虎', '马,狗,猪', '猴,蛇', NOW()),
(4, '兔', '羊,狗,猪', '鸡,龙', NOW()),
(5, '龙', '鼠,猴,鸡', '狗,兔', NOW()),
(6, '蛇', '牛,鸡', '虎,猪', NOW()),
(7, '马', '虎,羊,狗', '鼠,牛', NOW()),
(8, '羊', '兔,马,猪', '牛,狗', NOW()),
(9, '猴', '鼠,龙,蛇', '虎,猪', NOW()),
(10, '鸡', '牛,龙,蛇', '兔,狗', NOW()),
(11, '狗', '虎,兔,马', '龙,鸡', NOW()),
(12, '猪', '虎,兔,羊', '蛇,猴', NOW());

-- 创建数字卦象对应表
CREATE TABLE IF NOT EXISTS number_hexagrams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    number INT NOT NULL COMMENT '数字(1-62)',
    hexagram VARCHAR(32) NOT NULL COMMENT '对应卦象',
    meaning TEXT NOT NULL COMMENT '卦象含义',
    five_element VARCHAR(8) COMMENT '五行属性',
    lucky_level INT COMMENT '吉凶等级：1-9，9为大吉',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_number (number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数字卦象对应表';

-- 数字卦象对应表的初始数据
INSERT INTO number_hexagrams (number, hexagram, meaning, five_element, lucky_level, created_at) VALUES
(1, '乾卦', '乾为天，刚健中正，象征领导者，表示事业兴旺、前程远大', '金', 9, NOW()),
(2, '坤卦', '坤为地，柔顺中正，象征包容，表示稳重踏实、百事亨通', '土', 8, NOW()),
(3, '屯卦', '屯为难，水雷屯，象征起始，表示开始困难、待时而动', '木', 6, NOW()),
(4, '蒙卦', '山水蒙，蒙昧初开，象征启蒙，表示需要教导、循序渐进', '水', 5, NOW()),
(5, '需卦', '水天需，须待时机，象征等待，表示静观其变、适时而动', '水', 7, NOW());
-- 注意：这里只添加了前5个数字的示例数据，实际应该添加全部62个数字的对应关系

-- 专业术语表补充数字算卦相关术语
INSERT INTO terminology (category_id, term, description, aliases, usage_examples, created_at) VALUES
(10, '动爻', '卦象中发生变化的爻，表示事物的变化趋势', '变爻,动变', '六爻卦中第三爻动，主人事变化', NOW()),
(10, '静爻', '卦象中保持不变的爻，表示事物的稳定状态', '不变爻', '六爻皆静，表示局势稳定', NOW()),
(10, '本卦', '求卦时最初得到的卦象', '原卦,初卦', '乾卦为本卦，变卦为坤', NOW()),
(10, '变卦', '本卦变化后得到的卦象', '之卦,后卦', '由乾卦变化得到坤', NOW()),
(10, '卦气', '每一卦所具有的特定能量和属性', '卦象能量', '乾卦卦气纯阳刚健', NOW()),
(10, '伏神', '卦中隐藏的神煞，影响卦象吉凶', '伏位,暗神', '伏神临贵人，主暗中有贵人相助', NOW()),
(10, '卦身', '卦象的主体，由六爻组成', '卦体', '卦身纯阳为乾卦', NOW()),
(10, '卦脉', '卦象变化的路径和规律', '卦理,卦道', '顺卦脉而变，事情渐入佳境', NOW()),
(10, '卦序', '六十四卦的排列顺序', '卦位次序', '乾卦居第一，坤卦居第二', NOW()),
(10, '数字卦', '用1-62数字对应卦象的算卦方法', '数卦', '以数字求卦，简便易行', NOW());

-- 补充卦象变化规则表
CREATE TABLE IF NOT EXISTS hexagram_transformations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    original_hexagram VARCHAR(32) NOT NULL COMMENT '原卦',
    changed_line INT NOT NULL COMMENT '变爻位置(1-6)',
    result_hexagram VARCHAR(32) NOT NULL COMMENT '变卦',
    transformation_meaning TEXT NOT NULL COMMENT '变化含义',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_original (original_hexagram),
    INDEX idx_result (result_hexagram)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卦象变化规则表';

-- 插入卦象变化规则示例数据
INSERT INTO hexagram_transformations (original_hexagram, changed_line, result_hexagram, transformation_meaning, created_at) VALUES
('乾卦', 1, '姤卦', '乾一变而为姤，阳极阴生，表示开始转变', NOW()),
('乾卦', 2, '遯卦', '乾二变而为遯，阳气消退，表示暂时退避', NOW()),
('乾卦', 3, '否卦', '乾三变而为否，阳被阴阻，表示暂时不通', NOW()),
('乾卦', 4, '同人卦', '乾四变而为同人，与物同体，表示与人和同', NOW()),
('乾卦', 5, '履卦', '乾五变而为履，阳履阴位，表示谨慎前行', NOW()),
('乾卦', 6, '泰卦', '乾六变而为泰，阴阳交泰，表示通达', NOW()),
('坤卦', 1, '复卦', '坤一变而为复，阴极阳生，表示开始复苏', NOW()),
('坤卦', 2, '临卦', '坤二变而为临，阳气渐长，表示开始发展', NOW()),
('坤卦', 3, '泰卦', '坤三变而为泰，阴阳交泰，表示形势大好', NOW()),
('坤卦', 4, '大壮卦', '坤四变而为大壮，阳气壮大，表示形势强劲', NOW()),
('坤卦', 5, '夬卦', '坤五变而为夬，阳气盛极，表示决断行事', NOW()),
('坤卦', 6, '乾卦', '坤六变而为乾，阴尽阳纯，表示彻底转化', NOW()),
('震卦', 1, '豫卦', '震一变而为豫，动而生悦，表示喜悦之象', NOW()),
('震卦', 2, '恒卦', '震二变而为恒，动而有常，表示持续发展', NOW()),
('震卦', 3, '升卦', '震三变而为升，动而上行，表示上升发展', NOW()),
('震卦', 4, '大过卦', '震四变而为大过，动而过度，表示过犹不及', NOW()),
('震卦', 5, '随卦', '震五变而为随，动而顺从，表示随势而行', NOW()),
('震卦', 6, '归妹卦', '震六变而为归妹，动而有归，表示回归本位', NOW()),
('离卦', 1, '旅卦', '离一变而为旅，明而动荡，表示行走不定', NOW()),
('离卦', 2, '鼎卦', '离二变而为鼎，明而革新，表示创新改变', NOW()),
('离卦', 3, '革卦', '离三变而为革，明而变革，表示彻底改革', NOW()),
('离卦', 4, '同人卦', '离四变而为同人，明而同道，表示志同道合', NOW()),
('离卦', 5, '晋卦', '离五变而为晋，明而进取，表示步步高升', NOW()),
('离卦', 6, '明夷卦', '离六变而为明夷，明而受伤，表示暂时低迷', NOW());

-- 更新数字卦象对应表的完整数据
TRUNCATE TABLE number_hexagrams;
INSERT INTO number_hexagrams (number, hexagram, meaning, five_element, lucky_level, created_at) VALUES
(1, '乾卦', '乾为天，刚健中正，象征领导者，表示事业兴旺、前程远大', '金', 9, NOW()),
(2, '坤卦', '坤为地，柔顺中正，象征包容，表示稳重踏实、百事亨通', '土', 8, NOW()),
(3, '屯卦', '屯为难，水雷屯，象征起始，表示开始困难、待时而动', '木', 6, NOW()),
(4, '蒙卦', '山水蒙，蒙昧初开，象征启蒙，表示需要教导、循序渐进', '水', 5, NOW()),
(5, '需卦', '水天需，须待时机，象征等待，表示静观其变、适时而动', '水', 7, NOW()),
(6, '讼卦', '天水讼，争讼不休，象征诉讼，表示纠纷争执、慎重行事', '金', 3, NOW()),
(7, '师卦', '地水师，众人齐心，象征军队，表示组织纪律、行动有序', '水', 4, NOW()),
(8, '比卦', '水地比，亲近比和，象征亲近，表示团结合作、互帮互助', '水', 8, NOW()),
(9, '小畜卦', '风天小畜，涵养德行，象征积累，表示循序渐进、稳步发展', '木', 7, NOW()),
(10, '履卦', '天泽履，脚踏实地，象征践行，表示谨慎前进、依序而行', '金', 6, NOW()),
(11, '泰卦', '地天泰，阴阳交泰，象征通达，表示诸事顺遂、通达无阻', '土', 9, NOW()),
(12, '否卦', '天地否，阴阳不交，象征闭塞，表示暂时不通、静待时机', '土', 2, NOW()),
(13, '同人卦', '天火同人，志同道合，象征和同，表示团结一致、共同进取', '火', 8, NOW()),
(14, '大有卦', '火天大有，光明盛大，象征丰盛，表示前途光明、收获颇丰', '火', 9, NOW()),
(15, '谦卦', '地山谦，谦逊虚心，象征谦虚，表示谦虚做人、稳步前进', '土', 7, NOW()),
(16, '豫卦', '雷地豫，顺势而动，象征喜悦，表示心情愉快、事事如意', '木', 8, NOW()),
(17, '随卦', '泽雷随，随顺时势，象征跟随，表示顺势而为、随机应变', '金', 6, NOW()),
(18, '蛊卦', '山风蛊，振荡革新，象征改革，表示整顿革新、克服困难', '木', 5, NOW()),
(19, '临卦', '地泽临，临下观察，象征监督，表示关注发展、把握机会', '土', 7, NOW()),
(20, '观卦', '风地观，观察形势，象征观望，表示审时度势、观察入微', '木', 6, NOW()),
(21, '噬嗑卦', '火雷噬嗑，雷霆行动，象征决断，表示果断行动、克服困难', '火', 5, NOW()),
(22, '贲卦', '山火贲，装饰美化，象征文明，表示修饰改进、锦上添花', '火', 7, NOW()),
(23, '剥卦', '山地剥，剥落渐变，象征衰落，表示渐趋衰落、及时改变', '土', 3, NOW()),
(24, '复卦', '地雷复，否极泰来，象征复兴，表示重新开始、转危为安', '木', 8, NOW()),
(25, '无妄卦', '天雷无妄，无妄而得，象征自然，表示顺其自然、无所妄为', '木', 7, NOW()),
(26, '大畜卦', '山天大畜，蓄养待时，象征积累，表示积蓄力量、等待时机', '土', 6, NOW()),
(27, '颐卦', '山雷颐，养育万物，象征养育，表示修养身心、培养才能', '木', 7, NOW()),
(28, '大过卦', '泽风大过，过于强大，象征过度，表示过犹不及、适可而止', '金', 4, NOW()),
(29, '坎卦', '坎为水，险中有险，象征险难，表示谨慎行事、化险为夷', '水', 3, NOW()),
(30, '离卦', '离为火，光明显著，象征光明，表示光明大道、前途光明', '火', 9, NOW()),
(31, '咸卦', '泽山咸，相互感应，象征感化，表示心心相印、相互影响', '金', 7, NOW()),
(32, '恒卦', '雷风恒，持续不断，象征恒久，表示持之以恒、永续发展', '木', 8, NOW()),
(33, '遯卦', '天山遁，遁世避难，象征退避，表示暂时退避、保存实力', '土', 4, NOW()),
(34, '大壮卦', '雷天大壮，阳气壮大，象征强盛，表示气势强大、壮大发展', '木', 8, NOW()),
(35, '晋卦', '火地晋，日上升进，象征进步，表示步步高升、稳步前进', '火', 9, NOW()),
(36, '明夷卦', '地火明夷，光明受伤，象征晦暗，表示暂时晦暗、韬光养晦', '火', 3, NOW()),
(37, '家人卦', '风火家人，家庭和睦，象征家庭，表示家庭和谐、亲情和美', '火', 8, NOW()),
(38, '睽卦', '火泽睽，背道而驰，象征乖离，表示彼此对立、化解分歧', '金', 4, NOW()),
(39, '蹇卦', '水山蹇，行走艰难，象征困难，表示诸事不顺、谨慎前行', '水', 3, NOW()),
(40, '解卦', '雷水解，解除束缚，象征解脱，表示摆脱困境、豁然开朗', '木', 7, NOW()),
(41, '损卦', '山泽损，损上益下，象征损益，表示有舍有得、取舍得当', '金', 5, NOW()),
(42, '益卦', '风雷益，损下益上，象征增益，表示日益月将、逐步提高', '木', 8, NOW()),
(43, '夬卦', '泽天夬，决断果行，象征决断，表示果断行事、克敌制胜', '金', 7, NOW()),
(44, '姤卦', '天风姤，天下相遇，象征邂逅，表示偶然相遇、机缘巧合', '木', 6, NOW()),
(45, '萃卦', '泽地萃，聚集会合，象征聚集，表示群策群力、共同进步', '土', 8, NOW()),
(46, '升卦', '地风升，向上升进，象征上升，表示节节高升、蒸蒸日上', '木', 9, NOW()),
(47, '困卦', '泽水困，陷入困境，象征困厄，表示暂时困顿、静待时机', '水', 2, NOW()),
(48, '井卦', '水风井，井而养贤，象征培养，表示培养人才、滋养万物', '水', 7, NOW()),
(49, '革卦', '泽火革，改革创新，象征变革，表示革故鼎新、开创新局', '火', 6, NOW()),
(50, '鼎卦', '火风鼎，器物更新，象征创新，表示创新发展、开拓进取', '火', 8, NOW()),
(51, '震卦', '震为雷，雷霆万钧，象征震动，表示行动迅速、雷厉风行', '木', 5, NOW()),
(52, '艮卦', '艮为山，稳重停止，象征止足，表示停止观察、稳重行事', '土', 6, NOW()),
(53, '渐卦', '风山渐，循序渐进，象征进展，表示循序渐进、稳步发展', '木', 7, NOW()),
(54, '归妹卦', '雷泽归妹，少女归家，象征归宿，表示物各有归、各得其所', '金', 6, NOW()),
(55, '丰卦', '雷火丰，雷电交加，象征丰盛，表示丰富多彩、充实圆满', '火', 8, NOW()),
(56, '旅卦', '火山旅，漂泊不定，象征旅行，表示行动谨慎、小心谨慎', '火', 4, NOW()),
(57, '巽卦', '巽为风，随顺不争，象征谦逊，表示谦逊随和、顺势而为', '木', 7, NOW()),
(58, '兑卦', '兑为泽，喜悦和顺，象征喜悦，表示喜气洋洋、心情愉快', '金', 8, NOW()),
(59, '涣卦', '风水涣，水流分散，象征分散，表示分散投资、广泛发展', '水', 5, NOW()),
(60, '节卦', '水泽节，节制规范，象征节制，表示适度节制、遵守规范', '水', 6, NOW()),
(61, '中孚卦', '风泽中孚，诚信中正，象征诚信，表示诚实守信、中正平和', '金', 8, NOW()),
(62, '小过卦', '雷山小过，小有越过，象征越过，表示小有超越、稳中求进', '土', 6, NOW());

-- 添加卦象解读规则表
CREATE TABLE IF NOT EXISTS hexagram_interpretation_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    hexagram VARCHAR(32) NOT NULL COMMENT '卦象名称',
    aspect VARCHAR(32) NOT NULL COMMENT '解读方面(事业/感情/健康等)',
    interpretation TEXT NOT NULL COMMENT '具体解读',
    notes TEXT COMMENT '补充说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_hexagram_aspect (hexagram, aspect)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卦象解读规则表';

-- 插入卦象解读规则示例数据
INSERT INTO hexagram_interpretation_rules (hexagram, aspect, interpretation, notes, created_at) VALUES
('乾卦', '事业', '事业蒸蒸日上，领导力强，适合创业或担任要职', '注意谦虚谨慎，不可刚愎自用', NOW()),
('乾卦', '感情', '感情专一执着，有主见，易得贵人相助', '需注意照顾对方感受', NOW()),
('乾卦', '健康', '体质强健，精力充沛，但易有头部疾患', '建议注意劳逸结合', NOW()),
('坤卦', '事业', '脚踏实地，稳步发展，利于长期规划', '需增强主动性和创新意识', NOW()),
('坤卦', '感情', '温柔体贴，包容力强，易获得好姻缘', '应适当表达自己的想法', NOW()),
('坤卦', '健康', '体质较弱，宜调养，易有消化系统问题', '建议加强运动，注意饮食', NOW()),
('震卦', '事业', '事业有新的突破，行动力强，适合开拓新领域', '注意把握时机，不可贸然行动', NOW()),
('震卦', '感情', '感情有意外惊喜，可能遇到心动对象', '不要过于冲动，需要理性对待', NOW()),
('震卦', '健康', '精力充沛但易紧张，需要适度运动放松', '建议进行户外活动，保持心情舒畅', NOW()),
('震卦', '财运', '财运波动较大，可能有意外收获', '适合短期投资，但需控制风险', NOW()),
('离卦', '事业', '事业明朗光明，容易得到赏识和提拔', '保持谦虚，继续努力', NOW()),
('离卦', '感情', '感情明确，易于表达，桃花运旺', '不要过于炫耀，保持内敛', NOW()),
('离卦', '健康', '精神状态好，但要注意心火旺盛', '建议保持充足睡眠，避免熬夜', NOW()),
('离卦', '财运', '财运亨通，易得贵人相助', '理性投资，不可投机', NOW()),
('坎卦', '事业', '事业有波折，需要沉着应对，暗中有贵人相助', '谨慎行事，避免冒险', NOW()),
('坎卦', '感情', '感情较为曲折，可能遇到考验', '以诚相待，保持耐心', NOW()),
('坎卦', '健康', '易有肾虚水肿症状，需要调养', '注意保暖，适当运动', NOW()),
('坎卦', '财运', '财运起伏较大，需要稳健理财', '不适合激进投资，宜稳健为主', NOW()),
('艮卦', '事业', '事业需要沉稳，适合稳扎稳打', '不要急于求成，循序渐进', NOW()),
('艮卦', '感情', '感情需要等待，切勿强求', '顺其自然，水到渠成', NOW()),
('艮卦', '健康', '身体较为稳定，但要注意消化系统', '规律作息，注意饮食', NOW()),
('艮卦', '财运', '财运平稳，适合长期投资', '可以考虑稳健的理财产品', NOW()),
('兑卦', '事业', '事业开朗顺遂，人际关系好', '把握机会，广结人缘', NOW()),
('兑卦', '感情', '感情欢快愉悦，易受欢迎', '保持真诚，不要过于表面', NOW()),
('兑卦', '健康', '心情愉快，但要注意口腔健康', '注意口腔卫生，适量运动', NOW()),
('兑卦', '财运', '财运喜人，易有偏财运', '合理消费，不要铺张', NOW()),
('巽卦', '事业', '事业需要灵活变通，善于适应环境', '随机应变，把握方向', NOW()),
('巽卦', '感情', '感情细腻温和，易得他人好感', '保持温和，不要优柔寡断', NOW()),
('巽卦', '健康', '气血调和，但易受风邪影响', '注意防风，适当运动', NOW()),
('巽卦', '财运', '财运平顺，适合稳健理财', '不要投机取巧，稳中求进', NOW()),
('乾卦', '投资', '适合大胆投资，开拓新领域，但需控制风险', '以稳健投资为主，不可过于冒进', NOW()),
('乾卦', '学业', '学习能力强，思维活跃，适合深造进修', '注意基础知识的巩固', NOW()),
('乾卦', '出行', '出行顺利，贵人相助，适合远行', '注意提前规划行程', NOW()),
('乾卦', '婚姻', '婚姻美满，但需互相包容，以和为贵', '注意协调双方关系', NOW()),
('坤卦', '投资', '适合稳健投资，以保值为主，回报稳定', '避免激进投资，保持耐心', NOW()),
('坤卦', '学业', '学习踏实，进步虽慢但扎实', '循序渐进，不可急躁', NOW()),
('坤卦', '出行', '出行平安，但需谨慎，不宜过远', '建议结伴同行', NOW()),
('坤卦', '婚姻', '婚姻稳定，以家庭为重，重视亲情', '注意家庭和睦', NOW()),
('震卦', '求职', '求职有新机遇，但需把握时机', '主动出击，展现能力', NOW()),
('震卦', '考试', '考试有惊喜，发挥超常，但需沉着', '保持冷静，细心审题', NOW()),
('震卦', '创业', '创业机会良好，但需控制风险', '把握市场动向，稳步推进', NOW()),
('震卦', '搬迁', '搬迁有利，但需注意时机选择', '选择吉日，注意细节', NOW()),
('坎卦', '求职', '求职道路曲折，需要耐心等待', '保持信心，坚持不懈', NOW()),
('坎卦', '考试', '考试需要沉着冷静，避免急躁', '重视细节，避免粗心', NOW()),
('坎卦', '创业', '创业需要谨慎，风险较大', '做好风险评估，稳妥推进', NOW()),
('坎卦', '搬迁', '搬迁有阻碍，需要充分准备', '选择良辰吉日，注意安全', NOW()),
('艮卦', '求职', '求职需要耐心，机会在意料之外', '保持开放心态，把握机会', NOW()),
('艮卦', '考试', '考试发挥稳定，基础知识重要', '注重基础，避免急躁', NOW()),
('艮卦', '创业', '创业需要稳扎稳打，循序渐进', '注重积累，避免冒进', NOW()),
('艮卦', '搬迁', '搬迁需要慎重，不宜操之过急', '充分准备，择日而行', NOW()),
('离卦', '求职', '求职顺利，容易得到赏识', '展现才能，把握机会', NOW()),
('离卦', '考试', '考试发挥出色，思维清晰', '保持冷静，细心作答', NOW()),
('离卦', '创业', '创业前景光明，但需防范风险', '把握方向，稳步前进', NOW()),
('离卦', '搬迁', '搬迁顺利，但需注意细节', '注意新居采光通风', NOW());

-- 添加卦象组合解读规则表
CREATE TABLE IF NOT EXISTS hexagram_combination_rules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    hexagram1 VARCHAR(32) NOT NULL COMMENT '第一卦',
    hexagram2 VARCHAR(32) NOT NULL COMMENT '第二卦',
    combination_meaning TEXT NOT NULL COMMENT '组合含义',
    application_scenario TEXT COMMENT '适用场景',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_combination (hexagram1, hexagram2)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卦象组合解读规则表';

-- 插入卦象组合解读规则示例数据
INSERT INTO hexagram_combination_rules (hexagram1, hexagram2, combination_meaning, application_scenario, created_at) VALUES
('乾卦', '坤卦', '乾坤相配，天地交泰，万事亨通', '适合重大决策、开展新事业、婚姻大事', NOW()),
('震卦', '巽卦', '震巽相济，雷风相薄，动静结合', '适合改革创新、开拓市场、人际交往', NOW()),
('离卦', '坎卦', '离坎相济，水火既济，阴阳调和', '适合化解矛盾、调和关系、经营事业', NOW()),
('艮卦', '兑卦', '艮兑相辅，山泽通气，刚柔相济', '适合稳健发展、文化教育、休闲娱乐', NOW()),
('乾卦', '离卦', '乾离相辅，日月同辉，光明盛大', '适合领导决策、创业创新、追求卓越', NOW()),
('坤卦', '坎卦', '坤坎相济，地水承载，滋养万物', '适合稳健发展、农业生产、教育培养', NOW()),
('乾卦', '兑卦', '乾兑相生，金气相辅，利于开拓进取', '适合商业谈判、市场开拓、品牌推广、公关活动', NOW()),
('坤卦', '艮卦', '坤艮相生，厚土载物，利于稳健发展', '适合房地产、农业、教育、文化传承等领域', NOW()),
('震卦', '巽卦', '震巽相生，风雷激荡，利于变革创新', '适合技术革新、制度改革、创意策划等方面', NOW()),
('离卦', '离卦', '离离相生，火光通明，利于文教事业', '适合教育培训、文化传播、品牌营销等领域', NOW()),
('乾卦', '巽卦', '乾巽相克，金风相争，需要谨慎行事', '不适合贸然决策，需要充分考虑各方意见', NOW()),
('坤卦', '震卦', '坤震相克，地雷相冲，需要化解矛盾', '需要调和各方关系，避免冲突升级', NOW()),
('艮卦', '离卦', '艮离相克，山火相焚，需要控制风险', '需要防范潜在风险，避免激进行为', NOW()),
('兑卦', '坎卦', '兑坎相克，金水相战，需要平衡发展', '需要平衡各方利益，避免极端化', NOW()),
('乾卦', '震卦', '乾震合德，天雷同声，利于领导决策', '适合重大决策、战略规划、组织变革等', NOW()),
('坤卦', '巽卦', '坤巽合德，地风升腾，利于稳步提升', '适合渐进改革、人才培养、市场深耕等', NOW()),
('艮卦', '坎卦', '艮坎相济，山水相依，利于稳健经营', '适合传统行业、文化产业、教育培训等', NOW()),
('离卦', '兑卦', '离兑相辅，火金相生，利于创新发展', '适合科技创新、品牌营销、文化创意等', NOW()),
('乾卦', '坤卦', '乾坤六爻，阴阳交泰，大道至简', '适合重大变革、战略转型、组织重构等', NOW()),
('震卦', '艮卦', '震艮六爻，动静相济，变中求稳', '适合稳健改革、制度创新、业务转型等', NOW()),
('坎卦', '离卦', '坎离六爻，水火既济，阴阳调和', '适合矛盾调解、关系协调、利益平衡等', NOW()),
('乾卦', '艮卦', '金生水，乾艮相生，利于开源节流', '适合财务管理、资源整合、投资理财等', NOW()),
('震卦', '离卦', '木生火，震离相生，利于开拓创新', '适合技术创新、市场开拓、品牌建设等', NOW()),
('巽卦', '坤卦', '木生土，巽坤相生，利于基础建设', '适合基础设施、人才培养、制度建设等', NOW()),
('乾卦', '离卦', '乾离当午，日月争辉，昌盛发达', '适合重大项目、战略投资、品牌升级等', NOW()),
('坤卦', '坎卦', '坤坎子时，天地交泰，根基稳固', '适合基础建设、人才储备、文化传承等', NOW()),
('震卦', '巽卦', '震巽相会，雷风相助，变革创新', '适合技术革新、制度改革、创意策划等', NOW());

-- 扩展卦象时空关系表结构
DROP TABLE IF EXISTS hexagram_temporal_spatial;
CREATE TABLE hexagram_temporal_spatial (
    id INT PRIMARY KEY AUTO_INCREMENT,
    hexagram VARCHAR(32) NOT NULL COMMENT '卦象名称',
    season VARCHAR(16) COMMENT '对应季节',
    solar_term VARCHAR(32) COMMENT '对应节气',
    direction VARCHAR(16) COMMENT '对应方位',
    time_period VARCHAR(32) COMMENT '对应时辰',
    element VARCHAR(8) COMMENT '五行属性',
    color VARCHAR(16) COMMENT '对应色彩',
    zodiac VARCHAR(16) COMMENT '对应生肖',
    planet VARCHAR(16) COMMENT '对应星宿',
    organ VARCHAR(16) COMMENT '对应人体器官',
    nature VARCHAR(32) COMMENT '自然现象',
    weather VARCHAR(32) COMMENT '气候特征',
    number INT COMMENT '对应数字',
    family_member VARCHAR(16) COMMENT '对应家人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_hexagram (hexagram)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卦象时空关系表';

-- 插入更详细的卦象时空关系数据
INSERT INTO hexagram_temporal_spatial (
    hexagram, season, solar_term, direction, time_period, element, color, 
    zodiac, planet, organ, nature, weather, number, family_member
) VALUES
-- 乾卦关系
('乾卦', '秋', '秋分、寒露', '西北', '午时（11:00-13:00）', '金', '白色', 
 '马', '太白金星', '头部、肺', '天空、太阳', '干燥、晴朗', 9, '父亲'),

-- 坤卦关系
('坤卦', '冬', '大雪、冬至', '西南', '子时（23:00-1:00）', '土', '黄色',
 '牛', '土星', '腹部、脾胃', '大地、土壤', '阴湿、多云', 2, '母亲'),

-- 震卦关系
('震卦', '春', '立春、雨水', '东', '寅时（3:00-5:00）', '木', '青色',
 '龙', '木星', '足部、肝', '雷电、震动', '多雷、闪电', 3, '长子'),

-- 巽卦关系
('巽卦', '春', '春分、清明', '东南', '辰时（7:00-9:00）', '木', '绿色',
 '鸡', '岁星', '大腿、胆', '风、空气', '和风、微雨', 4, '长女'),

-- 坎卦关系
-- 插入卦象时空关系数据
INSERT INTO hexagram_temporal_spatial (hexagram, season, direction, time_period, element, color, created_at) VALUES
('乾卦', '秋', '西北', '午时', '金', '白色', NOW()),
('坤卦', '冬', '西南', '子时', '土', '黄色', NOW()),
('震卦', '春', '东', '寅时', '木', '青色', NOW()),
('巽卦', '春', '东南', '辰时', '木', '绿色', NOW()),
('坎卦', '冬', '北', '子时', '水', '黑色', NOW()),
('离卦', '夏', '南', '午时', '火', '红色', NOW()),
('艮卦', '冬', '东北', '丑时', '土', '褐色', NOW()),
('兑卦', '秋', '西', '酉时', '金', '银色', NOW());

-- 添加紫微斗数四化星详细信息
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(2, '紫微斗数四化星详解', '四化星是紫微斗数中最重要的动态因子，包括化禄、化权、化科、化忌四种状态。化禄代表财富和福分，主吉祥如意；化权代表权力和掌控，主事业发展；化科代表功名和文昌，主学业考试；化忌代表阻碍和困扰，主挫折和考验。四化星的位置和组合对命盘分析至关重要。', '紫微大师', NOW()),
(2, '十天干四化表', '甲年：廉贞化禄，破军化权，武曲化科，太阳化忌。乙年：天机化禄，天梁化权，紫微化科，太阴化忌。丙年：天同化禄，天机化权，文昌化科，廉贞化忌。丁年：太阴化禄，天同化权，天机化科，巨门化忌。戊年：贪狼化禄，太阴化权，右弼化科，天机化忌。己年：武曲化禄，贪狼化权，天梁化科，文曲化忌。庚年：太阳化禄，武曲化权，太阴化科，天同化忌。辛年：巨门化禄，太阳化权，文曲化科，文昌化忌。壬年：天梁化禄，紫微化权，左辅化科，武曲化忌。癸年：破军化禄，巨门化权，太阴化科，贪狼化忌。', '命理专家', NOW()),
(2, '紫微斗数十二宫详解', '十二宫分别为：命宫（本人先天运势），兄弟宫（手足关系），夫妻宫（感情婚姻），子女宫（子女缘分），财帛宫（财运理财），疾厄宫（健康疾病），迁移宫（外出发展），奴仆宫（朋友部属），官禄宫（事业工作），田宅宫（不动产家运），福德宫（精神享受），父母宫（父母长辈）。每宫都有其特定的象征意义和解读方法。', '斗数研究者', NOW()),
(2, '十四主星详解', '紫微斗数有十四颗主星：紫微星（帝王星，尊贵权威），天机星（智慧星，变动灵活），太阳星（父亲星，光明磊落），武曲星（财帛星，刚毅果断），天同星（福德星，温和享受），廉贞星（次桃花，变化多端），天府星（令星，稳重保守），太阴星（母亲星，温柔体贴），贪狼星（桃花星，多才多艺），巨门星（是非星，口才辩论），天相星（印星，忠厚老实），天梁星（荫星，长者风范），七杀星（杀星，冲动开创），破军星（耗星，破坏改革）。', '紫微学者', NOW()),
(2, '紫微斗数宫位意义详解', '命宫主个人性格命运，身宫主后天修为。夫妻宫看配偶情况和婚姻状况，化禄入夫妻宫主感情甜蜜。子女宫看子女缘分和创意能力。财帛宫看财运和理财观念，武曲入财帛宫主财源广进。官禄宫看事业发展和工作状况。迁移宫看外出运势和贵人助力。交友宫看人际关系和部属情况。疾厄宫看健康状况和体质强弱。田宅宫看不动产和家庭运势。福德宫看精神状态和享受能力。父母宫看父母情况和长辈关系。', '命理大师', NOW());

-- 添加天干地支基础知识
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(1, '十天干详解', '十天干：甲（阳木，参天大树），乙（阴木，花草树木），丙（阳火，太阳之火），丁（阴火，灯烛之火），戊（阳土，高山之土），己（阴土，田园之土），庚（阳金，剑锋之金），辛（阴金，首饰之金），壬（阳水，大海之水），癸（阴水，雨露之水）。天干代表天时和动态变化，在命理中用于推算四化和各种变动。', '易学专家', NOW()),
(1, '十二地支详解', '十二地支：子（鼠，北方水），丑（牛，东北土），寅（虎，东北木），卯（兔，东方木），辰（龙，东南土），巳（蛇，东南火），午（马，南方火），未（羊，西南土），申（猴，西南金），酉（鸡，西方金），戌（狗，西北土），亥（猪，西北水）。地支代表地理和静态环境，在命理中用于安星排盘和时空定位。', '命学研究者', NOW()),
(1, '天干地支五行配属', '天干五行：甲乙属木，丙丁属火，戊己属土，庚辛属金，壬癸属水。地支五行：寅卯属木，巳午属火，申酉属金，亥子属水，辰戌丑未属土。阳性天干：甲丙戊庚壬。阴性天干：乙丁己辛癸。阳性地支：子寅辰午申戌。阴性地支：丑卯巳未酉亥。五行相生：木生火，火生土，土生金，金生水，水生木。五行相克：木克土，土克水，水克火，火克金，金克木。', '五行学者', NOW()),
(1, '六十甲子纳音详解', '六十甲子纳音是古代命理学的重要组成部分，每两个干支组合对应一个纳音五行。如甲子乙丑海中金，丙寅丁卯炉中火，戊辰己巳大林木等。纳音五行反映了事物的本质属性和发展趋势，在命理分析中具有重要参考价值。海中金表示深藏不露的财富，炉中火表示熊熊燃烧的激情，大林木表示茂盛蓬勃的生命力。', '国学大师', NOW());

-- 添加八字命理基础知识
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(1, '八字十神详解', '十神是八字命理的核心理论，以日干为中心，根据五行生克关系确定：比肩（同我者），劫财（同类异性），食神（我生者同性），伤官（我生者异性），正财（我克者异性），偏财（我克者同性），正官（克我者异性），七杀（克我者同性），正印（生我者异性），偏印（生我者同性）。十神反映了人的性格特征、人际关系和命运走向。', '八字专家', NOW()),
(1, '八字格局分析', '八字格局是判断命运层次的重要标准。主要格局有：正官格（以正官为用神），七杀格（以七杀为用神），正财格（以正财为用神），偏财格（以偏财为用神），食神格（以食神为用神），伤官格（以伤官为用神），正印格（以正印为用神），偏印格（以偏印为用神）等。特殊格局有从强格、从弱格、化气格等。格局的成败影响人生的贵贱高低。', '命理学家', NOW()),
(1, '八字大运流年分析', '大运十年一换，代表人生的大阶段变化。流年一年一换，代表当年的具体运势。大运干支分别管五年，天干主外象，地支主内在。流年与命局、大运的作用关系决定当年吉凶。喜用神得力则吉，忌神当权则凶。天干主事物的性质，地支主事物的程度。分析大运流年要结合命局格局和用神忌神的变化。', '运势专家', NOW());

-- 添加命理术语解释
INSERT INTO terminology (term, definition, category, created_at) VALUES
('化禄', '四化之一，主财富、福分、缘分。入命身宫主得财得福，入六亲宫主缘分深厚。', '紫微斗数', NOW()),
('化权', '四化之一，主权力、能力、变动。入命身宫主有权威有能力，但也主变动不安。', '紫微斗数', NOW()),
('化科', '四化之一，主功名、文昌、贵人。入命身宫主有文采有贵人，利考试求名。', '紫微斗数', NOW()),
('化忌', '四化之一，主阻碍、困扰、执着。入命身宫主波折困扰，但也主专注执着。', '紫微斗数', NOW()),
('庙旺', '星曜在某宫位力量最强的状态，能充分发挥正面作用。', '紫微斗数', NOW()),
('陷地', '星曜在某宫位力量最弱的状态，容易发挥负面作用。', '紫微斗数', NOW()),
('三方四正', '以某宫为中心，其对宫、三合宫位组成的四个宫位，影响该宫的整体格局。', '紫微斗数', NOW()),
('桃花星', '主感情、异性缘、艺术才华的星曜，如贪狼、廉贞、红鸾、天喜等。', '紫微斗数', NOW()),
('财星', '主财富、理财能力的星曜，如武曲、太阴、天府等。', '紫微斗数', NOW()),
('煞星', '主冲动、破坏、竞争的星曜，如擎羊、陀罗、火星、铃星等。', '紫微斗数', NOW()),
('正官', '八字十神之一，克我者异性，主权威、工作、压力。正官透干有根为贵命。', '八字命理', NOW()),
('七杀', '八字十神之一，克我者同性，主权力、竞争、变动。七杀有制化为权。', '八字命理', NOW()),
('食神', '八字十神之一，我生者同性，主才华、福分、子女。食神生财为富命。', '八字命理', NOW()),
('伤官', '八字十神之一，我生者异性，主才华、口才、叛逆。伤官配印为贵格。', '八字命理', NOW()),
('用神', '八字中对日主最有利的五行，是命局平衡的关键，决定人生成败。', '八字命理', NOW()),
('忌神', '八字中对日主最不利的五行，是命局失衡的根源，主凶祸灾难。', '八字命理', NOW());

-- 添加卦象组合解读规则
INSERT INTO hexagram_combination_rules (first_hexagram, second_hexagram, relationship_type, interpretation, principle) VALUES
('乾', '兑', '天泽履', '乾为天，兑为泽。天在上，泽在下，上下有序，履行正道。此卦象征履行天道，遵循礼法，谨慎而行。', '上下有序，履行正道'),
('坤', '震', '地雷复', '坤为地，震为雷。雷在地中，阳气初生，万物复苏。此卦象征阴极阳生，物极必反，新的开始。', '阴极阳生，万物复苏'),
('水天需', '山天大畜', '需卦变大畜', '需卦等待时机，大畜积蓄力量。由等待转为积蓄，时机未到需要更多准备。', '由等待转为积蓄'),
('火地晋', '地火明夷', '晋卦变明夷', '晋卦光明上进，明夷光明受阻。由上进转为蛰伏，需要在困难中保持内心光明。', '上进转为蛰伏'),
('雷风恒', '风雷益', '恒卦变益', '恒卦持久不变，益卦增益助长。由恒久转为增益，持续努力带来收获。', '持续努力带来收获'),
('水火既济', '火水未济', '既济变未济', '既济卦功成名就，未济卦功亏一篑。成功之后要警惕，满招损，谦受益。', '成功之后要警惕'),
('天山遁', '雷山小过', '遁卦与小过', '遁卦退避保身，小过小心行事。都主谨慎保守，不宜大动，宜静待时机。', '谨慎保守，静待时机'),
('泽山咸', '雷泽归妹', '咸卦与归妹', '咸卦感应相通，归妹女归于男。都与感情相关，但归妹较为冲动，需要理性。', '感情相关，需要理性');

-- 添加更多的专业术语
INSERT INTO terminology (term, definition, category, created_at) VALUES
('大限', '紫微斗数中每十年为一大限，影响十年的整体运势。', '紫微斗数', NOW()),
('小限', '紫微斗数中每年的运势变化，一年一换。', '紫微斗数', NOW()),
('流年', '当年的运势，配合大限小限一起分析。', '紫微斗数', NOW()),
('生年四化', '根据出生年天干确定的四化星，代表先天特质。', '紫微斗数', NOW()),
('自化', '某宫位宫干化到本宫的现象，有自给自足的意义。', '紫微斗数', NOW()),
('冲破', '化忌星冲破对宫的现象，主破坏和阻碍。', '紫微斗数', NOW()),
('左辅右弼', '紫微斗数中的辅星，主贵人助力和领导能力。', '紫微斗数', NOW()),
('文昌文曲', '紫微斗数中的文星，主文采学识和考试运。', '紫微斗数', NOW()),
('天魁天钺', '紫微斗数中的贵人星，主贵人提携和机遇。', '紫微斗数', NOW()),
('禄存', '紫微斗数中的财星，主正财和储蓄能力。', '紫微斗数', NOW()),
('擎羊陀罗', '紫微斗数中的煞星，主冲动和阻碍。', '紫微斗数', NOW()),
('火星铃星', '紫微斗数中的煞星，主急躁和变动。', '紫微斗数', NOW()),
('地空地劫', '紫微斗数中的空星，主损失和空虚。', '紫微斗数', NOW()),
('红鸾天喜', '紫微斗数中的桃花星，主婚姻和喜庆。', '紫微斗数', NOW()),
('天马', '紫微斗数中的动星，主变动和驿马。', '紫微斗数', NOW());

-- 添加历史命理大师信息
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(3, '陈抟老祖与紫微斗数', '陈抟（871-989年），道号扶摇子，后世尊称为陈抟老祖。相传为紫微斗数的创始人，同时也是《太极图》的传承者。陈抟精通易学、天文、历法，将古代天文学与命理学结合，创立了紫微斗数这一独特的命理体系。其理论基础源于《易经》和天文观测，具有深厚的哲学内涵。', '史学研究者', NOW()),
(3, '刘伯温的命理成就', '刘基（1311-1375年），字伯温，明朝开国功臣，著名的军事家、政治家，同时也是杰出的命理学家。精通奇门遁甲、紫微斗数、八字命理等多种术数。著有《滴天髓》、《金锁玉关》等命理典籍，对后世命理学发展影响深远。其预测准确，被誉为"前知五百年，后知五百年"。', '历史学者', NOW()),
(3, '袁天罡与李淳风', '袁天罡（生卒年不详，约590-665年）和李淳风（602-670年）是唐代著名的天文学家和命理学家。两人合著《推背图》，预言历史发展。袁天罡精通面相、八字等命理术，李淳风精通天文历法、奇门遁甲。两人都为唐朝钦天监，推算历法，观测天象，为皇室占卜吉凶。', '古代学者', NOW()),
(3, '邵雍与皇极经世', '邵雍（1011-1077年），字尧夫，北宋理学家、哲学家，也是著名的象数学家。创立《皇极经世》理论体系，以先天八卦和河图洛书为基础，推演天地变化规律。其数理哲学对后世命理学发展影响很大，被誉为"数学宗师"。邵雍的思想体现了"天人合一"的哲学观念。', '理学研究者', NOW()),
(3, '徐子平与子平八字', '徐子平（约907-960年），五代末宋初人，八字命理学的集大成者。在前人基础上完善了八字理论体系，确立了以日干为中心的十神理论，创立了格局分析方法。后世称八字命理为"子平术"，徐子平被尊为"八字之父"。其理论体系至今仍是八字命理的主要框架。', '命理史学家', NOW());

-- 完成数据插入
SELECT 'Initial data insertion completed successfully!' as message;

-- 添加现代命理学应用知识
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(4, '命理学在职业选择中的应用', '现代命理学可以通过分析个人的八字五行、紫微主星等信息，来判断适合的职业方向。水命人适合流动性工作如贸易、运输、媒体等；火命人适合能源、电子、餐饮等；木命人适合文创、教育、农林等；金命人适合金融、机械、法律等；土命人适合建筑、房地产、农业等。通过了解自己的命理特征，可以选择更适合的职业发展方向。', '职业规划师', NOW()),
(4, '八字合婚的现代意义', '八字合婚不是封建迷信，而是通过分析两人的性格特征和人生模式，来判断是否相配。现代八字合婚重点看：日主强弱是否互补、用神忌神是否冲突、性格特征是否协调、人生目标是否一致。通过命理分析可以了解双方的优缺点，在婚姻中互相理解和包容，提高婚姻质量。', '婚恋专家', NOW()),
(4, '紫微斗数与投资理财', '紫微斗数可以分析个人的财运特征和投资偏好。武曲星主正财，适合稳健投资；贪狼星主偏财，适合投机操作；天府星主储蓄，适合长期投资；破军星主变动，需谨慎理财。通过分析财帛宫的星曜组合，可以制定适合自己的理财策略，在合适的时机进行投资操作。', '理财专家', NOW()),
(4, '命理学趋吉避凶指南', '命理学的核心目的是趋吉避凶，通过提前了解自己的运势变化，在吉利的时候积极进取，在不利的时候谨慎保守。具体方法包括：了解自己的用神忌神，在用神当令时主动出击；分析大运流年变化，避开凶险年份的重大决策；利用五行调候改善环境；通过行善积德改善因果关系。命理学是人生的参考指南，不是宿命论。', '命理导师', NOW()),
(4, '五行与行业选择对照表', '根据五行属性选择行业：水行业-航运、水产、饮料、清洁、旅游、媒体、通讯等；火行业-能源、电力、餐饮、娱乐、广告、化工等；木行业-农林、教育、文化、出版、家具、纺织等；金行业-金融、机械、汽车、军工、五金、珠宝等；土行业-建筑、房地产、农业、陶瓷、殡葬、保险等。选择与自己命局五行相配的行业，有利于事业发展。', '行业分析师', NOW()),
(4, '现代人如何学习命理学', '学习命理学需要循序渐进：1、掌握基础理论（阴阳五行、天干地支）；2、学习排盘方法（八字、紫微排盘）；3、理解格局分析（格局高低、用神取用）；4、练习实战案例（从简单到复杂）；5、培养悟性思维（象数结合、灵活运用）。现代学习可以利用网络资源和软件工具，但要避免只记口诀不懂原理，要理论实践相结合。', '命理教师', NOW());

-- 添加五行职业对应表
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(4, '水性职业详解', '水主流动、智慧、沟通。适合职业：航海运输、水产渔业、饮料食品、清洁服务、旅游导游、媒体传播、网络通信、教育培训、医疗护理、娱乐服务、物流快递、贸易销售、酒店餐饮、美容化妆、心理咨询等。水性人聪明灵活，善于沟通，适合需要流动性和与人接触的工作。', '职业顾问', NOW()),
(4, '火性职业详解', '火主热情、创造、光明。适合职业：电力能源、电子科技、计算机IT、激光光学、燃料化工、餐饮烹饪、娱乐影视、广告策划、摄影照明、焊接冶炼、军工武器、烟草酒类、化妆美容、心理治疗、演讲培训等。火性人热情开朗，富有创造力，适合需要激情和创新的工作。', '职业顾问', NOW()),
(4, '木性职业详解', '木主生长、创造、仁慈。适合职业：农林园艺、木材家具、纺织服装、造纸印刷、教育文化、出版书店、文学艺术、设计策划、中医药材、素食餐饮、宗教慈善、环保绿化、儿童用品、文具办公、图书馆等。木性人富有同情心，具有艺术天赋，适合教育和文化创意工作。', '职业顾问', NOW()),
(4, '金性职业详解', '金主坚硬、锐利、权威。适合职业：金融银行、机械制造、汽车工业、钢铁冶金、五金工具、珠宝首饰、军警保安、法律司法、外科医生、精密仪器、钟表眼镜、健身器材、音响电器、网络硬件等。金性人意志坚强，善于决断，适合需要权威和精确的工作。', '职业顾问', NOW()),
(4, '土性职业详解', '土主稳重、包容、承载。适合职业：建筑工程、房地产业、农业种植、畜牧养殖、陶瓷制品、玻璃建材、仓储物流、殡葬服务、保险理财、管理咨询、社会服务、环卫清洁、地质勘探、考古文物、中介服务等。土性人踏实可靠，有责任心，适合需要稳定和管理的工作。', '职业顾问', NOW());

-- 添加命理学实用技巧
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(4, '如何通过命理改善人际关系', '通过命理分析可以了解自己和他人的性格特征，改善人际关系：1、了解对方的用神喜忌，投其所好；2、分析双方的五行配合，互补不足；3、选择合适的沟通时机和方式；4、理解对方的行为模式和思维方式；5、调整自己的言行举止，减少冲突。命理学帮助我们更好地理解人性，建立和谐的人际关系。', '人际关系专家', NOW()),
(4, '命理学看健康养生', '中医和命理学都基于五行理论，可以通过八字分析体质特征：木型人易肝胆问题，宜疏肝理气；火型人易心血管问题，宜清心降火；土型人易脾胃问题，宜健脾和胃；金型人易肺部问题，宜润肺清燥；水型人易肾脏问题，宜温肾助阳。根据自己的五行特征选择合适的养生方法，预防疾病发生。', '中医养生师', NOW()),
(4, '命理学在子女教育中的应用', '通过分析孩子的八字可以了解其天赋特长和性格特征，制定个性化的教育方案：水命孩子聪明好动，适合语言类学习；火命孩子热情活泼，适合艺术体育；木命孩子善良文静，适合文学音乐；金命孩子意志坚强，适合数理化；土命孩子稳重踏实，适合实用技能。因材施教，发挥孩子的天赋优势。', '教育专家', NOW()),
(4, '现代命理学的科学性探讨', '现代命理学不是迷信，而是一门综合性的人文学科，包含了天文、历法、哲学、心理学等多种知识。其科学性体现在：1、基于精确的天文历法计算；2、符合统计学规律和概率论；3、体现了系统论和信息论思想；4、与现代心理学有诸多相通之处；5、具有可验证性和实用性。我们应该以科学的态度研究和运用命理学。', '学者研究员', NOW());

-- 添加更多专业术语
INSERT INTO terminology (term, definition, category, created_at) VALUES
('用神', '八字中最需要的五行，能够平衡命局，决定人生成败的关键因素。', '八字命理', NOW()),
('调候', '根据出生季节调节寒暖燥湿，如夏生用水，冬生用火，春秋看具体情况。', '八字命理', NOW()),
('通关', '当两种五行相克时，用中间的五行化解矛盾，如金木相克用水通关。', '八字命理', NOW()),
('从格', '八字中日主极弱，顺从整体气势的特殊格局，分从强、从弱、从儿等。', '八字命理', NOW()),
('专旺格', '八字中某一五行极旺，其他五行很弱或没有的特殊格局。', '八字命理', NOW()),
('月德贵人', '根据出生月份确定的贵人星，主逢凶化吉，有贵人相助。', '八字命理', NOW()),
('华盖', '带有宗教、艺术、孤独色彩的神煞，主有艺术天赋但较孤独。', '八字命理', NOW()),
('桃花煞', '桃花星遇到煞星，主感情复杂，易有烂桃花或感情纠纷。', '紫微斗数', NOW()),
('科甲', '与文昌、文曲、化科相关的组合，主考试运佳，利求学功名。', '紫微斗数', NOW()),
('财荫夹印', '财星和印星夹住某宫位，主该宫位既有财运又有贵人扶持。', '紫微斗数', NOW()),
('禄马交驰', '禄存和天马在命盘中形成特殊配置，主奔波中得财，动中生财。', '紫微斗数', NOW()),
('空宫借星', '某宫位无主星时，借用对宫的星曜来论断该宫位的吉凶。', '紫微斗数', NOW()),
('星情', '星曜的性质和特征，包括庙旺平陷、五行属性、象征意义等。', '紫微斗数', NOW()),
('宫垣', '十二宫位的另一种称呼，强调宫位的空间概念和界限。', '紫微斗数', NOW()),
('飞化', '宫干四化的简称，指某宫位的天干化出四化星到其他宫位。', '紫微斗数', NOW());

-- 添加实用案例分析
INSERT INTO knowledge_entries (category_id, title, content, author, created_at) VALUES
(4, '命理学在重大决策中的应用案例', '案例：某企业家在2019年想要扩大投资，通过八字分析发现其2019年为偏印夺食之年，不利于投资理财，建议保守经营。结果2019年经济形势不好，很多同行都亏损，而他因为谨慎经营反而稳中有升。2020年进入正财当令的大运，果然投资顺利，收益颇丰。这说明命理学在商业决策中具有重要的参考价值。', '商业顾问', NOW()),
(4, '紫微斗数择偶标准分析', '通过紫微斗数可以分析适合的婚配对象：夫妻宫有紫微星者，适合找有领导能力的伴侣；有天机星者，适合找聪明灵活的伴侣；有太阳星者，适合找开朗大方的伴侣；有武曲星者，适合找务实能干的伴侣。还要看夫妻宫的四化变化，化禄主甜蜜，化权主个性强，化科主感情稳定，化忌主感情波折。综合分析可以找到最适合的伴侣类型。', '婚恋专家', NOW()),
(4, '八字看子女教育时机', '通过八字分析可以判断子女的学习能力和教育时机：子女宫有食神伤官者，子女聪明有才华，但需要正确引导；有印星者，子女好学上进，适合传统教育；有财星者，子女实用性强，适合技能培训；有官杀者，子女有领导潜质，需要严格管教。结合大运流年分析，可以选择最佳的教育投资时机和方式。', '教育顾问', NOW());

-- 完成现代应用数据插入
SELECT 'Modern metaphysics application data inserted successfully!' as message;

-- 添加用户反馈和评分系统
CREATE TABLE IF NOT EXISTS user_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    consultation_type VARCHAR(32) NOT NULL COMMENT '咨询类型',
    rating TINYINT NOT NULL COMMENT '评分(1-5)',
    feedback_text TEXT COMMENT '反馈内容',
    is_helpful TINYINT DEFAULT 1 COMMENT '是否有帮助',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_rating (user_id, rating),
    INDEX idx_type_rating (consultation_type, rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈评分表';

-- 添加AI学习优化表
CREATE TABLE IF NOT EXISTS ai_learning_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question_type VARCHAR(32) NOT NULL COMMENT '问题类型',
    user_question TEXT NOT NULL COMMENT '用户问题',
    ai_response TEXT NOT NULL COMMENT 'AI回复',
    user_satisfaction TINYINT COMMENT '用户满意度(1-5)',
    is_correct TINYINT COMMENT '回答是否正确',
    improvement_suggestion TEXT COMMENT '改进建议',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_question_type (question_type),
    INDEX idx_satisfaction (user_satisfaction)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI学习优化数据表';

-- 添加用户行为分析表
CREATE TABLE IF NOT EXISTS user_behavior_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    action_type VARCHAR(32) NOT NULL COMMENT '行为类型',
    action_detail JSON COMMENT '行为详情',
    session_id VARCHAR(64) COMMENT '会话ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_session (session_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为日志表';

-- 添加缓存配置表
CREATE TABLE IF NOT EXISTS cache_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cache_key VARCHAR(128) NOT NULL UNIQUE COMMENT '缓存键',
    cache_type VARCHAR(32) NOT NULL COMMENT '缓存类型',
    ttl INT DEFAULT 3600 COMMENT '过期时间(秒)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_cache_type (cache_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存配置表';

-- 添加API使用统计表
CREATE TABLE IF NOT EXISTS api_usage_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    endpoint VARCHAR(128) NOT NULL COMMENT 'API端点',
    method VARCHAR(10) NOT NULL COMMENT '请求方法',
    response_time INT COMMENT '响应时间(毫秒)',
    status_code INT COMMENT '状态码',
    error_message TEXT COMMENT '错误信息',
    request_date DATE NOT NULL COMMENT '请求日期',
    request_hour TINYINT NOT NULL COMMENT '请求小时',
    count INT DEFAULT 1 COMMENT '请求次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_endpoint_date (endpoint, request_date),
    INDEX idx_date_hour (request_date, request_hour)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API使用统计表';

-- 添加知识准确性验证表
CREATE TABLE IF NOT EXISTS knowledge_validation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    knowledge_id BIGINT NOT NULL COMMENT '知识条目ID',
    validator_id BIGINT COMMENT '验证者ID',
    validation_status TINYINT NOT NULL COMMENT '验证状态：1通过，2需修改，3拒绝',
    validation_notes TEXT COMMENT '验证说明',
    expert_level TINYINT COMMENT '专家等级(1-5)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_id) REFERENCES knowledge_entries(id),
    FOREIGN KEY (validator_id) REFERENCES users(id),
    INDEX idx_knowledge_status (knowledge_id, validation_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识准确性验证表';

-- 添加推荐算法配置
INSERT INTO cache_config (cache_key, cache_type, ttl, description) VALUES
('user_preference', 'redis', 86400, '用户偏好数据缓存'),
('knowledge_hot', 'redis', 3600, '热门知识缓存'),
('divination_result', 'redis', 7200, '测算结果缓存'),
('user_session', 'redis', 1800, '用户会话缓存'),
('ai_response', 'redis', 3600, 'AI回复缓存');

-- 添加性能优化索引
CREATE INDEX idx_knowledge_keywords ON knowledge_entries(keywords);
CREATE INDEX idx_birth_info_date ON birth_info(birth_date);
CREATE INDEX idx_divination_created ON divination_records(created_at);
CREATE INDEX idx_chat_created ON ai_chat_records(created_at);

-- 添加数据完整性检查
ALTER TABLE users ADD CONSTRAINT chk_gender CHECK (gender IN (0, 1, 2));
ALTER TABLE knowledge_entries ADD CONSTRAINT chk_confidence CHECK (confidence >= 0 AND confidence <= 1);
ALTER TABLE number_hexagrams ADD CONSTRAINT chk_lucky_level CHECK (lucky_level >= 1 AND lucky_level <= 9);
ALTER TABLE user_feedback ADD CONSTRAINT chk_rating CHECK (rating >= 1 AND rating <= 5);

-- 创建视图简化常用查询
CREATE VIEW v_user_stats AS
SELECT 
    u.id,
    u.nickname,
    u.points,
    u.level,
    COUNT(DISTINCT dr.id) as divination_count,
    COUNT(DISTINCT acr.id) as chat_count,
    COUNT(DISTINCT p.id) as post_count,
    COUNT(DISTINCT f.id) as favorite_count
FROM users u
LEFT JOIN divination_records dr ON u.id = dr.user_id
LEFT JOIN ai_chat_records acr ON u.id = acr.user_id
LEFT JOIN posts p ON u.id = p.user_id
LEFT JOIN favorites f ON u.id = f.user_id
GROUP BY u.id;

-- 创建热门知识视图
CREATE VIEW v_popular_knowledge AS
SELECT 
    ke.id,
    ke.title,
    ke.category_id,
    kc.name as category_name,
    COUNT(DISTINCT dr.user_id) as user_count,
    AVG(uf.rating) as avg_rating,
    COUNT(uf.id) as feedback_count
FROM knowledge_entries ke
JOIN knowledge_categories kc ON ke.category_id = kc.id
LEFT JOIN divination_records dr ON JSON_EXTRACT(dr.input_data, '$.knowledge_id') = ke.id
LEFT JOIN user_feedback uf ON uf.consultation_type = kc.code
GROUP BY ke.id
HAVING user_count > 0 OR feedback_count > 0
ORDER BY user_count DESC, avg_rating DESC;

-- 添加数据备份和归档策略说明
-- 注意：以下是建议的数据管理策略，需要在应用层实现

/*
数据管理策略建议：
1. 定期备份：
   - 每日增量备份用户数据和聊天记录
   - 每周全量备份知识库数据
   - 每月备份完整数据库

2. 数据归档：
   - 超过1年的聊天记录归档到历史表
   - 超过6个月的行为日志归档
   - 保留3个月的API统计数据

3. 数据清理：
   - 删除超过30天的临时缓存数据
   - 清理无效的用户会话数据
   - 定期清理测试用户数据

4. 数据安全：
   - 用户敏感信息加密存储
   - 定期更新数据库权限
   - 启用数据库审计日志
*/

SELECT 'Database optimization and monitoring tables created successfully!' as message;

-- ============================================
-- 优先级1：完善用户反馈和AI学习机制
-- ============================================

-- 扩展用户反馈表，添加更详细的反馈分类
ALTER TABLE user_feedback ADD COLUMN feedback_category VARCHAR(32) COMMENT '反馈分类：accuracy/speed/content/ui';
ALTER TABLE user_feedback ADD COLUMN specific_issue VARCHAR(64) COMMENT '具体问题';
ALTER TABLE user_feedback ADD COLUMN suggestion_implemented TINYINT DEFAULT 0 COMMENT '建议是否已实施';

-- 创建AI回答质量评估表
CREATE TABLE IF NOT EXISTS ai_response_quality (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    chat_record_id BIGINT NOT NULL COMMENT '对话记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question_category VARCHAR(32) NOT NULL COMMENT '问题分类',
    response_accuracy TINYINT COMMENT '回答准确性(1-5)',
    response_completeness TINYINT COMMENT '回答完整性(1-5)',
    response_helpfulness TINYINT COMMENT '回答有用性(1-5)',
    response_clarity TINYINT COMMENT '回答清晰度(1-5)',
    response_speed TINYINT COMMENT '回答速度(1-5)',
    overall_satisfaction TINYINT COMMENT '总体满意度(1-5)',
    improvement_areas JSON COMMENT '改进领域',
    positive_aspects JSON COMMENT '优点方面',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chat_record_id) REFERENCES ai_chat_records(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_category_quality (question_category, overall_satisfaction),
    INDEX idx_user_satisfaction (user_id, overall_satisfaction)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI回答质量评估表';

-- 创建知识点有效性跟踪表
CREATE TABLE IF NOT EXISTS knowledge_effectiveness (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    knowledge_id BIGINT NOT NULL COMMENT '知识条目ID',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    positive_feedback_count INT DEFAULT 0 COMMENT '正面反馈次数',
    negative_feedback_count INT DEFAULT 0 COMMENT '负面反馈次数',
    avg_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
    effectiveness_score DECIMAL(5,3) DEFAULT 0.000 COMMENT '有效性得分',
    needs_update TINYINT DEFAULT 0 COMMENT '是否需要更新',
    update_priority TINYINT DEFAULT 0 COMMENT '更新优先级(1-5)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (knowledge_id) REFERENCES knowledge_entries(id),
    INDEX idx_effectiveness (effectiveness_score DESC),
    INDEX idx_usage (usage_count DESC),
    INDEX idx_needs_update (needs_update, update_priority DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识点有效性跟踪表';

-- 创建AI学习样本表
CREATE TABLE IF NOT EXISTS ai_training_samples (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question TEXT NOT NULL COMMENT '用户问题',
    question_intent VARCHAR(64) NOT NULL COMMENT '问题意图',
    question_entities JSON COMMENT '问题实体',
    ideal_response TEXT NOT NULL COMMENT '理想回答',
    current_response TEXT COMMENT '当前AI回答',
    response_quality DECIMAL(3,2) COMMENT '回答质量得分',
    user_feedback TEXT COMMENT '用户反馈',
    expert_review TEXT COMMENT '专家评审',
    training_priority TINYINT DEFAULT 1 COMMENT '训练优先级(1-5)',
    is_used_for_training TINYINT DEFAULT 0 COMMENT '是否已用于训练',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_intent_quality (question_intent, response_quality),
    INDEX idx_training_priority (training_priority DESC, is_used_for_training),
    FULLTEXT INDEX idx_question (question)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI学习样本表';

-- 创建用户问题分类表
CREATE TABLE IF NOT EXISTS question_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(64) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(32) NOT NULL UNIQUE COMMENT '分类代码',
    parent_id INT NULL COMMENT '父分类ID',
    keywords TEXT COMMENT '关键词',
    confidence_threshold DECIMAL(3,2) DEFAULT 0.80 COMMENT '置信度阈值',
    sample_questions JSON COMMENT '示例问题',
    response_template TEXT COMMENT '回答模板',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES question_categories(id),
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户问题分类表';

-- 创建智能推荐记录表
CREATE TABLE IF NOT EXISTS smart_recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    recommendation_type VARCHAR(32) NOT NULL COMMENT '推荐类型',
    recommended_content JSON NOT NULL COMMENT '推荐内容',
    recommendation_reason TEXT COMMENT '推荐理由',
    confidence_score DECIMAL(3,2) COMMENT '推荐置信度',
    user_action VARCHAR(32) COMMENT '用户行为：viewed/clicked/ignored/dismissed',
    is_helpful TINYINT COMMENT '是否有帮助',
    feedback_text TEXT COMMENT '用户反馈',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_type (user_id, recommendation_type),
    INDEX idx_confidence (confidence_score DESC),
    INDEX idx_user_action (user_action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能推荐记录表';

-- 插入问题分类初始数据
INSERT INTO question_categories (category_name, category_code, keywords, sample_questions, response_template) VALUES
('八字命理咨询', 'bazi_consult', '八字,生辰,命理,五行,十神', 
 '["我的八字怎么样？", "帮我分析一下我的命格", "我的五行缺什么？"]',
 '根据您的出生信息，我来为您分析八字命理...'),

('紫微斗数咨询', 'ziwei_consult', '紫微,斗数,命盘,星耀,宫位',
 '["紫微斗数看我的运势", "我的命宫有什么星？", "今年的财运如何？"]',
 '根据紫微斗数排盘，您的命格特点是...'),

('事业职业咨询', 'career_consult', '工作,事业,职业,创业,升职',
 '["适合什么工作？", "什么时候换工作好？", "创业前景如何？"]',
 '从命理角度分析，您的事业发展方向...'),

('感情婚姻咨询', 'marriage_consult', '感情,婚姻,恋爱,配偶,桃花',
 '["什么时候结婚？", "我的另一半什么样？", "感情运势如何？"]',
 '关于您的感情婚姻，命理显示...'),

('财运投资咨询', 'wealth_consult', '财运,财富,投资,理财,偏财',
 '["今年财运怎么样？", "适合投资吗？", "什么时候能发财？"]',
 '从财运角度来看，您的财富状况...'),

('健康养生咨询', 'health_consult', '健康,养生,疾病,体质,五行调理',
 '["身体健康如何？", "需要注意什么疾病？", "如何养生？"]',
 '根据您的五行体质分析，健康方面需要注意...'),

('择日择时咨询', 'timing_consult', '择日,择时,吉日,时机,开业',
 '["什么时候结婚好？", "开业选什么日子？", "搬家吉日"]',
 '根据您的信息和所求事项，建议选择...'),

('风水布局咨询', 'fengshui_consult', '风水,布局,方位,住宅,办公室',
 '["家里风水如何？", "办公室怎么布置？", "买房选哪个方向？"]',
 '关于风水布局，建议您...'),

('起名改名咨询', 'naming_consult', '起名,改名,姓名,五格,三才',
 '["帮孩子起个名字", "我要改名字", "这个名字好不好？"]',
 '根据姓名学分析，为您推荐的名字...'),

('综合命理咨询', 'general_consult', '运势,命运,人生,综合分析',
 '["我的命运如何？", "人生运势分析", "综合看看我的情况"]',
 '综合您的命理信息，整体分析如下...');

-- 插入AI学习样本示例数据
INSERT INTO ai_training_samples (question, question_intent, question_entities, ideal_response, training_priority) VALUES
('我是1990年3月15日出生的，请帮我分析八字', 'bazi_analysis', 
 '{"birth_date": "1990-03-15", "request_type": "bazi_analysis"}',
 '根据您的出生日期1990年3月15日，我来为您分析八字命理。您的年柱为庚午年，春季出生，五行以金火为主。日主性格特点是...（需要具体时辰才能完整分析）', 5),

('我适合做什么工作？', 'career_guidance',
 '{"request_type": "career_advice"}',
 '要准确推荐适合的工作，需要了解您的出生信息来分析命理特征。一般来说，可以从五行特质、紫微主星等角度分析职业倾向。请提供您的出生年月日时，我来为您详细分析。', 4),

('今年财运怎么样？', 'wealth_forecast',
 '{"request_type": "wealth_prediction", "time_period": "this_year"}',
 '财运分析需要结合您的生辰八字和当年流年运势。请提供您的出生信息，我可以分析今年的财运走势，包括正财偏财、投资理财建议等。', 4),

('什么时候能结婚？', 'marriage_timing',
 '{"request_type": "marriage_prediction"}',
 '婚姻时机的预测需要分析您的八字中的夫妻星、桃花运势等。请提供出生年月日时，我来分析您的感情运势和适宜的结婚时机。', 3);

-- 创建反馈处理优先级表
CREATE TABLE IF NOT EXISTS feedback_priorities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    feedback_type VARCHAR(32) NOT NULL COMMENT '反馈类型',
    issue_category VARCHAR(32) NOT NULL COMMENT '问题分类',
    priority_level TINYINT NOT NULL COMMENT '优先级(1-5)',
    expected_response_time INT COMMENT '期望响应时间(小时)',
    auto_response_template TEXT COMMENT '自动回复模板',
    escalation_threshold INT COMMENT '升级阈值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_priority (priority_level DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈处理优先级表';

-- 插入反馈处理优先级配置
INSERT INTO feedback_priorities (feedback_type, issue_category, priority_level, expected_response_time, auto_response_template) VALUES
('accuracy', 'wrong_prediction', 5, 2, '感谢您的反馈，我们会立即核查预测准确性问题'),
('accuracy', 'incomplete_answer', 4, 4, '我们注意到回答不够完整，正在改进中'),
('speed', 'slow_response', 3, 8, '我们正在优化系统响应速度'),
('content', 'outdated_info', 4, 6, '感谢指出信息更新问题，我们会及时修正'),
('ui', 'interface_bug', 2, 12, '界面问题已记录，将在下次更新中修复'),
('ui', 'usability_issue', 1, 24, '用户体验建议已收到，感谢您的意见');

-- 创建AI学习进度跟踪表
CREATE TABLE IF NOT EXISTS ai_learning_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    learning_session_id VARCHAR(64) NOT NULL COMMENT '学习会话ID',
    learning_type VARCHAR(32) NOT NULL COMMENT '学习类型',
    data_source VARCHAR(32) NOT NULL COMMENT '数据来源',
    training_samples_count INT DEFAULT 0 COMMENT '训练样本数量',
    accuracy_before DECIMAL(5,3) COMMENT '训练前准确率',
    accuracy_after DECIMAL(5,3) COMMENT '训练后准确率',
    improvement_rate DECIMAL(5,3) COMMENT '改进率',
    training_duration INT COMMENT '训练耗时(分钟)',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    INDEX idx_session (learning_session_id),
    INDEX idx_type_status (learning_type, status),
    INDEX idx_created_at (created_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI学习进度跟踪表';

-- 创建用户满意度趋势表
CREATE TABLE IF NOT EXISTS user_satisfaction_trends (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    date DATE NOT NULL COMMENT '日期',
    category VARCHAR(32) NOT NULL COMMENT '分类',
    total_responses INT DEFAULT 0 COMMENT '总回复数',
    avg_satisfaction DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均满意度',
    satisfaction_distribution JSON COMMENT '满意度分布',
    improvement_suggestions JSON COMMENT '改进建议汇总',
    action_items JSON COMMENT '行动项目',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (date, category),
    INDEX idx_date_satisfaction (date, avg_satisfaction DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户满意度趋势表';

-- 插入初始知识点有效性数据
INSERT INTO knowledge_effectiveness (knowledge_id, usage_count, positive_feedback_count, avg_rating, effectiveness_score)
SELECT 
    id as knowledge_id,
    FLOOR(RAND() * 100) + 10 as usage_count,
    FLOOR(RAND() * 50) + 5 as positive_feedback_count,
    ROUND(3.5 + RAND() * 1.5, 2) as avg_rating,
    ROUND((RAND() * 0.5 + 0.7), 3) as effectiveness_score
FROM knowledge_entries
LIMIT 50;

-- 创建智能提示和建议表
CREATE TABLE IF NOT EXISTS smart_suggestions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    trigger_condition VARCHAR(128) NOT NULL COMMENT '触发条件',
    suggestion_type VARCHAR(32) NOT NULL COMMENT '建议类型',
    suggestion_content TEXT NOT NULL COMMENT '建议内容',
    target_audience VARCHAR(64) COMMENT '目标用户群',
    priority_score DECIMAL(3,2) DEFAULT 1.00 COMMENT '优先级得分',
    effectiveness_rate DECIMAL(3,2) DEFAULT 0.00 COMMENT '有效率',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_trigger (trigger_condition),
    INDEX idx_type_priority (suggestion_type, priority_score DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能提示和建议表';

-- 插入智能提示初始数据
INSERT INTO smart_suggestions (trigger_condition, suggestion_type, suggestion_content, target_audience, priority_score) VALUES
('user_first_visit', 'welcome_guide', '欢迎使用命理咨询AI！建议先完善您的出生信息，这样我能为您提供更准确的分析。', 'new_users', 5.00),
('incomplete_birth_info', 'data_completion', '您的出生信息不完整，补充完整的年月日时信息可以获得更精准的命理分析。', 'all_users', 4.50),
('low_satisfaction_feedback', 'service_improvement', '很抱歉这次的回答没有满足您的期望，您可以提供更多详细信息，我会重新为您分析。', 'dissatisfied_users', 4.80),
('frequent_career_questions', 'career_specialization', '看起来您对事业发展很关注，建议深入了解适合您的行业和职业方向。', 'career_focused', 3.50),
('multiple_prediction_requests', 'comprehensive_analysis', '您咨询了多个方面，建议做一次全面的命理分析，可以更好地规划人生。', 'comprehensive_users', 4.00);

SELECT 'AI learning and feedback system enhancement completed!' as message;

-- ============================================
-- 优先级2：添加缓存系统提升性能
-- ============================================

-- 扩展缓存配置表，添加更多缓存策略
ALTER TABLE cache_config ADD COLUMN cache_strategy VARCHAR(32) DEFAULT 'LRU' COMMENT '缓存策略：LRU/LFU/FIFO';
ALTER TABLE cache_config ADD COLUMN max_memory_mb INT DEFAULT 256 COMMENT '最大内存使用(MB)';
ALTER TABLE cache_config ADD COLUMN hit_rate_target DECIMAL(3,2) DEFAULT 0.85 COMMENT '目标命中率';
ALTER TABLE cache_config ADD COLUMN warmup_queries JSON COMMENT '预热查询';

-- 创建缓存命中率统计表
CREATE TABLE IF NOT EXISTS cache_hit_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    cache_key VARCHAR(128) NOT NULL COMMENT '缓存键',
    cache_type VARCHAR(32) NOT NULL COMMENT '缓存类型',
    hit_count INT DEFAULT 0 COMMENT '命中次数',
    miss_count INT DEFAULT 0 COMMENT '未命中次数',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    hit_rate DECIMAL(5,3) DEFAULT 0.000 COMMENT '命中率',
    avg_response_time INT COMMENT '平均响应时间(毫秒)',
    last_accessed_at TIMESTAMP COMMENT '最后访问时间',
    date DATE NOT NULL COMMENT '统计日期',
    hour TINYINT NOT NULL COMMENT '统计小时',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (cache_key, date, hour),
    INDEX idx_cache_type_date (cache_type, date),
    INDEX idx_hit_rate (hit_rate DESC),
    INDEX idx_last_accessed (last_accessed_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存命中率统计表';

-- 创建数据库查询性能监控表
CREATE TABLE IF NOT EXISTS query_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    query_hash VARCHAR(64) NOT NULL COMMENT '查询哈希',
    query_type VARCHAR(32) NOT NULL COMMENT '查询类型',
    table_names VARCHAR(255) COMMENT '涉及的表名',
    execution_time INT NOT NULL COMMENT '执行时间(毫秒)',
    rows_examined INT COMMENT '扫描行数',
    rows_sent INT COMMENT '返回行数',
    query_plan JSON COMMENT '查询计划',
    is_slow_query TINYINT DEFAULT 0 COMMENT '是否慢查询',
    optimization_suggestion TEXT COMMENT '优化建议',
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_query_hash (query_hash),
    INDEX idx_execution_time (execution_time DESC),
    INDEX idx_slow_query (is_slow_query, executed_at DESC),
    INDEX idx_table_names (table_names)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库查询性能监控表';

-- 创建热点数据预加载配置表
CREATE TABLE IF NOT EXISTS data_preload_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    data_type VARCHAR(32) NOT NULL COMMENT '数据类型',
    table_name VARCHAR(64) NOT NULL COMMENT '表名',
    selection_criteria JSON NOT NULL COMMENT '筛选条件',
    preload_schedule VARCHAR(32) DEFAULT 'daily' COMMENT '预加载计划',
    priority_level TINYINT DEFAULT 1 COMMENT '优先级(1-5)',
    cache_duration INT DEFAULT 3600 COMMENT '缓存时长(秒)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    last_preload_at TIMESTAMP NULL COMMENT '最后预加载时间',
    preload_size_mb DECIMAL(8,2) COMMENT '预加载数据大小(MB)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_data_type (data_type),
    INDEX idx_priority (priority_level DESC),
    INDEX idx_schedule (preload_schedule)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='热点数据预加载配置表';

-- 创建系统性能监控表
CREATE TABLE IF NOT EXISTS system_performance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(64) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(10,3) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(16) COMMENT '指标单位',
    threshold_warning DECIMAL(10,3) COMMENT '警告阈值',
    threshold_critical DECIMAL(10,3) COMMENT '严重阈值',
    current_status ENUM('normal', 'warning', 'critical') DEFAULT 'normal',
    server_instance VARCHAR(32) COMMENT '服务器实例',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_time (metric_name, recorded_at DESC),
    INDEX idx_status (current_status),
    INDEX idx_server (server_instance)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统性能监控表';

-- 创建CDN缓存配置表
CREATE TABLE IF NOT EXISTS cdn_cache_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    resource_type VARCHAR(32) NOT NULL COMMENT '资源类型',
    url_pattern VARCHAR(255) NOT NULL COMMENT 'URL模式',
    cache_duration INT NOT NULL COMMENT '缓存时长(秒)',
    cache_level VARCHAR(32) DEFAULT 'public' COMMENT '缓存级别',
    compression_enabled TINYINT DEFAULT 1 COMMENT '是否启用压缩',
    cache_headers JSON COMMENT '缓存头配置',
    purge_triggers TEXT COMMENT '清除触发条件',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_resource_type (resource_type),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CDN缓存配置表';

-- 插入缓存配置扩展数据
UPDATE cache_config SET 
    cache_strategy = 'LRU',
    max_memory_mb = 512,
    hit_rate_target = 0.90,
    warmup_queries = '["SELECT * FROM knowledge_entries WHERE category_id IN (1,2,3) LIMIT 100"]'
WHERE cache_key = 'knowledge_hot';

UPDATE cache_config SET 
    cache_strategy = 'LFU',
    max_memory_mb = 256,
    hit_rate_target = 0.95,
    warmup_queries = '["SELECT * FROM users WHERE level > 1"]'
WHERE cache_key = 'user_preference';

-- 插入热点数据预加载配置
INSERT INTO data_preload_config (data_type, table_name, selection_criteria, preload_schedule, priority_level, cache_duration) VALUES
('hot_knowledge', 'knowledge_entries', '{"category_id": [1,2,3,4], "confidence": {"$gte": 0.8}}', 'hourly', 5, 7200),
('popular_hexagrams', 'number_hexagrams', '{"lucky_level": {"$gte": 7}}', 'daily', 4, 14400),
('active_users', 'users', '{"level": {"$gte": 2}, "updated_at": {"$gte": "7_days_ago"}}', 'daily', 3, 3600),
('recent_consultations', 'ai_chat_records', '{"created_at": {"$gte": "24_hours_ago"}}', 'hourly', 4, 1800),
('trending_posts', 'posts', '{"likes_count": {"$gte": 10}, "created_at": {"$gte": "7_days_ago"}}', 'daily', 3, 7200);

-- 插入CDN缓存配置
INSERT INTO cdn_cache_config (resource_type, url_pattern, cache_duration, cache_level, compression_enabled, cache_headers) VALUES
('static_images', '/images/*', 86400, 'public', 1, '{"Cache-Control": "public, max-age=86400", "Vary": "Accept-Encoding"}'),
('css_files', '*.css', 604800, 'public', 1, '{"Cache-Control": "public, max-age=604800", "ETag": "auto"}'),
('js_files', '*.js', 604800, 'public', 1, '{"Cache-Control": "public, max-age=604800", "ETag": "auto"}'),
('api_responses', '/api/knowledge/*', 300, 'private', 1, '{"Cache-Control": "private, max-age=300", "Vary": "Authorization"}'),
('user_avatars', '/avatars/*', 3600, 'public', 1, '{"Cache-Control": "public, max-age=3600"}');

-- 创建缓存预热任务表
CREATE TABLE IF NOT EXISTS cache_warmup_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(64) NOT NULL COMMENT '任务名称',
    cache_keys JSON NOT NULL COMMENT '缓存键列表',
    warmup_queries JSON NOT NULL COMMENT '预热查询',
    schedule_cron VARCHAR(32) NOT NULL COMMENT '调度表达式',
    task_status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    last_execution_at TIMESTAMP NULL COMMENT '最后执行时间',
    next_execution_at TIMESTAMP NULL COMMENT '下次执行时间',
    execution_duration INT COMMENT '执行耗时(秒)',
    cached_items_count INT COMMENT '缓存项目数',
    error_message TEXT COMMENT '错误信息',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_status (task_status),
    INDEX idx_next_execution (next_execution_at),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存预热任务表';

-- 插入缓存预热任务
INSERT INTO cache_warmup_tasks (task_name, cache_keys, warmup_queries, schedule_cron, next_execution_at) VALUES
('知识库热点数据预热', 
 '["knowledge_hot", "knowledge_popular"]',
 '["SELECT * FROM knowledge_entries WHERE category_id IN (1,2,3,4) ORDER BY id LIMIT 200", "SELECT * FROM v_popular_knowledge LIMIT 100"]',
 '0 */2 * * *',
 DATE_ADD(NOW(), INTERVAL 2 HOUR)),

('用户数据预热',
 '["user_preference", "user_session"]',
 '["SELECT * FROM users WHERE level >= 2 AND updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)", "SELECT * FROM birth_info WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)"]',
 '0 6 * * *',
 DATE_ADD(NOW(), INTERVAL 1 DAY)),

('卦象数据预热',
 '["divination_result"]',
 '["SELECT * FROM number_hexagrams WHERE lucky_level >= 7", "SELECT * FROM hexagram_interpretation_rules LIMIT 500"]',
 '0 0 * * *',
 DATE_ADD(NOW(), INTERVAL 1 DAY));

-- 创建数据库连接池配置表
CREATE TABLE IF NOT EXISTS db_connection_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pool_name VARCHAR(32) NOT NULL COMMENT '连接池名称',
    database_type VARCHAR(16) NOT NULL COMMENT '数据库类型',
    host VARCHAR(128) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口',
    database_name VARCHAR(64) NOT NULL COMMENT '数据库名',
    min_connections INT DEFAULT 5 COMMENT '最小连接数',
    max_connections INT DEFAULT 50 COMMENT '最大连接数',
    connection_timeout INT DEFAULT 30 COMMENT '连接超时(秒)',
    idle_timeout INT DEFAULT 600 COMMENT '空闲超时(秒)',
    is_read_only TINYINT DEFAULT 0 COMMENT '是否只读',
    weight INT DEFAULT 1 COMMENT '权重',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pool_name (pool_name),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据库连接池配置表';

-- 创建读写分离配置表
CREATE TABLE IF NOT EXISTS read_write_split_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    operation_type VARCHAR(32) NOT NULL COMMENT '操作类型：SELECT/INSERT/UPDATE/DELETE',
    table_patterns TEXT COMMENT '表名模式',
    target_pool VARCHAR(32) NOT NULL COMMENT '目标连接池',
    conditions JSON COMMENT '条件配置',
    priority INT DEFAULT 1 COMMENT '优先级',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_operation (operation_type),
    INDEX idx_priority (priority DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='读写分离配置表';

-- 插入数据库连接池配置
INSERT INTO db_connection_config (pool_name, database_type, host, port, database_name, min_connections, max_connections, is_read_only, weight) VALUES
('master_pool', 'mysql', 'localhost', 3306, 'fortune_telling', 10, 50, 0, 1),
('slave_pool_1', 'mysql', 'slave1.localhost', 3306, 'fortune_telling', 5, 30, 1, 2),
('slave_pool_2', 'mysql', 'slave2.localhost', 3306, 'fortune_telling', 5, 30, 1, 2);

-- 插入读写分离配置
INSERT INTO read_write_split_config (operation_type, table_patterns, target_pool, priority) VALUES
('SELECT', 'knowledge_entries,knowledge_categories,terminology', 'slave_pool_1', 1),
('SELECT', 'number_hexagrams,hexagram_*', 'slave_pool_2', 1),
('SELECT', 'users,birth_info', 'slave_pool_1', 2),
('INSERT', '*', 'master_pool', 1),
('UPDATE', '*', 'master_pool', 1),
('DELETE', '*', 'master_pool', 1);

-- 创建性能优化建议表
CREATE TABLE IF NOT EXISTS performance_optimization_suggestions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    suggestion_type VARCHAR(32) NOT NULL COMMENT '建议类型',
    target_object VARCHAR(128) NOT NULL COMMENT '目标对象',
    current_performance DECIMAL(10,3) COMMENT '当前性能指标',
    expected_improvement DECIMAL(5,2) COMMENT '预期提升比例',
    optimization_method TEXT NOT NULL COMMENT '优化方法',
    implementation_difficulty TINYINT DEFAULT 1 COMMENT '实施难度(1-5)',
    estimated_effort_hours INT COMMENT '预估工时',
    priority_score DECIMAL(5,2) COMMENT '优先级得分',
    status ENUM('suggested', 'approved', 'implementing', 'completed', 'rejected') DEFAULT 'suggested',
    implemented_at TIMESTAMP NULL COMMENT '实施时间',
    actual_improvement DECIMAL(5,2) COMMENT '实际提升',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_type_priority (suggestion_type, priority_score DESC),
    INDEX idx_status (status),
    INDEX idx_difficulty (implementation_difficulty)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='性能优化建议表';

-- 插入性能优化建议
INSERT INTO performance_optimization_suggestions (suggestion_type, target_object, current_performance, expected_improvement, optimization_method, implementation_difficulty, priority_score) VALUES
('index_optimization', 'knowledge_entries.content', 250.5, 0.60, '为content字段添加FULLTEXT索引，优化文本搜索性能', 2, 8.5),
('query_optimization', 'user_stats_query', 150.2, 0.40, '使用视图v_user_stats替代复杂JOIN查询', 1, 7.8),
('cache_strategy', 'knowledge_hot_data', 89.3, 0.70, '实施多级缓存策略：L1本地缓存+L2Redis缓存', 3, 9.2),
('connection_pooling', 'database_connections', 45.6, 0.30, '优化连接池配置，增加最小连接数到15', 1, 6.5),
('data_partitioning', 'ai_chat_records', 320.1, 0.50, '按日期分区ai_chat_records表，提升查询性能', 4, 7.0),
('cdn_implementation', 'static_resources', 180.0, 0.80, '为静态资源配置CDN加速，减少服务器负载', 2, 8.8);

-- 添加性能监控指标配置
INSERT INTO system_performance (metric_name, metric_value, metric_unit, threshold_warning, threshold_critical, server_instance) VALUES
('cpu_usage_percent', 25.5, '%', 70.0, 90.0, 'web-server-1'),
('memory_usage_percent', 42.3, '%', 80.0, 95.0, 'web-server-1'),
('disk_io_rate', 156.7, 'MB/s', 500.0, 800.0, 'db-server-1'),
('response_time_avg', 85.2, 'ms', 200.0, 500.0, 'web-server-1'),
('concurrent_users', 128, 'count', 500, 1000, 'web-server-1'),
('cache_hit_rate', 0.923, 'ratio', 0.80, 0.60, 'cache-server-1'),
('db_connections_active', 15, 'count', 40, 45, 'db-server-1'),
('error_rate_percent', 0.2, '%', 1.0, 5.0, 'web-server-1');

-- 创建自动扩容配置表
CREATE TABLE IF NOT EXISTS auto_scaling_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    resource_type VARCHAR(32) NOT NULL COMMENT '资源类型',
    metric_name VARCHAR(64) NOT NULL COMMENT '监控指标',
    scale_up_threshold DECIMAL(10,3) NOT NULL COMMENT '扩容阈值',
    scale_down_threshold DECIMAL(10,3) NOT NULL COMMENT '缩容阈值',
    min_instances INT DEFAULT 1 COMMENT '最小实例数',
    max_instances INT DEFAULT 10 COMMENT '最大实例数',
    scale_step INT DEFAULT 1 COMMENT '扩缩容步长',
    cooldown_period INT DEFAULT 300 COMMENT '冷却期(秒)',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_resource_type (resource_type),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动扩容配置表';

-- 插入自动扩容配置
INSERT INTO auto_scaling_config (resource_type, metric_name, scale_up_threshold, scale_down_threshold, min_instances, max_instances, scale_step) VALUES
('web_server', 'cpu_usage_percent', 70.0, 30.0, 2, 8, 1),
('web_server', 'memory_usage_percent', 80.0, 40.0, 2, 8, 1),
('cache_server', 'cache_hit_rate', 0.70, 0.90, 1, 4, 1),
('db_connection_pool', 'db_connections_active', 35.0, 10.0, 5, 50, 5);

SELECT 'Cache system and performance optimization completed!' as message;