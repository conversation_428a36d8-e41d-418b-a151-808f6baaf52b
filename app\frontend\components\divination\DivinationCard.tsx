import React from 'react';
import styled from 'styled-components';
import { Card, Text, Tag, Button } from '../base';

interface DivinationCardProps {
  title: string;
  description: string;
  type: 'iching' | 'tarot' | 'astrology';
  confidence: number;
  onSelect: () => void;
}

const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  gap: 16px;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: translateY(-4px);
  }
`;

const IconWrapper = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${props => props.theme.borderRadius.circle};
  background-color: ${props => props.theme.colors.primary}11;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
`;

const ConfidenceBar = styled.div<{ confidence: number }>`
  width: 100%;
  height: 4px;
  background-color: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.small};
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    width: ${props => props.confidence}%;
    height: 100%;
    background-color: ${props => 
      props.confidence > 80 ? props.theme.colors.success :
      props.confidence > 60 ? props.theme.colors.primary :
      props.confidence > 40 ? props.theme.colors.warning :
      props.theme.colors.error
    };
    transition: width 0.3s ease-in-out;
  }
`;

const DivinationIcon = styled.img`
  width: 24px;
  height: 24px;
`;

const DivinationCard: React.FC<DivinationCardProps> = ({
  title,
  description,
  type,
  confidence,
  onSelect
}) => {
  const getIcon = () => {
    switch (type) {
      case 'iching':
        return '/icons/iching.svg';
      case 'tarot':
        return '/icons/tarot.svg';
      case 'astrology':
        return '/icons/astrology.svg';
      default:
        return '/icons/default.svg';
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'iching':
        return '易经';
      case 'tarot':
        return '塔罗';
      case 'astrology':
        return '占星';
      default:
        return '占卜';
    }
  };

  return (
    <StyledCard elevation="medium" onClick={onSelect}>
      <IconWrapper>
        <DivinationIcon src={getIcon()} alt={type} />
      </IconWrapper>
      
      <div>
        <Text variant="h2">{title}</Text>
        <Text variant="body" color="secondary" style={{ marginTop: 8 }}>
          {description}
        </Text>
      </div>

      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <Tag>{getTypeLabel()}</Tag>
        <Text variant="small" color="secondary">
          准确度: {confidence}%
        </Text>
      </div>

      <ConfidenceBar confidence={confidence} />

      <Button variant="outline" size="small">
        开始占卜
      </Button>
    </StyledCard>
  );
};

export default DivinationCard; 