from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from app.core.model_manager import model_manager
from app.models.user import User
from app.core.deps import get_current_user
from pathlib import Path
import shutil

router = APIRouter(
    prefix="/api",
    tags=["Community"],
    responses={
        401: {"description": "认证失败"},
        403: {"description": "权限不足"},
        404: {"description": "资源不存在"},
        500: {"description": "服务器内部错误"}
    }
)

class PostBase(BaseModel):
    title: str = Field(..., description="帖子标题")
    content: str = Field(..., description="帖子内容")
    category: str = Field(..., description="帖子分类")
    tags: List[str] = Field(default=[], description="帖子标签")

    class Config:
        schema_extra = {
            "example": {
                "title": "分享我的八字分析经验",
                "content": "最近研究了一下八字分析...",
                "category": "experience",
                "tags": ["八字", "命理", "经验分享"]
            }
        }

class PostCreate(PostBase):
    author_id: int = Field(..., description="作者ID")

class Post(PostBase):
    id: int = Field(..., description="帖子ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    likes: int = Field(default=0, description="点赞数")
    comments: List[Dict[str, Any]] = Field(default=[], description="评论列表")

@router.post(
    "/posts",
    response_model=Dict[str, Any],
    summary="创建帖子",
    description="创建一个新的社区帖子",
    response_description="返回创建的帖子信息"
)
async def create_post(
    post: PostCreate,
    current_user: User = Depends(get_current_user)
):
    """创建帖子"""
    try:
        new_post = model_manager.create_post(post)
        return {
            "status": "success",
            "post": new_post
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get(
    "/posts",
    response_model=Dict[str, Any],
    summary="获取帖子列表",
    description="""
    获取社区帖子列表，支持分页和筛选。
    
    筛选条件：
    - category: 帖子分类
    - tag: 帖子标签
    
    分页参数：
    - page: 页码，从1开始
    - size: 每页数量，默认20
    """,
    response_description="返回帖子列表和总数"
)
async def list_posts(
    category: Optional[str] = None,
    tag: Optional[str] = None,
    page: int = Field(1, ge=1, description="页码"),
    size: int = Field(20, ge=1, le=100, description="每页数量")
):
    """获取帖子列表"""
    try:
        posts = model_manager.get_posts(category, tag, page, size)
        return {
            "status": "success",
            "posts": posts,
            "total": model_manager.get_posts_count(category, tag),
            "page": page,
            "size": size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post(
    "/posts/{post_id}/comments",
    response_model=Dict[str, Any],
    summary="添加评论",
    description="为指定帖子添加评论",
    response_description="返回创建的评论信息"
)
async def add_comment(
    post_id: int = Field(..., description="帖子ID"),
    content: str = Field(..., description="评论内容"),
    current_user: User = Depends(get_current_user)
):
    """添加评论"""
    try:
        comment = model_manager.add_comment(post_id, current_user.id, content)
        return {
            "status": "success",
            "comment": comment
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post(
    "/users/me/avatar",
    response_model=Dict[str, str],
    summary="上传头像",
    description="上传用户头像图片",
    response_description="返回头像URL"
)
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """上传用户头像"""
    try:
        file_path = f"static/avatars/{current_user.id}_{file.filename}"
        with Path(file_path).open("wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 更新用户头像路径
        model_manager.update_user_avatar(current_user.id, file_path)
        
        return {
            "status": "success",
            "avatar_url": f"/static/avatars/{current_user.id}_{file.filename}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put(
    "/users/me",
    response_model=Dict[str, Any],
    summary="更新用户信息",
    description="更新当前用户的个人信息",
    response_description="返回更新后的用户信息"
)
async def update_user_profile(
    profile_update: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """更新用户信息"""
    try:
        updated_user = model_manager.update_user(
            current_user.id,
            profile_update
        )
        return {
            "status": "success",
            "user": updated_user
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 