from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, ForeignKey, Text, JSON, Enum as SQLEnum, func
from sqlalchemy.orm import relationship, Session
from pydantic import BaseModel, Field, EmailStr, validator
from enum import Enum
from .database import Base
from app.core.exceptions import *

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    PREMIUM = "premium"

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    BANNED = "banned"

# SQLAlchemy Models
class UserDB(Base):
    """用户数据库模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    role = Column(SQLEnum(UserRole), default=UserRole.USER)
    status = Column(SQLEnum(UserStatus), default=UserStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    last_login = Column(DateTime, nullable=True)

    # 关系定义
    messages = relationship("ChatMessageDB", back_populates="user", cascade="all, delete-orphan")
    usage_records = relationship("UserUsageDB", back_populates="user", cascade="all, delete-orphan")

    @classmethod
    async def create(cls, db: Session, user_data: Dict[str, Any]) -> "UserDB":
        """创建新用户"""
        user = cls(**user_data)
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    @classmethod
    async def get_by_id(cls, db: Session, user_id: int) -> Optional["UserDB"]:
        """通过ID获取用户"""
        return db.query(cls).filter(cls.id == user_id).first()

    @classmethod
    async def get_by_email(cls, db: Session, email: str) -> Optional["UserDB"]:
        """通过邮箱获取用户"""
        return db.query(cls).filter(cls.email == email).first()

    async def update(self, db: Session, **kwargs) -> "UserDB":
        """更新用户信息"""
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.updated_at = datetime.now()
        db.commit()
        db.refresh(self)
        return self

    async def delete(self, db: Session) -> None:
        """删除用户"""
        db.delete(self)
        db.commit()

class ChatMessageDB(Base):
    """聊天消息数据库模型"""
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    query = Column(Text, nullable=False)
    response = Column(Text, nullable=False)
    model_name = Column(String(50), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    tokens_used = Column(Integer, default=0)
    processing_time = Column(Float, default=0.0)
    feedback_score = Column(Integer, nullable=True)
    feedback_text = Column(Text, nullable=True)

    # 关系定义
    user = relationship("UserDB", back_populates="messages")

    @classmethod
    async def create(cls, db: Session, message_data: Dict[str, Any]) -> "ChatMessageDB":
        """创建新消息"""
        message = cls(**message_data)
        db.add(message)
        db.commit()
        db.refresh(message)
        return message

    @classmethod
    async def get_user_messages(cls, db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List["ChatMessageDB"]:
        """获取用户的消息历史"""
        return db.query(cls).filter(cls.user_id == user_id).offset(skip).limit(limit).all()

    async def update_feedback(self, db: Session, score: int, text: Optional[str] = None) -> "ChatMessageDB":
        """更新反馈信息"""
        self.feedback_score = score
        if text:
            self.feedback_text = text
        db.commit()
        db.refresh(self)
        return self

class KnowledgeBaseDB(Base):
    """知识库数据库模型"""
    __tablename__ = "knowledge_base"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(50), index=True)
    source = Column(String(100), index=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    embedding = Column(JSON, nullable=True)
    metadata = Column(JSON, default=dict)

    @classmethod
    async def create(cls, db: Session, knowledge_data: Dict[str, Any]) -> "KnowledgeBaseDB":
        """创建新知识条目"""
        knowledge = cls(**knowledge_data)
        db.add(knowledge)
        db.commit()
        db.refresh(knowledge)
        return knowledge

    @classmethod
    async def search(cls, db: Session, query: str, category: Optional[str] = None) -> List["KnowledgeBaseDB"]:
        """搜索知识库"""
        q = db.query(cls)
        if category:
            q = q.filter(cls.category == category)
        # TODO: 实现向量搜索
        return q.all()

    async def update(self, db: Session, **kwargs) -> "KnowledgeBaseDB":
        """更新知识条目"""
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.updated_at = datetime.now()
        db.commit()
        db.refresh(self)
        return self

    async def delete(self, db: Session) -> None:
        """删除知识条目"""
        db.delete(self)
        db.commit()

class UserUsageDB(Base):
    """用户使用记录数据库模型"""
    __tablename__ = "user_usage"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    date = Column(DateTime, default=datetime.now)
    requests_count = Column(Integer, default=0)
    tokens_used = Column(Integer, default=0)
    model_name = Column(String(50), nullable=False)

    # 关系定义
    user = relationship("UserDB", back_populates="usage_records")

    @classmethod
    async def create_or_update(cls, db: Session, user_id: int, model_name: str, tokens: int = 1) -> "UserUsageDB":
        """创建或更新使用记录"""
        today = datetime.now().date()
        record = db.query(cls).filter(
            cls.user_id == user_id,
            cls.model_name == model_name,
            cls.date >= today
        ).first()

        if record:
            record.requests_count += 1
            record.tokens_used += tokens
        else:
            record = cls(
                user_id=user_id,
                model_name=model_name,
                requests_count=1,
                tokens_used=tokens
            )
            db.add(record)

        db.commit()
        db.refresh(record)
        return record

    @classmethod
    async def get_user_stats(cls, db: Session, user_id: int) -> Dict[str, Any]:
        """获取用户使用统计"""
        stats = db.query(
            cls.model_name,
            func.sum(cls.requests_count).label("total_requests"),
            func.sum(cls.tokens_used).label("total_tokens")
        ).filter(cls.user_id == user_id).group_by(cls.model_name).all()

        return {
            model: {
                "requests": requests,
                "tokens": tokens
            } for model, requests, tokens in stats
        }

class SystemLogDB(Base):
    """系统日志数据库模型"""
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    level = Column(String(20), nullable=False)
    message = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.now)
    module = Column(String(50), nullable=False)
    trace = Column(Text, nullable=True)

    @classmethod
    async def log(cls, db: Session, level: str, message: str, module: str, trace: Optional[str] = None) -> "SystemLogDB":
        """记录日志"""
        log = cls(
            level=level,
            message=message,
            module=module,
            trace=trace
        )
        db.add(log)
        db.commit()
        db.refresh(log)
        return log

    @classmethod
    async def get_recent_logs(cls, db: Session, level: Optional[str] = None, limit: int = 100) -> List["SystemLogDB"]:
        """获取最近的日志"""
        query = db.query(cls)
        if level:
            query = query.filter(cls.level == level)
        return query.order_by(cls.timestamp.desc()).limit(limit).all()

class ModelConfigDB(Base):
    """模型配置数据库模型"""
    __tablename__ = "model_configs"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    model_name = Column(String(50), unique=True, nullable=False)
    parameters = Column(JSON, nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_active = Column(Boolean, default=True)
    description = Column(Text, nullable=True)

    @classmethod
    async def get_active_config(cls, db: Session, model_name: str) -> Optional["ModelConfigDB"]:
        """获取活动配置"""
        return db.query(cls).filter(
            cls.model_name == model_name,
            cls.is_active == True
        ).first()

    @classmethod
    async def create_or_update(cls, db: Session, model_name: str, parameters: Dict[str, Any], description: Optional[str] = None) -> "ModelConfigDB":
        """创建或更新配置"""
        config = db.query(cls).filter(cls.model_name == model_name).first()
        if config:
            config.parameters = parameters
            if description:
                config.description = description
            config.updated_at = datetime.now()
        else:
            config = cls(
                model_name=model_name,
                parameters=parameters,
                description=description
            )
            db.add(config)

        db.commit()
        db.refresh(config)
        return config

    async def deactivate(self, db: Session) -> None:
        """停用配置"""
        self.is_active = False
        db.commit()

# Pydantic Models for API
class UserCreate(BaseModel):
    """用户创建模型"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=6)

    class Config:
        schema_extra = {
            "example": {
                "username": "john_doe",
                "email": "<EMAIL>",
                "password": "secure_password123"
            }
        }

class UserUpdate(BaseModel):
    """用户更新模型"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None

    class Config:
        schema_extra = {
            "example": {
                "username": "john_doe_updated",
                "email": "<EMAIL>",
                "role": "premium"
            }
        }

class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: str
    role: UserRole
    status: UserStatus
    created_at: datetime
    last_login: Optional[datetime]

    class Config:
        orm_mode = True

class ChatMessageCreate(BaseModel):
    """聊天消息创建模型"""
    query: str = Field(..., min_length=1)
    model_name: str = Field(..., min_length=1)

    class Config:
        schema_extra = {
            "example": {
                "query": "什么是人工智能？",
                "model_name": "chatglm-6b"
            }
        }

class ChatMessageResponse(BaseModel):
    """聊天消息响应模型"""
    id: int
    query: str
    response: str
    model_name: str
    created_at: datetime
    tokens_used: int
    processing_time: float
    feedback_score: Optional[int]

    class Config:
        orm_mode = True

class KnowledgeBaseCreate(BaseModel):
    """知识库创建模型"""
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    category: str = Field(..., min_length=1, max_length=50)
    source: str = Field(..., min_length=1, max_length=100)
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        schema_extra = {
            "example": {
                "title": "人工智能简介",
                "content": "人工智能是计算机科学的一个分支...",
                "category": "技术",
                "source": "AI教程",
                "metadata": {
                    "author": "张三",
                    "tags": ["AI", "机器学习"]
                }
            }
        }

class KnowledgeBaseResponse(BaseModel):
    """知识库响应模型"""
    id: int
    title: str
    content: str
    category: str
    source: str
    created_at: datetime
    updated_at: datetime
    metadata: Optional[Dict[str, Any]]

    class Config:
        orm_mode = True 