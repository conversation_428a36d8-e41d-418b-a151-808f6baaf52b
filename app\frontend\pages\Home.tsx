import React, { useState } from 'react';
import styled from 'styled-components';
import { Text, Button, Input } from '../components/base';
import DivinationCard from '../components/divination/DivinationCard';
import ResultCard from '../components/divination/ResultCard';

const PageContainer = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  padding: 40px 20px;
`;

const ContentWrapper = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 48px;
`;

const SearchSection = styled.div`
  max-width: 600px;
  margin: 0 auto 48px;
`;

const SearchInput = styled(Input)`
  margin-bottom: 16px;
`;

const CardsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
`;

const divinationTypes = [
  {
    title: '八字算命',
    description: '基于生辰八字的命理分析，揭示人生轨迹与潜在机遇。',
    type: 'iching',
    confidence: 85
  },
  {
    title: '塔罗牌占卜',
    description: '通过塔罗牌阵解读当前困惑，指引未来方向。',
    type: 'tarot',
    confidence: 78
  },
  {
    title: '紫微斗数',
    description: '紫微斗数精准分析，全面解读人生格局。',
    type: 'astrology',
    confidence: 92
  },
  {
    title: '六爻预测',
    description: '传统六爻预测，解答具体事项疑惑。',
    type: 'iching',
    confidence: 88
  }
];

const Home: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleTypeSelect = (type: string) => {
    setSelectedType(type);
    setShowResult(true);
  };

  const mockResult = {
    title: '八字算命综合解读',
    type: 'iching',
    confidence: 85,
    interpretation: {
      summary: '您的八字显示近期事业发展潜力较大，但需要注意健康方面的投资。',
      details: [
        {
          title: '事业运势',
          content: '今年是事业上升期，适合大胆尝试新的发展方向。'
        },
        {
          title: '财运分析',
          content: '财运稳定，可以考虑进行稳健的投资理财。'
        }
      ],
      recommendations: [
        {
          text: '近期可以考虑参加职业培训，提升专业技能。',
          priority: 'high'
        },
        {
          text: '注意作息规律，保持充足的休息时间。',
          priority: 'medium'
        }
      ]
    },
    timestamp: new Date().toLocaleString()
  };

  return (
    <PageContainer>
      <ContentWrapper>
        <Header>
          <Text variant="h1">AI 智能算命</Text>
          <Text variant="body" color="secondary" style={{ marginTop: 16 }}>
            结合传统命理与现代AI技术，为您提供精准的命理解读
          </Text>
        </Header>

        <SearchSection>
          <SearchInput
            placeholder="输入您想解答的问题..."
            value={searchQuery}
            onChange={handleSearch}
          />
          <Button variant="primary" size="large" style={{ width: '100%' }}>
            开始解答
          </Button>
        </SearchSection>

        {!showResult ? (
          <>
            <Text variant="h2" style={{ marginBottom: 24 }}>
              选择占卜方式
            </Text>
            <CardsGrid>
              {divinationTypes.map((type, index) => (
                <DivinationCard
                  key={index}
                  {...type}
                  onSelect={() => handleTypeSelect(type.type)}
                />
              ))}
            </CardsGrid>
          </>
        ) : (
          <ResultCard
            {...mockResult}
            onShare={() => console.log('Share result')}
            onSave={() => console.log('Save result')}
          />
        )}
      </ContentWrapper>
    </PageContainer>
  );
};

export default Home; 