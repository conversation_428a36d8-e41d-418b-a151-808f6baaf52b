import os
import logging
import torch
from torch.optim import Adam<PERSON>
from torch.nn import CrossEntropyLoss
from transformers import (
    get_linear_schedule_with_warmup,
    get_scheduler,
    AutoModelForCausalLM,
    AutoTokenizer
)
from tqdm import tqdm
import numpy as np
from typing import Dict, List, Optional, Tuple
from .data_processor import ChatbotDataset, create_dataloader
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Trainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device("cpu")  # 使用CPU训练
        
        # 初始化模型和tokenizer
        logger.info(f"Loading model {config.model_name}...")
        self.tokenizer = AutoTokenizer.from_pretrained(config.model_name)
        self.model = AutoModelForCausalLM.from_pretrained(config.model_name, 
                                                         low_cpu_mem_usage=True)
        
        # 移动模型到设备
        self.model.to(self.device)
        logger.info("Model loaded successfully")
        
        # 初始化数据集
        logger.info("Initializing datasets...")
        self.train_dataset = ChatbotDataset(
            config.train_data_path,
            self.tokenizer,
            config.max_length,
            is_training=True
        )
        
        self.eval_dataset = ChatbotDataset(
            config.eval_data_path,
            self.tokenizer,
            config.max_length,
            is_training=True
        )
        
        # 创建数据加载器
        self.train_dataloader = create_dataloader(
            self.train_dataset,
            config.batch_size,
            shuffle=True,
            num_workers=0  # CPU模式下使用0个worker
        )
        
        self.eval_dataloader = create_dataloader(
            self.eval_dataset,
            config.batch_size,
            shuffle=False,
            num_workers=0  # CPU模式下使用0个worker
        )
        
        # 设置优化器
        self.optimizer = AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=config.weight_decay
        )
        
        # 设置学习率调度器
        num_training_steps = len(self.train_dataloader) * config.max_epochs
        self.scheduler = get_scheduler(
            "linear",
            optimizer=self.optimizer,
            num_warmup_steps=config.warmup_steps,
            num_training_steps=num_training_steps
        )
        
        # 初始化早停
        self.best_eval_loss = float('inf')
        self.no_improvement_count = 0
        
    def train(self):
        """训练模型"""
        logger.info("Starting training...")

        best_eval_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.config.max_epochs):
            self.model.train()
            total_loss = 0
            num_steps = 0

            # 训练循环
            progress_bar = tqdm(self.train_dataloader, desc=f"Epoch {epoch + 1}")
            for step, batch in enumerate(progress_bar):
                # 将数据移到设备
                batch = {k: v.to(self.device) for k, v in batch.items()}

                # 前向传播
                outputs = self.model(**batch)
                loss = outputs.loss

                # 梯度累积
                loss = loss / self.config.gradient_accumulation_steps
                loss.backward()

                total_loss += loss.item()
                num_steps += 1

                # 每隔gradient_accumulation_steps步更新一次参数
                if (step + 1) % self.config.gradient_accumulation_steps == 0:
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.config.max_grad_norm
                    )

                    # 优化器步进
                    self.optimizer.step()
                    self.scheduler.step()
                    self.optimizer.zero_grad()

                progress_bar.set_postfix({"loss": loss.item() * self.config.gradient_accumulation_steps})

                # 记录训练信息
                if step % self.config.logging_steps == 0:
                    current_lr = self.scheduler.get_last_lr()[0] if self.scheduler else self.config.learning_rate
                    logger.info(f"Epoch {epoch+1}, Step {step}: loss={loss.item():.4f}, lr={current_lr:.2e}")

                    # 记录到wandb（如果可用）
                    try:
                        import wandb
                        wandb.log({
                            "train_loss": loss.item(),
                            "learning_rate": current_lr,
                            "epoch": epoch,
                            "step": step
                        })
                    except ImportError:
                        pass

            # 计算平均训练损失
            avg_train_loss = total_loss / num_steps
            logger.info(f"Epoch {epoch + 1} - Average training loss: {avg_train_loss:.4f}")

            # 评估模型
            if hasattr(self, 'eval_dataloader') and self.eval_dataloader:
                eval_loss = self.evaluate()
                logger.info(f"Epoch {epoch + 1} - Evaluation loss: {eval_loss:.4f}")

                # 早停检查
                if eval_loss < best_eval_loss:
                    best_eval_loss = eval_loss
                    patience_counter = 0
                    # 保存最佳模型
                    self.save_checkpoint(f"best_model_epoch_{epoch+1}")
                    logger.info(f"New best model saved with eval loss: {eval_loss:.4f}")
                else:
                    patience_counter += 1
                    logger.info(f"No improvement for {patience_counter} epochs")

                # 早停
                if patience_counter >= self.config.early_stopping_patience:
                    logger.info(f"Early stopping triggered after {patience_counter} epochs without improvement")
                    break

                # 记录到wandb
                try:
                    import wandb
                    wandb.log({
                        "eval_loss": eval_loss,
                        "best_eval_loss": best_eval_loss,
                        "epoch": epoch
                    })
                except ImportError:
                    pass

            # 定期保存检查点
            if (epoch + 1) % 5 == 0:
                self.save_checkpoint(f"checkpoint_epoch_{epoch+1}")

        logger.info("Training completed!")
        return best_eval_loss

    def evaluate(self):
        """评估模型"""
        self.model.eval()
        total_eval_loss = 0
        num_eval_steps = 0

        with torch.no_grad():
            for batch in tqdm(self.eval_dataloader, desc="Evaluating"):
                batch = {k: v.to(self.device) for k, v in batch.items()}
                outputs = self.model(**batch)
                loss = outputs.loss
                total_eval_loss += loss.item()
                num_eval_steps += 1

        avg_eval_loss = total_eval_loss / num_eval_steps
        return avg_eval_loss
                    self.model.train()
                    
                    # 早停检查
                    if eval_loss < self.best_eval_loss:
                        self.best_eval_loss = eval_loss
                        self.no_improvement_count = 0
                        self.save_checkpoint("best_model")
                    else:
                        self.no_improvement_count += 1
                        
                    if self.no_improvement_count >= self.config.early_stopping_patience:
                        logger.info("Early stopping triggered")
                        return
                        
                # 保存检查点
                if step % self.config.save_steps == 0:
                    self.save_checkpoint(f"checkpoint-{epoch}-{step}")
            
            # 每个epoch结束后的平均损失
            avg_loss = total_loss / len(self.train_dataloader)
            logger.info(f"Epoch {epoch + 1} average loss: {avg_loss:.4f}")
        
    def evaluate(self) -> float:
        """评估模型"""
        self.model.eval()
        total_loss = 0
        
        with torch.no_grad():
            for batch in tqdm(self.eval_dataloader, desc="Evaluating"):
                batch = {k: v.to(self.device) for k, v in batch.items()}
                outputs = self.model(**batch)
                loss = outputs.loss
                total_loss += loss.item()
        
        avg_loss = total_loss / len(self.eval_dataloader)
        logger.info(f"Evaluation loss: {avg_loss:.4f}")
        
        return avg_loss
        
    def save_checkpoint(self, checkpoint_name: str):
        """保存模型检查点"""
        output_dir = Path(self.config.output_dir) / checkpoint_name
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存模型
        self.model.save_pretrained(output_dir)
        self.tokenizer.save_pretrained(output_dir)
        
        # 保存优化器和调度器状态
        torch.save(self.optimizer.state_dict(), output_dir / "optimizer.pt")
        torch.save(self.scheduler.state_dict(), output_dir / "scheduler.pt")
        
        logger.info(f"Saved checkpoint to {output_dir}")
        
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        # 加载模型和tokenizer
        self.model = AutoModelForCausalLM.from_pretrained(checkpoint_path)
        self.tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)
        self.model.to(self.device)
        
        # 加载优化器和调度器状态
        optimizer_path = Path(checkpoint_path) / "optimizer.pt"
        scheduler_path = Path(checkpoint_path) / "scheduler.pt"
        
        if optimizer_path.exists():
            self.optimizer.load_state_dict(torch.load(optimizer_path))
        if scheduler_path.exists():
            self.scheduler.load_state_dict(torch.load(scheduler_path))
            
        logger.info(f"Loaded checkpoint from {checkpoint_path}") 