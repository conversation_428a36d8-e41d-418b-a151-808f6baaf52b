from typing import Dict, List, Optional, Any
import json
from datetime import datetime
from pathlib import Path
import logging
from pydantic import BaseModel, Field

class KnowledgeEntry(BaseModel):
    """知识条目模型"""
    id: str
    category: str
    subcategory: str
    content: str
    keywords: List[str]
    relations: Dict[str, List[str]]
    metadata: Dict[str, Any]
    
class KnowledgeBase:
    """算命知识库管理类"""
    
    def __init__(self, base_path: str = "data/fortune_knowledge"):
        """
        初始化知识库
        
        Args:
            base_path: 知识库文件存储路径
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.entries: Dict[str, KnowledgeEntry] = {}
        self.logger = logging.getLogger(__name__)
        self._load_knowledge()
    
    def _load_knowledge(self):
        """加载所有知识条目"""
        for file in self.base_path.glob("*.json"):
            try:
                with open(file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    entry = KnowledgeEntry(**data)
                    self.entries[entry.id] = entry
            except Exception as e:
                self.logger.error(f"Failed to load knowledge file {file}: {e}")
    
    def add_entry(self, entry: KnowledgeEntry) -> bool:
        """
        添加新的知识条目
        
        Args:
            entry: 知识条目对象
            
        Returns:
            bool: 是否添加成功
        """
        try:
            file_path = self.base_path / f"{entry.id}.json"
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(entry.dict(), f, ensure_ascii=False, indent=2)
            self.entries[entry.id] = entry
            return True
        except Exception as e:
            self.logger.error(f"Failed to add entry {entry.id}: {e}")
            return False
    
    def get_entry(self, entry_id: str) -> Optional[KnowledgeEntry]:
        """
        获取知识条目
        
        Args:
            entry_id: 条目ID
            
        Returns:
            Optional[KnowledgeEntry]: 知识条目对象或None
        """
        return self.entries.get(entry_id)
    
    def search(self, 
              keywords: List[str] = None, 
              category: str = None, 
              subcategory: str = None) -> List[KnowledgeEntry]:
        """
        搜索知识条目
        
        Args:
            keywords: 关键词列表
            category: 类别
            subcategory: 子类别
            
        Returns:
            List[KnowledgeEntry]: 匹配的知识条目列表
        """
        results = []
        for entry in self.entries.values():
            if category and entry.category != category:
                continue
            if subcategory and entry.subcategory != subcategory:
                continue
            if keywords:
                if not any(kw in entry.keywords for kw in keywords):
                    continue
            results.append(entry)
        return results
    
    def update_entry(self, entry_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新知识条目
        
        Args:
            entry_id: 条目ID
            updates: 更新内容
            
        Returns:
            bool: 是否更新成功
        """
        if entry_id not in self.entries:
            return False
        
        try:
            entry = self.entries[entry_id]
            updated_data = entry.dict()
            updated_data.update(updates)
            updated_data["metadata"]["last_updated"] = datetime.now().isoformat()
            
            updated_entry = KnowledgeEntry(**updated_data)
            file_path = self.base_path / f"{entry_id}.json"
            
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(updated_entry.dict(), f, ensure_ascii=False, indent=2)
            
            self.entries[entry_id] = updated_entry
            return True
        except Exception as e:
            self.logger.error(f"Failed to update entry {entry_id}: {e}")
            return False
    
    def delete_entry(self, entry_id: str) -> bool:
        """
        删除知识条目
        
        Args:
            entry_id: 条目ID
            
        Returns:
            bool: 是否删除成功
        """
        if entry_id not in self.entries:
            return False
        
        try:
            file_path = self.base_path / f"{entry_id}.json"
            file_path.unlink()
            del self.entries[entry_id]
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete entry {entry_id}: {e}")
            return False
    
    def get_categories(self) -> Dict[str, List[str]]:
        """
        获取所有类别和子类别
        
        Returns:
            Dict[str, List[str]]: 类别和对应的子类别列表
        """
        categories = {}
        for entry in self.entries.values():
            if entry.category not in categories:
                categories[entry.category] = set()
            categories[entry.category].add(entry.subcategory)
        return {k: list(v) for k, v in categories.items()}
    
    def export_knowledge(self, output_path: str):
        """
        导出知识库
        
        Args:
            output_path: 导出文件路径
        """
        try:
            data = {
                "metadata": {
                    "exported_at": datetime.now().isoformat(),
                    "total_entries": len(self.entries)
                },
                "entries": [entry.dict() for entry in self.entries.values()]
            }
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to export knowledge base: {e}")
            raise

# 使用示例
if __name__ == "__main__":
    # 初始化知识库
    kb = KnowledgeBase()
    
    # 添加示例条目
    entry = KnowledgeEntry(
        id="bazi_001",
        category="bazi",
        subcategory="theory",
        content="八字是由年、月、日、时四柱组成，每柱包含天干和地支...",
        keywords=["八字", "命理", "四柱"],
        relations={
            "prerequisites": ["天干地支"],
            "related_topics": ["五行", "纳音"]
        },
        metadata={
            "source": "传统命理著作",
            "confidence": 0.95,
            "last_updated": datetime.now().isoformat()
        }
    )
    
    kb.add_entry(entry)
    
    # 搜索示例
    results = kb.search(keywords=["八字"], category="bazi")
    for result in results:
        print(f"Found: {result.content[:50]}...") 