from typing import Optional, Union, List, Dict, Any
from pathlib import Path
import json
from haystack.document_stores import FAISSDocumentStore, InMemoryDocumentStore
from haystack.schema import Document
from app.core.config import settings
from app.utils.preprocessor import DocumentPreprocessor
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
import logging

logger = logging.getLogger(__name__)

class DocumentStoreManager:
    """
    文档存储管理器，支持 FAISS 和 InMemory 存储
    """
    
    def __init__(
        self,
        store_type: str = settings.DOCUMENT_STORE_TYPE,
        faiss_index_path: Optional[str] = settings.FAISS_INDEX_PATH,
        embedding_dim: int = settings.EMBEDDING_DIM,
        preprocessor: Optional[DocumentPreprocessor] = None,
    ):
        self.store_type = store_type
        self.faiss_index_path = faiss_index_path
        self.embedding_dim = embedding_dim
        self.document_store = self._initialize_store()
        self.preprocessor = preprocessor or DocumentPreprocessor()
        self.index = faiss.IndexFlatL2(768)  # Using 768 dimensions for sentence-transformers
        self.documents = []
        self.embedder = SentenceTransformer('paraphrase-MiniLM-L6-v2')
    
    def _initialize_store(self):
        """
        初始化文档存储
        """
        if self.store_type == "faiss":
            return FAISSDocumentStore(
                faiss_index_path=self.faiss_index_path,
                embedding_dim=self.embedding_dim,
                return_embedding=True,
                similarity="cosine",
            )
        return InMemoryDocumentStore(
            embedding_dim=self.embedding_dim,
            return_embedding=True,
            similarity="cosine",
        )
    
    def add_documents(
        self,
        documents: Union[List[dict], List[Document]],
        index: Optional[str] = None,
        batch_size: int = 1000,
    ) -> None:
        """
        添加文档到存储
        """
        if not documents:
            return
        
        processed_docs = []
        
        # 处理文档
        for doc in documents:
            if isinstance(doc, dict):
                content = doc.get("content", "")
                meta = doc.get("meta", {})
                # 对文档进行分块处理
                chunks = self.preprocessor.process_document(content, meta)
                processed_docs.extend(chunks)
            else:
                processed_docs.append(doc)
        
        # 批量写入文档
        for i in range(0, len(processed_docs), batch_size):
            batch = processed_docs[i:i + batch_size]
            self.document_store.write_documents(batch, index=index)
        
        try:
            # 生成文档向量
            texts = [doc.content for doc in processed_docs]
            embeddings = self.embedder.encode(texts)
            
            # 添加到 FAISS 索引
            self.index.add(np.array(embeddings))
            
            # 保存文档
            self.documents.extend(processed_docs)
            
            logger.info(f"Successfully added {len(processed_docs)} documents")
            
        except Exception as e:
            logger.error(f"Error adding documents: {str(e)}", exc_info=True)
            raise
    
    def add_knowledge_base(
        self,
        knowledge_file: Union[str, Path],
        index: Optional[str] = None,
    ) -> None:
        """
        从 JSON 文件加载知识库
        """
        knowledge_file = Path(knowledge_file)
        if not knowledge_file.exists():
            raise FileNotFoundError(f"Knowledge file not found: {knowledge_file}")
        
        with knowledge_file.open("r", encoding="utf-8") as f:
            knowledge_base = json.load(f)
        
        documents = []
        for item in knowledge_base:
            # 使用预处理器处理知识库条目
            doc = self.preprocessor.process_knowledge_item(item)
            documents.append(doc)
        
        self.add_documents(documents, index=index)
    
    def get_document_store(self):
        """
        获取文档存储实例
        """
        return self.document_store
    
    def save(self) -> None:
        """
        保存文档存储（仅 FAISS）
        """
        if self.store_type == "faiss" and self.faiss_index_path:
            self.document_store.save(self.faiss_index_path)
    
    def get_document_count(self, index: Optional[str] = None) -> int:
        """
        获取文档数量
        """
        return self.document_store.get_document_count(index=index)
    
    def get_all_documents(
        self,
        index: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        return_embedding: bool = False,
    ) -> List[Document]:
        """
        获取所有文档
        """
        return self.document_store.get_all_documents(
            index=index,
            filters=filters,
            return_embedding=return_embedding,
        )
    
    def get_relevant_documents(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 5
    ) -> List[Document]:
        """
        检索相关文档
        
        Args:
            query: 查询文本
            filters: 过滤条件
            top_k: 返回文档数量
            
        Returns:
            相关文档列表
        """
        try:
            # 生成查询向量
            query_vector = self.embedder.encode([query])[0]
            
            # 搜索相似文档
            D, I = self.index.search(
                np.array([query_vector]),
                min(top_k, len(self.documents))
            )
            
            # 获取文档
            documents = [self.documents[i] for i in I[0]]
            
            # 应用过滤
            if filters:
                documents = [
                    doc for doc in documents
                    if all(
                        doc.meta.get(k) == v
                        for k, v in filters.items()
                    )
                ]
            
            return documents
            
        except Exception as e:
            logger.error(f"Error retrieving documents: {str(e)}", exc_info=True)
            raise
            
    def clear(self) -> None:
        """清空文档存储"""
        self.index = faiss.IndexFlatL2(768)
        self.documents = [] 