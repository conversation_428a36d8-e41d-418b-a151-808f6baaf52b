#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据模型
根据API接口文档定义的数据结构
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, date
from enum import Enum

# ===== 基础响应模型 =====

class APIResponse(BaseModel):
    """API标准响应格式"""
    status: str = Field(..., description="响应状态: success|error")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")

class PaginationInfo(BaseModel):
    """分页信息"""
    current_page: int = Field(..., description="当前页码")
    total_pages: int = Field(..., description="总页数")
    total_count: int = Field(..., description="总记录数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

# ===== 用户相关模型 =====

class UserInfo(BaseModel):
    """用户基础信息"""
    id: str = Field(..., description="用户唯一ID")
    openid: str = Field(..., description="微信OpenID")
    unionid: Optional[str] = Field(None, description="微信UnionID")
    nickname: str = Field(..., description="用户昵称")
    avatar_url: str = Field(..., description="头像URL")
    gender: int = Field(..., description="性别 (0:未知, 1:男, 2:女)")
    country: Optional[str] = Field(None, description="国家")
    province: Optional[str] = Field(None, description="省份")
    city: Optional[str] = Field(None, description="城市")
    language: Optional[str] = Field(None, description="语言")
    phone: Optional[str] = Field(None, description="手机号")
    email: Optional[str] = Field(None, description="邮箱")
    status: int = Field(..., description="用户状态 (0:正常, 1:禁用, 2:删除)")
    vip_level: int = Field(..., description="VIP等级 (0:普通, 1:VIP, 2:SVIP)")
    vip_expire_time: Optional[datetime] = Field(None, description="VIP过期时间")
    points: int = Field(..., description="用户积分")
    total_points: int = Field(..., description="累计积分")
    register_time: datetime = Field(..., description="注册时间")
    last_login_time: Optional[datetime] = Field(None, description="最后登录时间")
    login_count: int = Field(..., description="登录次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class WxLoginRequest(BaseModel):
    """微信登录请求"""
    code: str = Field(..., description="微信登录code")
    user_info: Optional[Dict[str, Any]] = Field(None, description="用户信息")

class WxLoginResponse(BaseModel):
    """微信登录响应"""
    user: UserInfo = Field(..., description="用户信息")
    token: str = Field(..., description="JWT Token")
    refresh_token: str = Field(..., description="刷新Token")
    expires_in: int = Field(..., description="Token过期时间(秒)")

class UserStatistics(BaseModel):
    """用户统计信息"""
    total_consultations: int = Field(..., description="总咨询次数")
    total_points_earned: int = Field(..., description="总获得积分")
    total_points_spent: int = Field(..., description="总消费积分")
    sign_in_days: int = Field(..., description="连续签到天数")
    last_sign_in: Optional[date] = Field(None, description="最后签到日期")

# ===== 出生信息模型 =====

class BaziInfo(BaseModel):
    """八字信息"""
    year_gan: str = Field(..., description="年干")
    year_zhi: str = Field(..., description="年支")
    month_gan: str = Field(..., description="月干")
    month_zhi: str = Field(..., description="月支")
    day_gan: str = Field(..., description="日干")
    day_zhi: str = Field(..., description="日支")
    hour_gan: str = Field(..., description="时干")
    hour_zhi: str = Field(..., description="时支")

class WuxingInfo(BaseModel):
    """五行信息"""
    jin: int = Field(..., description="金")
    mu: int = Field(..., description="木")
    shui: int = Field(..., description="水")
    huo: int = Field(..., description="火")
    tu: int = Field(..., description="土")

class BirthInfo(BaseModel):
    """出生信息"""
    id: str = Field(..., description="记录ID")
    user_id: str = Field(..., description="用户ID")
    name: str = Field(..., description="姓名")
    gender: str = Field(..., description="性别 (男/女)")
    birth_year: int = Field(..., description="出生年份")
    birth_month: int = Field(..., description="出生月份")
    birth_day: int = Field(..., description="出生日期")
    birth_hour: int = Field(..., description="出生小时")
    birth_minute: int = Field(..., description="出生分钟")
    birth_timezone: str = Field(default="Asia/Shanghai", description="时区")
    lunar_year: Optional[int] = Field(None, description="农历年份")
    lunar_month: Optional[int] = Field(None, description="农历月份")
    lunar_day: Optional[int] = Field(None, description="农历日期")
    lunar_leap_month: Optional[bool] = Field(None, description="是否闰月")
    zodiac: Optional[str] = Field(None, description="生肖")
    constellation: Optional[str] = Field(None, description="星座")
    lucky_number: Optional[int] = Field(None, description="幸运数字 (1-62)")
    bazi: Optional[BaziInfo] = Field(None, description="八字信息")
    wuxing: Optional[WuxingInfo] = Field(None, description="五行信息")
    is_verified: bool = Field(default=False, description="是否已验证")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class BirthInfoRequest(BaseModel):
    """保存出生信息请求"""
    name: str = Field(..., min_length=1, max_length=50, description="姓名")
    gender: str = Field(..., regex="^(男|女)$", description="性别")
    birth_year: int = Field(..., ge=1900, le=2100, description="出生年份")
    birth_month: int = Field(..., ge=1, le=12, description="出生月份")
    birth_day: int = Field(..., ge=1, le=31, description="出生日期")
    birth_hour: int = Field(..., ge=0, le=23, description="出生小时")
    birth_minute: int = Field(default=0, ge=0, le=59, description="出生分钟")
    zodiac: Optional[str] = Field(None, description="生肖")
    lucky_number: Optional[int] = Field(None, ge=1, le=62, description="幸运数字")

# ===== AI聊天模型 =====

class MessageType(str, Enum):
    """消息类型"""
    USER = "user"
    AI = "ai"
    SYSTEM = "system"

class ChatMessage(BaseModel):
    """聊天消息"""
    id: str = Field(..., description="消息ID")
    user_id: str = Field(..., description="用户ID")
    session_id: str = Field(..., description="会话ID")
    message_type: MessageType = Field(..., description="消息类型")
    content: str = Field(..., description="消息内容")
    intent: Optional[str] = Field(None, description="意图识别结果")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    attachments: Optional[Dict[str, Any]] = Field(None, description="附件信息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    feedback: Optional[Dict[str, Any]] = Field(None, description="用户反馈")
    is_deleted: bool = Field(default=False, description="是否删除")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class ChatSession(BaseModel):
    """聊天会话"""
    id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="会话标题")
    status: str = Field(..., description="状态 (active/ended/archived)")
    message_count: int = Field(..., description="消息数量")
    total_tokens: int = Field(..., description="总token消耗")
    total_cost: float = Field(..., description="总成本")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    last_activity: datetime = Field(..., description="最后活动时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class AIChatRequest(BaseModel):
    """AI聊天请求"""
    message: str = Field(..., min_length=1, max_length=2000, description="用户消息")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: Optional[List[Dict[str, Any]]] = Field(None, description="上下文消息")
    intent_hint: Optional[str] = Field(None, description="意图提示")
    analysis_type: Optional[str] = Field(None, description="分析类型")

class ActionItem(BaseModel):
    """操作项"""
    type: str = Field(..., description="操作类型")
    text: str = Field(..., description="显示文本")
    url: Optional[str] = Field(None, description="跳转链接")
    icon: Optional[str] = Field(None, description="图标")

class AIChatResponse(BaseModel):
    """AI聊天响应"""
    session_id: str = Field(..., description="会话ID")
    message_id: str = Field(..., description="消息ID")
    response: Dict[str, Any] = Field(..., description="AI回复")
    analysis: Optional[Dict[str, Any]] = Field(None, description="分析结果")
    usage: Dict[str, Any] = Field(..., description="使用统计")

# ===== 命理分析模型 =====

class AnalysisType(str, Enum):
    """分析类型"""
    BAZI = "bazi"
    YIJING = "yijing"
    FENGSHUI = "fengshui"
    WUXING = "wuxing"
    ZIWEI = "ziwei"
    MARRIAGE = "marriage"

class AnalysisRecord(BaseModel):
    """分析记录"""
    id: str = Field(..., description="记录ID")
    user_id: str = Field(..., description="用户ID")
    analysis_type: AnalysisType = Field(..., description="分析类型")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    result_data: Dict[str, Any] = Field(..., description="分析结果")
    score: Optional[float] = Field(None, description="评分 (0-100)")
    summary: Optional[str] = Field(None, description="总结")
    suggestions: Optional[List[str]] = Field(None, description="建议")
    points_cost: int = Field(..., description="消耗积分")
    processing_time: Optional[int] = Field(None, description="处理时间(ms)")
    is_shared: bool = Field(default=False, description="是否分享")
    share_count: int = Field(default=0, description="分享次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

# ===== 积分系统模型 =====

class PointType(str, Enum):
    """积分类型"""
    EARN = "earn"
    SPEND = "spend"
    REFUND = "refund"
    EXPIRE = "expire"

class PointSource(str, Enum):
    """积分来源"""
    SIGN_IN = "sign_in"
    SHARE = "share"
    INVITE = "invite"
    PURCHASE = "purchase"
    ANALYSIS = "analysis"
    REFUND = "refund"

class PointRecord(BaseModel):
    """积分记录"""
    id: str = Field(..., description="记录ID")
    user_id: str = Field(..., description="用户ID")
    type: PointType = Field(..., description="类型")
    amount: int = Field(..., description="积分数量")
    source: PointSource = Field(..., description="来源")
    description: str = Field(..., description="描述")
    related_id: Optional[str] = Field(None, description="关联ID")
    balance_before: int = Field(..., description="操作前余额")
    balance_after: int = Field(..., description="操作后余额")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    status: str = Field(..., description="状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class PointBalance(BaseModel):
    """积分余额"""
    current_balance: int = Field(..., description="当前余额")
    total_earned: int = Field(..., description="累计获得")
    total_spent: int = Field(..., description="累计消费")
    pending_points: int = Field(..., description="待到账积分")
    expiring_soon: List[Dict[str, Any]] = Field(..., description="即将过期积分")

# ===== 知识库模型 =====

class KnowledgeDocument(BaseModel):
    """知识库文档"""
    id: str = Field(..., description="文档ID")
    title: str = Field(..., description="文档标题")
    content: str = Field(..., description="文档内容")
    summary: str = Field(..., description="文档摘要")
    category: str = Field(..., description="分类")
    tags: List[str] = Field(..., description="标签列表")
    keywords: List[str] = Field(..., description="关键词")
    difficulty_level: str = Field(..., description="难度等级")
    content_type: str = Field(..., description="内容类型")
    source: Optional[str] = Field(None, description="来源")
    author: Optional[str] = Field(None, description="作者")
    version: str = Field(..., description="版本")
    status: str = Field(..., description="状态")
    view_count: int = Field(..., description="查看次数")
    like_count: int = Field(..., description="点赞次数")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class KnowledgeSearchRequest(BaseModel):
    """知识库搜索请求"""
    query: str = Field(..., min_length=1, max_length=200, description="搜索查询")
    search_type: str = Field(default="hybrid", description="搜索类型")
    category: Optional[str] = Field(None, description="分类筛选")
    tags: Optional[List[str]] = Field(None, description="标签筛选")
    difficulty: Optional[str] = Field(None, description="难度筛选")
    limit: int = Field(default=10, ge=1, le=50, description="返回数量")
    include_content: bool = Field(default=False, description="是否包含完整内容")
    user_context: Optional[Dict[str, Any]] = Field(None, description="用户上下文")

class KnowledgeSearchResult(BaseModel):
    """知识库搜索结果"""
    id: str = Field(..., description="文档/问答对ID")
    type: str = Field(..., description="类型")
    title: str = Field(..., description="标题")
    summary: str = Field(..., description="摘要")
    content: Optional[str] = Field(None, description="内容")
    category: str = Field(..., description="分类")
    tags: List[str] = Field(..., description="标签")
    relevance_score: float = Field(..., description="相关性评分")
    confidence: float = Field(..., description="置信度")
    source: Optional[str] = Field(None, description="来源")
    last_updated: datetime = Field(..., description="最后更新时间")
    view_count: int = Field(..., description="查看次数")
    helpful_count: int = Field(..., description="有用评价数")

# ===== 配置模型 =====

class AIConfig(BaseModel):
    """AI配置"""
    model_name: str = Field(..., description="模型名称")
    version: str = Field(..., description="版本")
    max_tokens: int = Field(..., description="最大token数")
    temperature: float = Field(..., description="温度参数")
    top_p: float = Field(..., description="top_p参数")
    frequency_penalty: float = Field(..., description="频率惩罚")
    presence_penalty: float = Field(..., description="存在惩罚")

class QuickAction(BaseModel):
    """快捷操作"""
    id: str = Field(..., description="操作ID")
    title: str = Field(..., description="标题")
    icon: str = Field(..., description="图标")
    description: str = Field(..., description="描述")
    requires_birth_info: bool = Field(..., description="是否需要出生信息")
    points_cost: int = Field(..., description="积分消费")
