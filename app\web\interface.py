import gradio as gr
from typing import Dict, Any
from app.core.pipeline import <PERSON><PERSON>ipeline
from app.utils.document_store import DocumentStoreManager

class WebInterface:
    def __init__(self):
        self.qa_pipeline = QAPipeline()
        self.document_store_manager = DocumentStoreManager()
        self.chat_history = []
    
    def answer_question(
        self,
        query: str,
        history: list,
        top_k: int = 3,
    ) -> tuple[str, list]:
        """
        处理问答并更新聊天历史
        """
        try:
            # 运行问答管道
            result = self.qa_pipeline.run(
                query=query,
                params={"top_k": top_k},
            )
            
            # 获取答案和相关文档
            answer = result["answers"][0]["answer"]
            documents = result["documents"]
            
            # 格式化参考文档
            references = "\n\n参考文档：\n" + "\n".join(
                f"- {doc.get('meta', {}).get('filename', '未知文档')}"
                for doc in documents
            )
            
            # 更新聊天历史
            history.append((query, answer + references))
            
            return "", history
        except Exception as e:
            return f"发生错误：{str(e)}", history
    
    def upload_knowledge(self, file: gr.File) -> str:
        """
        上传知识库文件
        """
        try:
            self.document_store_manager.add_knowledge_base(file.name)
            self.qa_pipeline.update_embeddings()
            return "知识库上传成功！"
        except Exception as e:
            return f"上传失败：{str(e)}"
    
    def upload_document(self, file: gr.File) -> str:
        """
        上传文档文件
        """
        try:
            with open(file.name, "r", encoding="utf-8") as f:
                content = f.read()
            
            self.document_store_manager.add_documents([{
                "content": content,
                "meta": {"filename": file.name}
            }])
            
            self.qa_pipeline.update_embeddings()
            return "文档上传成功！"
        except Exception as e:
            return f"上传失败：{str(e)}"
    
    def create_interface(self) -> gr.Blocks:
        """
        创建 Gradio 界面
        """
        with gr.Blocks(title="ChatGLM 知识库问答系统") as interface:
            gr.Markdown("# ChatGLM 知识库问答系统")
            
            with gr.Row():
                with gr.Column(scale=3):
                    chatbot = gr.Chatbot(
                        [],
                        elem_id="chatbot",
                        height=600,
                    )
                    
                    with gr.Row():
                        query = gr.Textbox(
                            show_label=False,
                            placeholder="请输入您的问题...",
                            lines=2,
                        )
                        submit_btn = gr.Button("发送")
                
                with gr.Column(scale=1):
                    top_k = gr.Slider(
                        minimum=1,
                        maximum=10,
                        value=3,
                        step=1,
                        label="返回文档数量",
                    )
                    
                    with gr.Tab("知识库"):
                        knowledge_file = gr.File(
                            label="上传知识库文件（JSON格式）",
                        )
                        upload_knowledge_btn = gr.Button("上传知识库")
                    
                    with gr.Tab("文档"):
                        document_file = gr.File(
                            label="上传文档文件（文本格式）",
                        )
                        upload_document_btn = gr.Button("上传文档")
            
            # 事件处理
            submit_btn.click(
                fn=self.answer_question,
                inputs=[query, chatbot, top_k],
                outputs=[query, chatbot],
            )
            
            query.submit(
                fn=self.answer_question,
                inputs=[query, chatbot, top_k],
                outputs=[query, chatbot],
            )
            
            upload_knowledge_btn.click(
                fn=self.upload_knowledge,
                inputs=[knowledge_file],
                outputs=[gr.Textbox(label="上传结果")],
            )
            
            upload_document_btn.click(
                fn=self.upload_document,
                inputs=[document_file],
                outputs=[gr.Textbox(label="上传结果")],
            )
        
        return interface
    
    def launch(self, **kwargs):
        """
        启动 Gradio 界面
        """
        interface = self.create_interface()
        interface.launch(**kwargs) 