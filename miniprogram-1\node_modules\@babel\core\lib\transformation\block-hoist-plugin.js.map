{"version": 3, "names": ["_traverse", "data", "require", "_plugin", "LOADED_PLUGIN", "blockHoistPlugin", "name", "visitor", "Block", "exit", "node", "body", "performHoisting", "SwitchCase", "consequent", "max", "Math", "pow", "hasChange", "i", "length", "n", "p", "priority", "stableSort", "slice", "loadBlockHoistPlugin", "Plugin", "Object", "assign", "traverse", "explode", "bodyNode", "_blockHoist", "buckets", "create", "bucket", "push", "keys", "map", "k", "sort", "a", "b", "index", "key"], "sources": ["../../src/transformation/block-hoist-plugin.ts"], "sourcesContent": ["import traverse from \"@babel/traverse\";\nimport type { Statement } from \"@babel/types\";\nimport type { PluginObject } from \"../config/index.ts\";\nimport Plugin from \"../config/plugin.ts\";\n\nlet LOADED_PLUGIN: Plugin | void;\n\nconst blockHoistPlugin: PluginObject = {\n  /**\n   * [Please add a description.]\n   *\n   * Priority:\n   *\n   *  - 0 We want this to be at the **very** bottom\n   *  - 1 Default node position\n   *  - 2 Priority over normal nodes\n   *  - 3 We want this to be at the **very** top\n   *  - 4 Reserved for the helpers used to implement module imports.\n   */\n\n  name: \"internal.blockHoist\",\n\n  visitor: {\n    Block: {\n      exit({ node }) {\n        node.body = performHoisting(node.body);\n      },\n    },\n    SwitchCase: {\n      exit({ node }) {\n        // In case statements, hoisting is difficult to perform correctly due to\n        // functions that are declared and referenced in different blocks.\n        // Nevertheless, hoisting the statements *inside* of each case should at\n        // least mitigate the failure cases.\n        node.consequent = performHoisting(node.consequent);\n      },\n    },\n  },\n};\n\nfunction performHoisting(body: Statement[]): Statement[] {\n  // Largest SMI\n  let max = 2 ** 30 - 1;\n  let hasChange = false;\n  for (let i = 0; i < body.length; i++) {\n    const n = body[i];\n    const p = priority(n);\n    if (p > max) {\n      hasChange = true;\n      break;\n    }\n    max = p;\n  }\n  if (!hasChange) return body;\n\n  // My kingdom for a stable sort!\n  return stableSort(body.slice());\n}\n\nexport default function loadBlockHoistPlugin(): Plugin {\n  if (!LOADED_PLUGIN) {\n    // cache the loaded blockHoist plugin plugin\n    LOADED_PLUGIN = new Plugin(\n      {\n        ...blockHoistPlugin,\n        visitor: traverse.explode(blockHoistPlugin.visitor),\n      },\n      {},\n    );\n  }\n\n  return LOADED_PLUGIN;\n}\n\nfunction priority(bodyNode: Statement & { _blockHoist?: number | true }) {\n  const priority = bodyNode?._blockHoist;\n  if (priority == null) return 1;\n  if (priority === true) return 2;\n  return priority;\n}\n\nfunction stableSort(body: Statement[]) {\n  // By default, we use priorities of 0-4.\n  const buckets = Object.create(null);\n\n  // By collecting into buckets, we can guarantee a stable sort.\n  for (let i = 0; i < body.length; i++) {\n    const n = body[i];\n    const p = priority(n);\n\n    // In case some plugin is setting an unexpected priority.\n    const bucket = buckets[p] || (buckets[p] = []);\n    bucket.push(n);\n  }\n\n  // Sort our keys in descending order. Keys are unique, so we don't have to\n  // worry about stability.\n  const keys = Object.keys(buckets)\n    .map(k => +k)\n    .sort((a, b) => b - a);\n\n  let index = 0;\n  for (const key of keys) {\n    const bucket = buckets[key];\n    for (const n of bucket) {\n      body[index++] = n;\n    }\n  }\n  return body;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,IAAAE,OAAA,GAAAD,OAAA;AAEA,IAAIE,aAA4B;AAEhC,MAAMC,gBAA8B,GAAG;EAarCC,IAAI,EAAE,qBAAqB;EAE3BC,OAAO,EAAE;IACPC,KAAK,EAAE;MACLC,IAAIA,CAAC;QAAEC;MAAK,CAAC,EAAE;QACbA,IAAI,CAACC,IAAI,GAAGC,eAAe,CAACF,IAAI,CAACC,IAAI,CAAC;MACxC;IACF,CAAC;IACDE,UAAU,EAAE;MACVJ,IAAIA,CAAC;QAAEC;MAAK,CAAC,EAAE;QAKbA,IAAI,CAACI,UAAU,GAAGF,eAAe,CAACF,IAAI,CAACI,UAAU,CAAC;MACpD;IACF;EACF;AACF,CAAC;AAED,SAASF,eAAeA,CAACD,IAAiB,EAAe;EAEvD,IAAII,GAAG,GAAGC,IAAA,CAAAC,GAAA,EAAC,EAAI,EAAE,IAAG,CAAC;EACrB,IAAIC,SAAS,GAAG,KAAK;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,MAAME,CAAC,GAAGV,IAAI,CAACQ,CAAC,CAAC;IACjB,MAAMG,CAAC,GAAGC,QAAQ,CAACF,CAAC,CAAC;IACrB,IAAIC,CAAC,GAAGP,GAAG,EAAE;MACXG,SAAS,GAAG,IAAI;MAChB;IACF;IACAH,GAAG,GAAGO,CAAC;EACT;EACA,IAAI,CAACJ,SAAS,EAAE,OAAOP,IAAI;EAG3B,OAAOa,UAAU,CAACb,IAAI,CAACc,KAAK,CAAC,CAAC,CAAC;AACjC;AAEe,SAASC,oBAAoBA,CAAA,EAAW;EACrD,IAAI,CAACtB,aAAa,EAAE;IAElBA,aAAa,GAAG,IAAIuB,eAAM,CAAAC,MAAA,CAAAC,MAAA,KAEnBxB,gBAAgB;MACnBE,OAAO,EAAEuB,mBAAQ,CAACC,OAAO,CAAC1B,gBAAgB,CAACE,OAAO;IAAC,IAErD,CAAC,CACH,CAAC;EACH;EAEA,OAAOH,aAAa;AACtB;AAEA,SAASmB,QAAQA,CAACS,QAAqD,EAAE;EACvE,MAAMT,QAAQ,GAAGS,QAAQ,oBAARA,QAAQ,CAAEC,WAAW;EACtC,IAAIV,QAAQ,IAAI,IAAI,EAAE,OAAO,CAAC;EAC9B,IAAIA,QAAQ,KAAK,IAAI,EAAE,OAAO,CAAC;EAC/B,OAAOA,QAAQ;AACjB;AAEA,SAASC,UAAUA,CAACb,IAAiB,EAAE;EAErC,MAAMuB,OAAO,GAAGN,MAAM,CAACO,MAAM,CAAC,IAAI,CAAC;EAGnC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,MAAME,CAAC,GAAGV,IAAI,CAACQ,CAAC,CAAC;IACjB,MAAMG,CAAC,GAAGC,QAAQ,CAACF,CAAC,CAAC;IAGrB,MAAMe,MAAM,GAAGF,OAAO,CAACZ,CAAC,CAAC,KAAKY,OAAO,CAACZ,CAAC,CAAC,GAAG,EAAE,CAAC;IAC9Cc,MAAM,CAACC,IAAI,CAAChB,CAAC,CAAC;EAChB;EAIA,MAAMiB,IAAI,GAAGV,MAAM,CAACU,IAAI,CAACJ,OAAO,CAAC,CAC9BK,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC,CACZC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EAExB,IAAIE,KAAK,GAAG,CAAC;EACb,KAAK,MAAMC,GAAG,IAAIP,IAAI,EAAE;IACtB,MAAMF,MAAM,GAAGF,OAAO,CAACW,GAAG,CAAC;IAC3B,KAAK,MAAMxB,CAAC,IAAIe,MAAM,EAAE;MACtBzB,IAAI,CAACiC,KAAK,EAAE,CAAC,GAAGvB,CAAC;IACnB;EACF;EACA,OAAOV,IAAI;AACb;AAAC", "ignoreList": []}