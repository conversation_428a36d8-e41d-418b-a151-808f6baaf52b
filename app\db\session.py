from typing import Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

engine = create_async_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    pool_pre_ping=True,
    echo=True,
    future=True
)

AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

async def get_db() -> Generator:
    """
    获取数据库会话
    """
    try:
        db = AsyncSessionLocal()
        yield db
    finally:
        await db.close()

class DatabaseManager:
    """
    数据库管理器
    """
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def commit_refresh(self, obj):
        """
        提交并刷新对象
        """
        try:
            await self.session.commit()
            await self.session.refresh(obj)
        except Exception as e:
            await self.session.rollback()
            raise e
    
    async def execute(self, statement):
        """
        执行SQL语句
        """
        try:
            result = await self.session.execute(statement)
            return result
        except Exception as e:
            await self.session.rollback()
            raise e
    
    async def bulk_save_objects(self, objects):
        """
        批量保存对象
        """
        try:
            self.session.add_all(objects)
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            raise e
    
    async def begin(self):
        """
        开始事务
        """
        return await self.session.begin()
    
    async def commit(self):
        """
        提交事务
        """
        try:
            await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            raise e
    
    async def rollback(self):
        """
        回滚事务
        """
        await self.session.rollback()
    
    async def close(self):
        """
        关闭会话
        """
        await self.session.close() 