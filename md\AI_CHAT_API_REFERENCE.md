# AI问答系统接口快速参考

## 📋 概述

本文档是AI问答系统接口的快速参考指南，包含所有AI聊天相关的接口定义、数据结构和使用示例。

## 🗄️ 数据结构

### 聊天会话 (Chat Session)
```javascript
{
  "id": "string",                   // 会话ID
  "user_id": "string",              // 用户ID
  "title": "string",                // 会话标题
  "status": "active|ended|archived", // 状态
  "message_count": "number",        // 消息数量
  "total_tokens": "number",         // 总token消耗
  "total_cost": "number",           // 总成本
  "start_time": "datetime",         // 开始时间
  "end_time": "datetime",           // 结束时间
  "last_activity": "datetime",      // 最后活动时间
  "initial_intent": "string"        // 初始意图
}
```

### 聊天消息 (Chat Message)
```javascript
{
  "id": "string",                   // 消息ID
  "session_id": "string",           // 会话ID
  "message_type": "user|ai|system", // 消息类型
  "content": "string",              // 消息内容
  "intent": "string",               // 意图识别结果
  "confidence": "number",           // 置信度 (0-1)
  "tokens_used": "number",          // 使用的token数
  "processing_time": "number",      // 处理时间(ms)
  "feedback": {                     // 用户反馈
    "rating": "number",             // 评分 (1-5)
    "feedback_type": "like|dislike|report",
    "comment": "string"
  },
  "created_at": "datetime"
}
```

### 意图配置 (Intent Config)
```javascript
{
  "id": "string",                   // 配置ID
  "intent_name": "string",          // 意图名称
  "keywords": ["string"],           // 关键词列表
  "patterns": ["string"],           // 正则模式
  "confidence_threshold": "number", // 置信度阈值
  "response_templates": ["string"], // 回复模板
  "requires_birth_info": "boolean", // 是否需要出生信息
  "points_cost": "number",          // 积分消费
  "is_active": "boolean",           // 是否启用
  "priority": "number",             // 优先级
  "usage_count": "number",          // 使用次数
  "success_rate": "number"          // 成功率
}
```

### 知识库文档 (Knowledge Document)
```javascript
{
  "id": "string",                   // 文档ID
  "title": "string",                // 文档标题
  "content": "string",              // 文档内容
  "summary": "string",              // 文档摘要
  "category": "string",             // 分类
  "tags": ["string"],               // 标签列表
  "keywords": ["string"],           // 关键词
  "difficulty_level": "string",     // 难度等级
  "content_type": "string",         // 内容类型
  "source": "string",               // 来源
  "author": "string",               // 作者
  "view_count": "number",           // 查看次数
  "like_count": "number",           // 点赞次数
  "published_at": "datetime",       // 发布时间
  "updated_at": "datetime"          // 更新时间
}
```

### 知识库问答对 (Knowledge QA Pair)
```javascript
{
  "id": "string",                   // 问答对ID
  "question": "string",             // 问题
  "answer": "string",               // 答案
  "category": "string",             // 分类
  "tags": ["string"],               // 标签
  "difficulty": "string",           // 难度
  "confidence_score": "number",     // 置信度评分
  "usage_count": "number",          // 使用次数
  "success_rate": "number",         // 成功率
  "related_documents": ["string"],  // 关联文档ID
  "is_active": "boolean",           // 是否启用
  "created_at": "datetime",         // 创建时间
  "updated_at": "datetime"          // 更新时间
}
```

## 🔗 核心接口

### 1. 发送消息
```http
POST /api/ai-chat/message
Authorization: Bearer {token}
Content-Type: application/json

{
  "message": "请帮我做一个八字分析",
  "session_id": "optional-session-id",
  "context": [
    {
      "role": "user",
      "content": "我想了解我的命理",
      "timestamp": "2024-01-01T10:00:00Z"
    }
  ],
  "analysis_type": "bazi"
}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "session_id": "sess_123456",
    "message_id": "msg_789012",
    "response": {
      "content": "我很乐意为您进行八字分析！为了提供准确的分析，我需要您的出生信息。请点击下方按钮完善您的出生信息。",
      "type": "analysis_guide",
      "analysis_type": "bazi",
      "requires_birth_info": true,
      "actions": [
        {
          "type": "navigate",
          "text": "完善出生信息",
          "url": "/pages/birth-info/birth-info",
          "icon": "📝"
        }
      ]
    },
    "birth_info_status": {
      "has_birth_info": false,
      "missing_fields": ["name", "birth_date", "birth_time"],
      "completeness": 0
    }
  }
}
```

### 2. 快捷操作询问
```http
POST /api/ai-chat/quick-action
Authorization: Bearer {token}
Content-Type: application/json

{
  "action_id": "bazi",
  "session_id": "sess_123456",
  "additional_info": "想了解事业运势"
}
```

### 3. 获取聊天历史
```http
GET /api/ai-chat/history?session_id=sess_123456&page=1&limit=20
Authorization: Bearer {token}
```

### 4. 意图识别
```http
POST /api/ai-chat/intent-recognition
Authorization: Bearer {token}
Content-Type: application/json

{
  "text": "我想看看我的八字怎么样",
  "context": [],
  "user_profile": {
    "has_birth_info": true,
    "vip_level": 1
  }
}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "intent": {
      "primary": "bazi_analysis",
      "secondary": ["fortune_telling", "personality_analysis"],
      "confidence": 0.95,
      "entities": [
        {
          "entity": "analysis_type",
          "value": "bazi",
          "confidence": 0.98
        }
      ]
    },
    "suggested_actions": [
      {
        "action": "start_bazi_analysis",
        "description": "开始八字分析",
        "priority": 1
      }
    ],
    "response_template": "我来为您进行八字分析...",
    "requires_clarification": false
  }
}
```

### 5. 消息反馈
```http
POST /api/ai-chat/feedback
Authorization: Bearer {token}
Content-Type: application/json

{
  "message_id": "msg_789012",
  "feedback_type": "like",
  "rating": 5,
  "comment": "回复很有帮助",
  "tags": ["helpful", "accurate"]
}
```

### 6. 导出聊天记录
```http
POST /api/ai-chat/export
Authorization: Bearer {token}
Content-Type: application/json

{
  "session_ids": ["sess_123456"],
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "format": "json",
  "include_analysis": true
}
```

## � 知识库接口

### 1. 知识库搜索
```http
POST /api/knowledge/search
Authorization: Bearer {token}
Content-Type: application/json

{
  "query": "八字分析的基本原理",
  "search_type": "hybrid",
  "category": "bazi",
  "limit": 10,
  "include_content": false
}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "search_id": "search_123456",
    "total_results": 25,
    "search_time": 150,
    "results": [
      {
        "id": "doc_789012",
        "type": "document",
        "title": "八字分析基础教程",
        "summary": "详细介绍八字分析的基本原理和方法...",
        "category": "bazi",
        "tags": ["八字", "基础", "教程"],
        "relevance_score": 0.95,
        "confidence": 0.88,
        "view_count": 1250,
        "helpful_count": 89
      }
    ],
    "related_queries": ["八字入门", "命理分析方法"],
    "search_suggestions": ["八字排盘", "五行分析"]
  }
}
```

### 2. 获取文档详情
```http
GET /api/knowledge/documents/doc_789012
Authorization: Bearer {token}
```

### 3. 获取问答对详情
```http
GET /api/knowledge/qa-pairs/qa_456789
Authorization: Bearer {token}
```

### 4. 知识库分类浏览
```http
GET /api/knowledge/categories?include_stats=true
Authorization: Bearer {token}
```

### 5. 获取分类内容
```http
GET /api/knowledge/categories/bazi/content?type=all&page=1&limit=20
Authorization: Bearer {token}
```

### 6. 用户反馈
```http
POST /api/knowledge/feedback
Authorization: Bearer {token}
Content-Type: application/json

{
  "content_id": "doc_789012",
  "content_type": "document",
  "feedback_type": "helpful",
  "rating": 5,
  "comment": "内容很详细，对初学者很有帮助"
}
```

### 7. 收藏管理
```http
POST /api/knowledge/bookmarks
Authorization: Bearer {token}
Content-Type: application/json

{
  "content_id": "doc_789012",
  "content_type": "document",
  "notes": "重要参考资料"
}
```

### 8. 智能推荐
```http
GET /api/knowledge/recommendations?context=chat&limit=5
Authorization: Bearer {token}
```

## �🔧 管理员接口

### 1. 获取所有会话
```http
GET /api/admin/ai-chat/sessions?page=1&limit=50&status=active
Authorization: Bearer {admin_token}
```

### 2. 意图配置管理
```http
GET /api/admin/ai-chat/intents
POST /api/admin/ai-chat/intents
PUT /api/admin/ai-chat/intents/{intent_id}
Authorization: Bearer {admin_token}
```

### 3. AI模型配置
```http
GET /api/admin/ai-chat/models
PUT /api/admin/ai-chat/models/config
Authorization: Bearer {admin_token}
```

### 4. 聊天质量分析
```http
GET /api/admin/ai-chat/quality-analysis?period=month
Authorization: Bearer {admin_token}
```

## 📊 统计接口

### 会话统计
```http
GET /api/ai-chat/sessions/stats?period=week
Authorization: Bearer {token}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "session_stats": {
      "total_sessions": 150,
      "active_sessions": 12,
      "avg_messages_per_session": 8.5,
      "avg_session_duration": 15.2
    },
    "message_stats": {
      "total_messages": 1275,
      "user_messages": 637,
      "ai_messages": 638,
      "avg_response_time": 1200
    },
    "intent_stats": [
      {
        "intent": "bazi_analysis",
        "count": 45,
        "percentage": 30.0
      },
      {
        "intent": "yijing_divination",
        "count": 30,
        "percentage": 20.0
      }
    ],
    "satisfaction_stats": {
      "total_feedbacks": 89,
      "positive_rate": 0.85,
      "negative_rate": 0.15,
      "avg_rating": 4.2
    }
  }
}
```

## 🎯 快捷操作配置

### 支持的快捷操作
```javascript
[
  {
    "id": "bazi",
    "title": "八字分析",
    "icon": "🔮",
    "description": "分析您的八字命理",
    "requires_birth_info": true,
    "points_cost": 10
  },
  {
    "id": "yijing",
    "title": "易经占卜",
    "icon": "☯️",
    "description": "易经占卜指导",
    "requires_birth_info": false,
    "points_cost": 5
  },
  {
    "id": "fengshui",
    "title": "风水分析",
    "icon": "🏠",
    "description": "居家风水布局",
    "requires_birth_info": false,
    "points_cost": 8
  },
  {
    "id": "wuxing",
    "title": "五行分析",
    "icon": "🌿",
    "description": "五行属性解读",
    "requires_birth_info": true,
    "points_cost": 10
  }
]
```

## 🚨 错误处理

### 常见错误码
```javascript
{
  "1301": "分析次数已达上限",
  "1302": "分析类型不支持",
  "1304": "AI服务暂时不可用",
  "1205": "出生信息未设置",
  "1206": "积分余额不足"
}
```

### 错误响应格式
```json
{
  "status": "error",
  "message": "积分余额不足",
  "error_code": "1206",
  "data": {
    "current_balance": 5,
    "required_points": 10,
    "suggested_action": "recharge_points"
  }
}
```

## 🔐 认证和权限

### Token认证
所有接口都需要在请求头中包含有效的JWT Token：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 权限级别
- **用户权限**: 基础聊天、查看历史、反馈
- **VIP权限**: 高级分析、优先响应、导出记录
- **管理员权限**: 配置管理、数据分析、系统监控

## 📈 性能指标

### 响应时间目标
- **普通消息**: < 2秒
- **分析类消息**: < 5秒
- **意图识别**: < 500ms
- **历史查询**: < 1秒

### 并发支持
- **最大并发会话**: 1000
- **每用户最大会话**: 5
- **消息队列容量**: 10000

---

## 📝 使用示例

### 完整对话流程
```javascript
// 1. 用户发起八字分析请求
POST /api/ai-chat/message
{
  "message": "请帮我做八字分析",
  "session_id": null
}

// 2. AI检测需要出生信息
Response: {
  "requires_birth_info": true,
  "actions": [{"type": "navigate", "url": "/pages/birth-info/birth-info"}]
}

// 3. 用户完善出生信息后重新请求
POST /api/ai-chat/quick-action
{
  "action_id": "bazi",
  "session_id": "sess_123456"
}

// 4. AI提供详细分析
Response: {
  "content": "根据您的八字...",
  "analysis": {...},
  "suggestions": [...]
}

// 5. 用户反馈
POST /api/ai-chat/feedback
{
  "message_id": "msg_789012",
  "feedback_type": "like",
  "rating": 5
}
```

这份快速参考文档涵盖了AI问答系统的所有核心接口，可以作为开发团队的实用工具书！
