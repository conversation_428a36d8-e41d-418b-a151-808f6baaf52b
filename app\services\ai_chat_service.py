"""
AI聊天服务
提供智能对话、命理咨询和功能引导
"""
import asyncio
import random
import json
import httpx
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
import os

# 导入AI配置管理器
from app.core.ai_config import get_ai_config, is_ai_available, AIModelConfig

logger = logging.getLogger(__name__)

class AIChatService:
    """AI聊天服务类"""
    
    def __init__(self):
        # 预定义的回复模板
        self.response_templates = {
            "greeting": [
                "您好！我是卦里乾坤的AI助手，专门为您提供专业的命理咨询服务。",
                "欢迎来到卦里乾坤！我可以帮您解答八字、易经、风水等各种玄学问题。",
                "很高兴为您服务！请告诉我您想了解什么方面的内容？"
            ],
            "bazi": [
                "八字命理是中华传统文化的重要组成部分，通过您的出生年月日时，可以分析性格、运势等。您想了解哪个方面？",
                "八字分析需要您的详细出生信息，包括年、月、日、时。我可以为您详细解读命盘特点。",
                "根据八字可以分析事业、财运、感情、健康等各个方面，您最关心哪个领域？"
            ],
            "yijing": [
                "易经是群经之首，包含深刻的哲学智慧。您有什么具体的问题想要占卜吗？",
                "易经六十四卦可以指导人生决策，请告诉我您想问的具体问题。",
                "易经占卜需要您静心思考问题，然后我为您起卦解读。"
            ],
            "fengshui": [
                "风水学讲究环境与人的和谐，关系到居住者的运势。您想了解家居还是办公风水？",
                "好的风水布局可以改善运势，请描述一下您的具体环境情况。",
                "风水分析需要考虑方向、格局、周边环境等因素，您有具体的问题吗？"
            ],
            "fortune": [
                "运势变化有其规律，结合您的命理信息可以预测趋势。您想查询哪个时期的运势？",
                "每个人的运势都有起伏，通过合理调节可以趋吉避凶。您关心什么方面的运势？",
                "运势预测可以帮助您把握时机，做出更好的决策。"
            ],
            "general": [
                "这是个很有意思的问题，让我从传统文化的角度为您分析。",
                "根据命理学的观点，这个问题可以这样理解...",
                "从玄学的角度来看，我建议您..."
            ],
            "birth_info_needed": [
                "为了给您更准确的分析，需要您提供详细的出生信息：年、月、日、时（最好精确到几点几分）",
                "准确的命理分析需要基于您的生辰八字，请先完善您的出生信息。",
                "您的出生时间决定了命盘的准确性，建议先在个人资料中完善这些信息。"
            ]
        }
        
        # 关键词映射
        self.keyword_mapping = {
            "八字": "bazi",
            "命理": "bazi", 
            "排盘": "bazi",
            "五行": "bazi",
            "易经": "yijing",
            "占卜": "yijing",
            "卦象": "yijing",
            "六爻": "yijing",
            "风水": "fengshui",
            "布局": "fengshui",
            "家居": "fengshui",
            "运势": "fortune",
            "财运": "fortune",
            "事业": "fortune",
            "感情": "fortune",
            "婚姻": "fortune",
            "健康": "fortune",
            "流年": "fortune",
            "你好": "greeting",
            "您好": "greeting",
            "hello": "greeting",
            "hi": "greeting"
        }
        
        # 建议问题模板
        self.suggestion_templates = {
            "bazi": [
                "我想详细了解我的八字命盘",
                "请分析我的性格特征",
                "我的事业发展方向如何？",
                "我的财运状况怎么样？"
            ],
            "yijing": [
                "我想问一个关于工作的问题",
                "感情方面有什么建议？",
                "最近的决策应该如何选择？",
                "我的未来发展趋势如何？"
            ],
            "fengshui": [
                "我的家居风水有什么问题？",
                "办公室应该如何布局？",
                "卧室风水需要注意什么？",
                "如何提升财运风水？"
            ],
            "fortune": [
                "我今天的运势如何？",
                "这个月的财运怎么样？",
                "我的事业运势分析",
                "感情运势什么时候会好转？"
            ],
            "general": [
                "我想了解更多关于命理的知识",
                "如何改善自己的运势？",
                "生活中有哪些风水禁忌？",
                "什么时候适合做重要决定？"
            ]
        }
    
    async def chat(self, message: str, context: Optional[List[Dict]] = None, 
                   feature_type: Optional[str] = None, user_profile: Optional[Dict] = None) -> Dict:
        """
        处理AI聊天请求 - 增强版
        """
        try:
            logger.info(f"处理AI聊天请求：{message[:50]}...")
            
            # 清理和预处理消息
            processed_message = self._preprocess_message(message)
            
            # 识别意图
            intent = self._detect_intent(processed_message, feature_type)
            
            # 尝试使用真实AI模型生成回复
            ai_response = None
            if is_ai_available():
                ai_response = await self._generate_ai_response(processed_message, intent, context, user_profile)
            
            # 如果AI模型不可用，使用智能模板回复
            if not ai_response:
                ai_response = await self._generate_intelligent_response(processed_message, intent, context, user_profile)
            
            # 生成建议问题
            suggestions = self._generate_suggestions(intent, user_profile)
            
            # 检查是否需要功能调用
            function_calls = self._check_function_calls(intent, processed_message, user_profile)
            
            result = {
                "response": ai_response,
                "suggestions": suggestions,
                "function_calls": function_calls
            }
            
            logger.info(f"AI聊天处理完成，意图：{intent}")
            return result
            
        except Exception as e:
            logger.error(f"AI聊天处理失败：{e}")
            return {
                "response": "抱歉，我现在无法理解您的问题，请稍后再试或换个方式提问。",
                "suggestions": ["我想了解八字分析", "请帮我看看运势", "风水有什么建议？"],
                "function_calls": None
            }
    
    def _preprocess_message(self, message: str) -> str:
        """预处理用户消息"""
        # 移除多余空格和特殊字符
        processed = message.strip()
        
        # 标准化常见词汇
        replacements = {
            "算命": "命理分析",
            "看相": "面相分析", 
            "测运": "运势分析",
            "起名": "姓名分析"
        }
        
        for old, new in replacements.items():
            processed = processed.replace(old, new)
        
        return processed
    
    def _detect_intent(self, message: str, feature_type: Optional[str] = None) -> str:
        """识别用户意图"""
        # 如果指定了功能类型，优先使用
        if feature_type:
            return feature_type
        
        # 基于关键词匹配
        for keyword, intent in self.keyword_mapping.items():
            if keyword in message:
                return intent
        
        # 基于消息长度和内容判断
        if len(message) < 10 and any(word in message for word in ["你好", "您好", "hi", "hello"]):
            return "greeting"
        
        # 如果包含问号，可能是占卜类问题
        if "?" in message or "？" in message:
            if any(word in message for word in ["工作", "事业", "感情", "婚姻", "健康", "财运"]):
                return "yijing"
        
        # 默认返回通用类型
        return "general"
    
    async def _generate_ai_response(self, message: str, intent: str, context: Optional[List[Dict]], 
                                  user_profile: Optional[Dict]) -> Optional[str]:
        """使用真实AI模型生成回复"""
        
        # 获取AI配置
        ai_config = get_ai_config()
        if not ai_config:
            return None
        
        # 构建专业的命理提示词
        system_prompt = self._build_system_prompt(intent, user_profile)
        
        try:
            # 根据配置选择合适的API调用方法
            if ai_config.provider == 'openai':
                return await self._call_openai(ai_config, system_prompt, message, context)
            elif ai_config.provider == 'ernie':
                return await self._call_ernie(ai_config, system_prompt, message, context)
            elif ai_config.provider in ['local', 'ollama']:
                return await self._call_ollama(ai_config, system_prompt, message, context)
            else:
                logger.warning(f"不支持的AI提供商: {ai_config.provider}")
                return None
                
        except Exception as e:
            logger.error(f"AI模型调用失败: {e}")
            return None
    
    def _build_system_prompt(self, intent: str, user_profile: Optional[Dict]) -> str:
        """构建专业的系统提示词"""
        base_prompt = """你是一位资深的命理学专家和AI助手，名字叫做"卦里乾坤AI"。你精通：

1. 八字命理：天干地支、五行生克、十神关系、格局分析
2. 易经占卜：六十四卦象、爻辞解读、变卦分析  
3. 风水布局：峦头理气、九宫飞星、八宅风水
4. 紫微斗数：主星特质、宫位分析、流年运势

请遵循以下原则：
- 回答要专业准确，体现传统文化底蕴
- 语言通俗易懂，让普通用户也能理解
- 提供建设性的建议和指导
- 保持客观理性，不做过分夸大的预测
- 回答长度控制在200字以内，简洁明了

"""
        
        # 根据意图添加特定指导
        if intent == "bazi":
            base_prompt += "\n当前用户询问八字命理问题，请从五行分析、性格特征、运势走向角度回答。"
        elif intent == "yijing":
            base_prompt += "\n当前用户询问易经占卜问题，请从卦象智慧、人生指导角度回答。"
        elif intent == "fengshui":
            base_prompt += "\n当前用户询问风水问题，请从环境调和、气场优化角度回答。"
        elif intent == "fortune":
            base_prompt += "\n当前用户询问运势问题，请从趋势分析、时机把握角度回答。"
        
        return base_prompt
    
    async def _call_openai(self, config: AIModelConfig, system_prompt: str, message: str, 
                          context: Optional[List[Dict]]) -> Optional[str]:
        """调用OpenAI API"""
        try:
            headers = {
                "Authorization": f"Bearer {config.api_key}",
                "Content-Type": "application/json"
            }
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ]
            
            data = {
                "model": config.model_name,
                "messages": messages,
                "max_tokens": config.max_tokens,
                "temperature": config.temperature
            }
            
            async with httpx.AsyncClient(timeout=config.timeout) as client:
                response = await client.post(
                    f"{config.base_url}/chat/completions",
                    headers=headers,
                    json=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result["choices"][0]["message"]["content"].strip()
                else:
                    logger.error(f"OpenAI API错误: {response.status_code} - {response.text}")
                    return None
                
        except Exception as e:
            logger.error(f"OpenAI调用异常: {e}")
            return None
    
    async def _call_ernie(self, config: AIModelConfig, system_prompt: str, message: str, 
                         context: Optional[List[Dict]]) -> Optional[str]:
        """调用百度文心一言API"""
        try:
            # 获取access_token
            token_url = f"{config.base_url}/oauth/2.0/token"
            token_params = {
                "grant_type": "client_credentials",
                "client_id": config.api_key,
                "client_secret": os.getenv('ERNIE_SECRET_KEY')
            }
            
            async with httpx.AsyncClient(timeout=config.timeout) as client:
                token_response = await client.post(token_url, params=token_params)
                access_token = token_response.json().get("access_token")
                
                if not access_token:
                    return None
                
                # 调用文心一言API
                api_url = f"{config.base_url}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token={access_token}"
                
                messages = [{"role": "user", "content": f"{system_prompt}\n\n用户问题：{message}"}]
                
                api_response = await client.post(api_url, json={
                    "messages": messages,
                    "max_output_tokens": config.max_tokens,
                    "temperature": config.temperature
                })
                
                if api_response.status_code == 200:
                    result = api_response.json()
                    return result.get("result", "").strip()
                else:
                    logger.error(f"文心一言API错误: {api_response.status_code}")
                    return None
                
        except Exception as e:
            logger.error(f"文心一言调用异常: {e}")
            return None
    
    async def _call_ollama(self, config: AIModelConfig, system_prompt: str, message: str, 
                          context: Optional[List[Dict]]) -> Optional[str]:
        """调用Ollama API"""
        try:
            async with httpx.AsyncClient(timeout=config.timeout) as client:
                data = {
                    "model": config.model_name,
                    "prompt": f"{system_prompt}\n\n用户问题：{message}\n\n请回答：",
                    "stream": False,
                    "options": {
                        "temperature": config.temperature,
                        "num_predict": config.max_tokens
                    }
                }
                
                response = await client.post(f"{config.base_url}/api/generate", json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "").strip()
                else:
                    logger.error(f"Ollama API错误: {response.status_code}")
                    return None
                
        except Exception as e:
            logger.error(f"Ollama调用异常: {e}")
            return None
    
    async def _generate_intelligent_response(self, message: str, intent: str, context: Optional[List[Dict]], 
                                           user_profile: Optional[Dict]) -> str:
        """生成智能模板回复（增强版）"""
        
        # 模拟思考时间
        await asyncio.sleep(0.5 + random.random())
        
        # 检查是否需要出生信息
        if intent in ["bazi", "fortune"] and not self._has_birth_info(user_profile):
            return random.choice(self.response_templates["birth_info_needed"])
        
        # 基于关键词生成更智能的回复
        intelligent_response = self._generate_keyword_based_response(message, intent, user_profile)
        if intelligent_response:
            return intelligent_response
        
        # 基于意图生成基础回复
        base_response = random.choice(self.response_templates.get(intent, self.response_templates["general"]))
        
        # 个性化回复
        personalized_response = self._personalize_response(base_response, message, intent, user_profile)
        
        return personalized_response
    
    def _generate_keyword_based_response(self, message: str, intent: str, user_profile: Optional[Dict]) -> Optional[str]:
        """基于关键词生成智能回复"""
        
        if intent == "bazi":
            if "五行" in message:
                return "五行包括金、木、水、火、土，它们相生相克，影响着我们的性格和运势。五行平衡的人通常身心健康，运势顺畅。您想了解自己的五行属性吗？"
            elif "性格" in message:
                return "通过八字分析可以深入了解您的性格特征，包括天性禀赋、行为模式、思维方式等。这有助于您更好地认识自己，发挥优势。"
            elif "事业" in message:
                return "八字中的官杀星、食伤星等都与事业发展密切相关。结合您的命盘结构，可以分析适合的职业方向和发展时机。"
        
        elif intent == "yijing":
            if "卦象" in message:
                return "卦象是易经的核心，每一卦都有其特定的含义和指导意义。通过解读卦象，可以为您的问题提供智慧的指引。"
            elif "决策" in message or "选择" in message:
                return "易经强调'时'的重要性，不同的时机有不同的行动策略。让我为您起卦，看看当前的形势和最佳的应对方案。"
        
        elif intent == "fengshui":
            if "财位" in message:
                return "财位是影响财运的重要位置，通常在入门斜对角。保持财位明亮整洁，可摆放招财植物或水晶，避免堆放杂物。"
            elif "卧室" in message:
                return "卧室风水影响睡眠和健康。床头要靠实墙，避免正对镜子和门，色调要温馨。好的卧室风水有助于恢复精力，改善运势。"
        
        elif intent == "fortune":
            if "今年" in message:
                current_year = datetime.now().year
                return f"{current_year}年的运势需要结合您的个人命理来具体分析。总体来说，建议保持积极心态，抓住机遇，谨慎应对挑战。"
            elif "财运" in message:
                return "财运与您的命理结构和流年运势都有关系。正财运看稳定收入，偏财运看意外收获。建议通过正当途径求财，同时注意理财规划。"
        
        return None
    
    def _get_birth_info_prompt(self, intent: str) -> str:
        """获取出生信息提示"""
        prompts = {
            "bazi": "要进行准确的八字分析，我需要您的详细出生信息：出生年月日时（最好精确到分钟）。这样我才能为您排出精准的命盘，分析您的性格特征和运势走向。",
            "fortune": "运势分析需要基于您的个人命理信息，请先完善您的出生年月日时，这样我可以为您提供更准确的运势预测和指导建议。"
        }
        return prompts.get(intent, "为了给您更精准的分析，建议先完善您的出生信息。")
    
    def _has_birth_info(self, user_profile: Optional[Dict]) -> bool:
        """检查用户是否有完整出生信息"""
        if not user_profile:
            return False
        
        required_fields = ["birth_year", "birth_month", "birth_day"]
        return all(user_profile.get(field) for field in required_fields)
    
    def _personalize_response(self, base_response: str, message: str, intent: str, 
                            user_profile: Optional[Dict]) -> str:
        """个性化回复内容"""
        if not user_profile:
            return base_response
        
        # 根据用户档案信息个性化
        if intent == "bazi" and self._has_birth_info(user_profile):
            year = user_profile.get("birth_year", 0)
            age = datetime.now().year - year if year else 0
            if age > 0:
                base_response += f"\n\n根据您{age}岁的人生阶段，"
                if age < 30:
                    base_response += "正是打基础的重要时期，建议注重学习和积累。"
                elif age < 50:
                    base_response += "正处于事业发展的黄金期，可以积极进取。"
                else:
                    base_response += "已进入成熟稳定期，建议注重养生和传承。"
        
        return base_response
    
    def _add_detailed_advice(self, response: str, message: str, intent: str, 
                           user_profile: Optional[Dict]) -> str:
        """添加详细建议"""
        advice_map = {
            "bazi": [
                "建议您完善出生信息后，我可以为您提供详细的八字分析，包括性格特征、运势走向等。",
                "八字分析可以帮助您了解自身优势，制定更好的人生规划。"
            ],
            "yijing": [
                "易经占卜最重要的是心诚则灵，请您专注思考要询问的问题。",
                "占卜结果仅供参考，最终的决定还是要靠您的智慧和努力。"
            ],
            "fengshui": [
                "风水调整是一个循序渐进的过程，不要急于求成。",
                "最重要的是保持居住环境的整洁和通风。"
            ],
            "fortune": [
                "运势有起伏是正常的，关键是要顺应天时，把握机会。",
                "积极的心态本身就是最好的风水。"
            ]
        }
        
        if intent in advice_map:
            additional_advice = random.choice(advice_map[intent])
            response += f"\n\n{additional_advice}"
        
        return response
    
    def _generate_suggestions(self, intent: str, user_profile: Optional[Dict]) -> List[str]:
        """生成建议问题"""
        suggestions = self.suggestion_templates.get(intent, self.suggestion_templates["general"])
        
        # 随机选择3-4个建议
        selected = random.sample(suggestions, min(3, len(suggestions)))
        
        # 根据用户档案调整建议
        if intent == "bazi" and not self._has_birth_info(user_profile):
            selected.insert(0, "我想先完善出生信息")
        
        return selected
    
    def _check_function_calls(self, intent: str, message: str, user_profile: Optional[Dict]) -> Optional[List[Dict]]:
        """检查是否需要功能调用"""
        function_calls = []
        
        # 如果用户明确要求某项分析
        if intent == "bazi" and any(word in message for word in ["分析", "排盘", "详细"]):
            if self._has_birth_info(user_profile):
                function_calls.append({
                    "function": "bazi_analysis",
                    "description": "八字命理分析",
                    "action": "点击进行八字分析"
                })
            else:
                function_calls.append({
                    "function": "birth_info",
                    "description": "完善出生信息",
                    "action": "点击填写出生信息"
                })
        
        if intent == "yijing" and ("占卜" in message or "问" in message):
            function_calls.append({
                "function": "yijing_divination", 
                "description": "易经占卜",
                "action": "点击开始占卜"
            })
        
        if intent == "fengshui" and any(word in message for word in ["布局", "分析", "看看"]):
            function_calls.append({
                "function": "fengshui_analysis",
                "description": "风水分析",
                "action": "点击进行风水分析"
            })
        
        if intent == "fortune" and any(word in message for word in ["运势", "今天", "这个月", "今年"]):
            function_calls.append({
                "function": "fortune_query",
                "description": "运势查询", 
                "action": "点击查询运势"
            })
        
        return function_calls if function_calls else None
    
    def get_welcome_message(self, user_profile: Optional[Dict] = None) -> str:
        """获取欢迎消息"""
        base_welcome = "🔮 欢迎来到卦里乾坤！我是您的专属AI命理助手。\n\n我可以为您提供：\n• 八字命理分析\n• 易经卦象占卜\n• 风水布局建议\n• 运势预测查询\n• 专业命理咨询"
        
        if user_profile and self._has_birth_info(user_profile):
            base_welcome += "\n\n✨ 您的出生信息已完善，可以为您提供更精准的个性化分析。"
        else:
            base_welcome += "\n\n💡 建议先完善出生信息，以获得更准确的命理分析。"
        
        base_welcome += "\n\n请告诉我您想了解什么，或者点击下方的快捷功能开始体验！"
        
        return base_welcome
    
    def get_quick_actions(self) -> List[Dict]:
        """获取快捷操作列表"""
        return [
            {
                "id": "bazi",
                "title": "八字分析", 
                "icon": "🔮",
                "description": "专业八字排盘分析"
            },
            {
                "id": "yijing",
                "title": "易经占卜",
                "icon": "☯️", 
                "description": "传统易经卦象占卜"
            },
            {
                "id": "fengshui",
                "title": "风水分析",
                "icon": "🏠",
                "description": "居家办公风水布局"
            },
            {
                "id": "fortune",
                "title": "运势查询",
                "icon": "⭐",
                "description": "个人运势预测分析"
            }
        ]
    
    def generate_conversation_summary(self, messages: List[Dict]) -> str:
        """生成对话摘要"""
        if not messages:
            return "暂无对话记录"
        
        # 统计对话类型
        topics = []
        for msg in messages:
            if msg.get("type") == "user":
                content = msg.get("content", "")
                for keyword, topic in self.keyword_mapping.items():
                    if keyword in content and topic not in topics:
                        topics.append(topic)
        
        topic_names = {
            "bazi": "八字分析",
            "yijing": "易经占卜", 
            "fengshui": "风水咨询",
            "fortune": "运势查询",
            "general": "综合咨询"
        }
        
        if topics:
            topic_str = "、".join([topic_names.get(t, t) for t in topics])
            return f"本次对话主要涉及：{topic_str}，共{len(messages)}条消息。"
        else:
            return f"进行了一般性咨询对话，共{len(messages)}条消息。" 