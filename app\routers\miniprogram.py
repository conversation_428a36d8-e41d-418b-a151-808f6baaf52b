from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
import json
import re
from app.core.security import get_current_user
from app.services.ai_service_optimized import create_ai_service
from app.core.cache_manager import CacheManager
from app.utils.enhanced_knowledge_base import enhanced_kb

router = APIRouter(prefix="/api/miniprogram", tags=["小程序专用API"])

# 数据模型
class WxChatRequest(BaseModel):
    """微信小程序聊天请求"""
    query: str = Field(..., description="用户问题", min_length=1, max_length=500)
    context: Optional[List[Dict[str, str]]] = Field([], description="聊天上下文")
    user_info: Optional[Dict[str, Any]] = Field(None, description="用户信息")
    
class WxFortuneRequest(BaseModel):
    """微信小程序占卜请求"""
    query: str = Field(..., description="占卜问题")
    birth_info: Optional[Dict[str, Any]] = Field(None, description="生辰信息")
    fortune_type: str = Field("bazi", description="占卜类型")
    
class WxResponse(BaseModel):
    """微信小程序统一响应格式"""
    status: str = Field("success", description="状态")
    message: str = Field("", description="消息")
    data: Optional[Dict[str, Any]] = Field(None, description="数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

# 意图识别配置
INTENT_PATTERNS = {
    "bazi": ["八字", "命理", "生辰", "算命"],
    "yijing": ["易经", "卦象", "占卜", "卜卦", "起卦"],
    "fengshui": ["风水", "布局", "方位", "家居"],
    "wuxing": ["五行", "金木水火土", "属性"],
    "fortune": ["运势", "今日", "本月", "今年", "未来"],
    "ziwei": ["紫微", "斗数", "命盘"],
    "question": ["问题", "咨询", "请问", "帮我"]
}

class MiniProgramService:
    """小程序服务类"""
    
    def __init__(self):
        self.cache_manager = None
        self.ai_service = None
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.cache_manager = CacheManager(redis_url="redis://localhost:6379/0")
            await self.cache_manager.initialize()
            self.ai_service = await create_ai_service(
                cache_manager=self.cache_manager,
                default_model="chatglm-6b"
            )
        except Exception as e:
            print(f"初始化小程序服务失败: {e}")
    
    def detect_intent(self, query: str) -> str:
        """检测用户意图"""
        query_lower = query.lower()
        
        # 检查关键词匹配
        for intent, keywords in INTENT_PATTERNS.items():
            for keyword in keywords:
                if keyword in query_lower:
                    return intent
        
        return "question"  # 默认为一般问题
    
    def validate_birth_info(self, birth_info: Dict[str, Any]) -> bool:
        """验证生辰信息的完整性"""
        required_fields = ["year", "month", "day", "hour"]
        return all(field in birth_info for field in required_fields)
    
    def format_ai_response(self, response: str, intent: str) -> Dict[str, Any]:
        """格式化AI响应"""
        formatted = {
            "content": response,
            "type": intent,
            "timestamp": datetime.now().isoformat()
        }
        
        # 根据意图类型添加特殊格式
        if intent in ["bazi", "ziwei", "fortune"]:
            formatted["category"] = "命理分析"
            formatted["icon"] = "🔮"
        elif intent == "yijing":
            formatted["category"] = "易经卦象" 
            formatted["icon"] = "☯️"
        elif intent == "fengshui":
            formatted["category"] = "风水分析"
            formatted["icon"] = "🏠"
        elif intent == "wuxing":
            formatted["category"] = "五行分析"
            formatted["icon"] = "🌿"
        else:
            formatted["category"] = "智能问答"
            formatted["icon"] = "🤖"
            
        return formatted

# 创建服务实例
mp_service = MiniProgramService()

@router.on_event("startup")
async def startup_event():
    """启动时初始化服务"""
    await mp_service.initialize()

@router.post("/chat", response_model=WxResponse, summary="小程序智能聊天")
async def miniprogram_chat(
    request: WxChatRequest,
    current_user: Optional[Dict] = Depends(get_current_user)
):
    """
    小程序专用的智能聊天接口
    
    支持:
    - 意图识别
    - 上下文理解
    - 多轮对话
    - 个性化回复
    """
    try:
        # 检测用户意图
        intent = mp_service.detect_intent(request.query)
        
        # 构建上下文
        context = {
            "intent": intent,
            "user_info": request.user_info,
            "history": request.context[-5:] if request.context else []
        }
        
        # 调用AI服务
        if not mp_service.ai_service:
            raise HTTPException(
                status_code=503, 
                detail="AI服务未就绪，请稍后再试"
            )
        
        response = await mp_service.ai_service.generate_text(
            prompt=request.query,
            config=None,
            user_id=current_user.get("id") if current_user else None
        )
        
        # 格式化响应
        formatted_response = mp_service.format_ai_response(
            response.text, 
            intent
        )
        
        return WxResponse(
            status="success",
            message="回复成功",
            data={
                "response": formatted_response,
                "intent": intent,
                "suggestions": await get_suggestions(intent)
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"处理聊天请求失败: {str(e)}"
        )

@router.post("/fortune", response_model=WxResponse, summary="小程序占卜分析")
async def miniprogram_fortune(
    request: WxFortuneRequest,
    current_user: Optional[Dict] = Depends(get_current_user)
):
    """
    小程序专用的占卜分析接口
    
    支持:
    - 八字分析
    - 运势预测
    - 个性化建议
    """
    try:
        # 验证生辰信息
        if not request.birth_info or not mp_service.validate_birth_info(request.birth_info):
            return WxResponse(
                status="error",
                message="需要完整的生辰信息",
                data={
                    "error_code": "BIRTH_INFO_REQUIRED",
                    "required_fields": ["year", "month", "day", "hour"]
                }
            )
        
        # 构建占卜prompt
        fortune_prompt = f"""
        请根据以下信息进行{request.fortune_type}分析：
        
        用户问题：{request.query}
        出生年份：{request.birth_info.get('year')}年
        出生月份：{request.birth_info.get('month')}月  
        出生日期：{request.birth_info.get('day')}日
        出生时辰：{request.birth_info.get('hour')}时
        
        请提供详细的分析和建议。
        """
        
        # 调用AI服务
        response = await mp_service.ai_service.generate_text(
            prompt=fortune_prompt,
            config=None,
            user_id=current_user.get("id") if current_user else None
        )
        
        # 解析占卜结果
        fortune_result = parse_fortune_result(response.text, request.fortune_type)
        
        return WxResponse(
            status="success", 
            message="占卜分析完成",
            data={
                "fortune": fortune_result,
                "birth_info": request.birth_info,
                "type": request.fortune_type
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"占卜分析失败: {str(e)}"
        )

@router.get("/suggestions", summary="获取建议问题")
async def get_suggestions(intent: str = "question") -> List[str]:
    """根据意图获取建议问题"""
    
    suggestions_map = {
        "bazi": [
            "请分析我的八字命理",
            "我的性格特点如何？", 
            "我适合什么职业？",
            "我的婚姻运势如何？"
        ],
        "yijing": [
            "我想问关于事业的问题",
            "感情方面有什么指引？",
            "最近的运势如何？",
            "我该如何做重要决定？"
        ],
        "fengshui": [
            "我的家居风水如何？",
            "办公室应该如何布局？",
            "什么方位对我有利？",
            "如何改善运势？"
        ],
        "wuxing": [
            "我的五行属性是什么？",
            "我缺什么五行？",
            "如何平衡五行？",
            "今年的五行运势？"
        ],
        "fortune": [
            "今日运势如何？",
            "本月需要注意什么？",
            "今年的总体运势？",
            "财运方面如何？"
        ]
    }
    
    return suggestions_map.get(intent, [
        "你能为我做什么？",
        "如何开始占卜？", 
        "我需要提供什么信息？",
        "有什么注意事项？"
    ])

def parse_fortune_result(text: str, fortune_type: str) -> Dict[str, Any]:
    """解析占卜结果为结构化数据"""
    
    result = {
        "summary": "",
        "details": "",
        "advice": "",
        "lucky_elements": [],
        "score": 0
    }
    
    try:
        # 使用正则表达式提取结构化信息
        summary_match = re.search(r'【?总结?】?[:：]?\s*([^【】]+)', text)
        if summary_match:
            result["summary"] = summary_match.group(1).strip()
        
        details_match = re.search(r'【?详细?分析?】?[:：]?\s*([^【】]+)', text)
        if details_match:
            result["details"] = details_match.group(1).strip()
            
        advice_match = re.search(r'【?建议?指导?】?[:：]?\s*([^【】]+)', text)
        if advice_match:
            result["advice"] = advice_match.group(1).strip()
        
        # 如果没有找到结构化内容，将整个文本作为详细内容
        if not any([result["summary"], result["details"], result["advice"]]):
            result["details"] = text
            
        # 生成运势评分
        positive_keywords = ["好", "吉", "旺", "顺", "佳", "利", "强"]
        negative_keywords = ["差", "凶", "弱", "不利", "困", "阻", "险"]
        
        score = 70  # 基础分数
        for keyword in positive_keywords:
            score += text.count(keyword) * 3
        for keyword in negative_keywords:
            score -= text.count(keyword) * 5
            
        result["score"] = max(0, min(100, score))
        
    except Exception as e:
        print(f"解析占卜结果失败: {e}")
        result["details"] = text
        
    return result

@router.get("/health", summary="健康检查")
async def health_check():
    """小程序服务健康检查"""
    
    status = {
        "service": "healthy",
        "ai_service": "unknown",
        "cache": "unknown",
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        # 检查AI服务
        if mp_service.ai_service:
            status["ai_service"] = "healthy"
        else:
            status["ai_service"] = "unavailable"
            
        # 检查缓存服务
        if mp_service.cache_manager:
            await mp_service.cache_manager.ping()
            status["cache"] = "healthy"
        else:
            status["cache"] = "unavailable"
            
    except Exception as e:
        status["error"] = str(e)
        
    return WxResponse(
        status="success",
        message="健康检查完成", 
        data=status
    ) 