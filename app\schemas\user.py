from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator
import re

class UserBase(BaseModel):
    """用户基本信息"""
    email: EmailStr = Field(
        ...,
        description="用户邮箱",
        example="<EMAIL>"
    )
    username: str = Field(
        ...,
        description="用户名",
        min_length=3,
        max_length=20,
        example="johndoe"
    )
    full_name: Optional[str] = Field(
        None,
        description="用户全名",
        max_length=50,
        example="John Doe"
    )

    @validator('username')
    def username_alphanumeric(cls, v):
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('用户名只能包含字母、数字、下划线和连字符')
        return v

class UserCreate(UserBase):
    """创建用户请求"""
    password: str = Field(
        ...,
        description="密码",
        min_length=8,
        max_length=50,
        example="strongpassword123"
    )

    @validator('password')
    def password_strength(cls, v):
        if not re.match(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$', v):
            raise ValueError('密码必须包含至少一个字母和一个数字')
        return v

class UserUpdate(BaseModel):
    """更新用户请求"""
    email: Optional[EmailStr] = Field(
        None,
        description="用户邮箱",
        example="<EMAIL>"
    )
    full_name: Optional[str] = Field(
        None,
        description="用户全名",
        max_length=50,
        example="John Doe"
    )
    password: Optional[str] = Field(
        None,
        description="新密码",
        min_length=8,
        max_length=50,
        example="newpassword123"
    )

    @validator('password')
    def password_strength(cls, v):
        if v is not None and not re.match(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$', v):
            raise ValueError('密码必须包含至少一个字母和一个数字')
        return v

class UserInDB(UserBase):
    """数据库中的用户模型"""
    id: int = Field(
        ...,
        description="用户ID",
        example=1
    )
    is_active: bool = Field(
        ...,
        description="是否激活",
        example=True
    )
    is_superuser: bool = Field(
        ...,
        description="是否是超级用户",
        example=False
    )

class UserResponse(UserBase):
    """用户响应模型"""
    id: int = Field(
        ...,
        description="用户ID",
        example=1
    )
    is_active: bool = Field(
        ...,
        description="是否激活",
        example=True
    )

    class Config:
        schema_extra = {
            "example": {
                "id": 1,
                "email": "<EMAIL>",
                "username": "johndoe",
                "full_name": "John Doe",
                "is_active": True
            }
        }

class Token(BaseModel):
    """认证令牌"""
    access_token: str = Field(
        ...,
        description="访问令牌",
        example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    )
    token_type: str = Field(
        ...,
        description="令牌类型",
        example="bearer"
    )
    expires_in: int = Field(
        ...,
        description="过期时间（秒）",
        example=3600
    )

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600
            }
        }

class TokenData(BaseModel):
    """令牌数据"""
    username: Optional[str] = None