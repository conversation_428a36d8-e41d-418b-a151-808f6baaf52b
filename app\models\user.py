"""
用户相关数据模型
包含用户基本信息、用户档案、占卜记录等
"""
from sqlalchemy import Column, String, Integer, DateTime, Boolean, Text, JSON, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class User(Base):
    """用户基本信息表"""
    __tablename__ = "users"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    openid = Column(String(128), unique=True, nullable=False, comment="微信openid")
    unionid = Column(String(128), nullable=True, comment="微信unionid")
    nickname = Column(String(100), nullable=False, default="用户", comment="昵称")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    gender = Column(Integer, default=0, comment="性别：0未知，1男，2女")
    phone = Column(String(20), nullable=True, comment="手机号")
    email = Column(String(100), nullable=True, comment="邮箱")
    
    # 积分和等级系统
    points = Column(Integer, default=0, comment="用户积分")
    level = Column(Integer, default=1, comment="用户等级")
    vip_level = Column(Integer, default=0, comment="VIP等级：0普通，1会员，2高级会员")
    vip_expire_time = Column(DateTime, nullable=True, comment="VIP过期时间")
    
    # 状态信息
    status = Column(Integer, default=1, comment="用户状态：0禁用，1正常")
    is_deleted = Column(Boolean, default=False, comment="是否删除")
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    login_count = Column(Integer, default=0, comment="登录次数")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    profile = relationship("UserProfile", back_populates="user", uselist=False)
    divination_records = relationship("DivinationRecord", back_populates="user")
    favorites = relationship("UserFavorite", back_populates="user")

    def __repr__(self):
        return f"<User {self.nickname}>"

class UserProfile(Base):
    """用户详细档案表"""
    __tablename__ = "user_profiles"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 出生信息
    birth_year = Column(Integer, nullable=True, comment="出生年份")
    birth_month = Column(Integer, nullable=True, comment="出生月份")
    birth_day = Column(Integer, nullable=True, comment="出生日期")
    birth_hour = Column(Integer, nullable=True, comment="出生小时")
    birth_minute = Column(Integer, default=0, comment="出生分钟")
    gender = Column(Integer, nullable=True, comment="性别：0女，1男")
    is_lunar = Column(Boolean, default=False, comment="是否农历")
    timezone = Column(String(50), default="Asia/Shanghai", comment="时区")
    birth_place = Column(String(200), nullable=True, comment="出生地点")
    
    # 个人信息
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    zodiac = Column(String(10), nullable=True, comment="生肖")
    constellation = Column(String(20), nullable=True, comment="星座")
    height = Column(Float, nullable=True, comment="身高(cm)")
    weight = Column(Float, nullable=True, comment="体重(kg)")
    blood_type = Column(String(10), nullable=True, comment="血型")
    
    # 联系信息
    qq = Column(String(20), nullable=True, comment="QQ号")
    wechat = Column(String(50), nullable=True, comment="微信号")
    address = Column(String(200), nullable=True, comment="联系地址")
    
    # 偏好设置
    preferred_analysis_type = Column(String(50), default="bazi", comment="偏好分析类型")
    privacy_level = Column(Integer, default=1, comment="隐私级别：1公开，2好友，3私密")
    notification_enabled = Column(Boolean, default=True, comment="是否开启通知")
    
    # 扩展信息（JSON格式存储）
    extra_info = Column(JSON, nullable=True, comment="扩展信息")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    user = relationship("User", back_populates="profile")

class DivinationRecord(Base):
    """占卜测算记录表"""
    __tablename__ = "divination_records"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 记录基本信息
    type = Column(String(50), nullable=False, comment="类型：bazi/yijing/fengshui/ziwei/fortune")
    title = Column(String(200), nullable=True, comment="记录标题")
    question = Column(Text, nullable=True, comment="占卜问题")
    
    # 请求和结果数据
    request_data = Column(JSON, nullable=True, comment="请求数据")
    result_data = Column(JSON, nullable=True, comment="分析结果")
    
    # 评分和标签
    score = Column(Integer, nullable=True, comment="分析评分")
    accuracy = Column(Float, nullable=True, comment="准确度")
    tags = Column(Text, nullable=True, comment="标签，逗号分隔")
    
    # 状态信息
    status = Column(Integer, default=1, comment="状态：0删除，1正常，2收藏")
    is_public = Column(Boolean, default=False, comment="是否公开")
    view_count = Column(Integer, default=0, comment="查看次数")
    
    # 付费信息
    is_paid = Column(Boolean, default=False, comment="是否付费")
    cost_points = Column(Integer, default=0, comment="消耗积分")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    user = relationship("User", back_populates="divination_records")

class UserFavorite(Base):
    """用户收藏表"""
    __tablename__ = "user_favorites"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 收藏内容信息
    favorite_type = Column(String(50), nullable=False, comment="收藏类型：record/article/master")
    target_id = Column(String(36), nullable=False, comment="目标ID")
    target_title = Column(String(200), nullable=True, comment="目标标题")
    target_summary = Column(Text, nullable=True, comment="目标摘要")
    
    # 收藏信息
    notes = Column(Text, nullable=True, comment="用户备注")
    folder = Column(String(50), default="default", comment="收藏夹")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    
    # 关联关系
    user = relationship("User", back_populates="favorites")

class UserPoints(Base):
    """用户积分记录表"""
    __tablename__ = "user_points"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 积分变动信息
    points_change = Column(Integer, nullable=False, comment="积分变动：正数增加，负数减少")
    points_before = Column(Integer, nullable=False, comment="变动前积分")
    points_after = Column(Integer, nullable=False, comment="变动后积分")
    
    # 变动原因
    reason_type = Column(String(50), nullable=False, comment="原因类型：sign_in/test/purchase/gift")
    reason_desc = Column(String(200), nullable=True, comment="原因描述")
    related_id = Column(String(36), nullable=True, comment="关联记录ID")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")

class UserSignIn(Base):
    """用户签到记录表"""
    __tablename__ = "user_sign_in"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 签到信息
    sign_date = Column(DateTime, nullable=False, comment="签到日期")
    continuous_days = Column(Integer, default=1, comment="连续签到天数")
    points_earned = Column(Integer, default=1, comment="获得积分")
    
    # 签到状态
    is_补签 = Column(Boolean, default=False, comment="是否补签")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")

class MasterInfo(Base):
    """大师信息表"""
    __tablename__ = "master_info"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 基本信息
    name = Column(String(50), nullable=False, comment="大师姓名")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    title = Column(String(100), nullable=True, comment="称号")
    specialties = Column(Text, nullable=True, comment="专长领域")
    introduction = Column(Text, nullable=True, comment="个人介绍")
    
    # 等级信息
    level = Column(Integer, default=1, comment="大师等级")
    rating = Column(Float, default=5.0, comment="评分")
    order_count = Column(Integer, default=0, comment="接单数量")
    success_rate = Column(Float, default=100.0, comment="成功率")
    
    # 收费信息
    price_per_question = Column(Integer, default=10, comment="单次问答价格")
    price_per_detailed = Column(Integer, default=50, comment="详细分析价格")
    
    # 状态信息
    status = Column(Integer, default=1, comment="状态：0离线，1在线，2忙碌")
    is_verified = Column(Boolean, default=False, comment="是否认证")
    is_recommended = Column(Boolean, default=False, comment="是否推荐")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间") 