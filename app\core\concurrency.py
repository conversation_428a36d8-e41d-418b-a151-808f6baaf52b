from typing import Optional, Callable, Any, List
import asyncio
import time
from redis import Redis
from datetime import datetime
import logging
from functools import wraps

logger = logging.getLogger(__name__)

class DistributedLock:
    def __init__(self, redis: Redis, lock_timeout: int = 30):
        self.redis = redis
        self.lock_timeout = lock_timeout

    async def acquire(self, key: str, wait_timeout: Optional[int] = None) -> bool:
        start_time = time.time()
        while True:
            if await self.redis.set(
                f"lock:{key}",
                datetime.utcnow().isoformat(),
                nx=True,
                ex=self.lock_timeout
            ):
                return True

            if wait_timeout and time.time() - start_time > wait_timeout:
                return False

            await asyncio.sleep(0.1)

    async def release(self, key: str) -> None:
        await self.redis.delete(f"lock:{key}")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.release()

class RequestQueue:
    def __init__(
        self,
        redis: Redis,
        max_concurrent: int = 10,
        timeout: int = 30
    ):
        self.redis = redis
        self.max_concurrent = max_concurrent
        self.timeout = timeout

    async def enqueue(self, key: str, data: Any) -> str:
        queue_key = f"queue:{key}"
        await self.redis.rpush(queue_key, str(data))
        return queue_key

    async def dequeue(self, key: str) -> Optional[str]:
        queue_key = f"queue:{key}"
        return await self.redis.lpop(queue_key)

    async def process_queue(
        self,
        key: str,
        processor: Callable[[str], Any]
    ) -> None:
        while True:
            item = await self.dequeue(key)
            if not item:
                break
            await processor(item)

class ConcurrencyManager:
    def __init__(
        self,
        redis: Redis,
        max_concurrent: int = 10,
        request_timeout: int = 30
    ):
        self.redis = redis
        self.lock = DistributedLock(redis)
        self.queue = RequestQueue(redis, max_concurrent, request_timeout)
        self.max_concurrent = max_concurrent

    async def get_active_count(self, resource: str) -> int:
        return int(await self.redis.get(f"active:{resource}") or 0)

    async def increment_active(self, resource: str) -> int:
        return await self.redis.incr(f"active:{resource}")

    async def decrement_active(self, resource: str) -> int:
        return await self.redis.decr(f"active:{resource}")

    def with_concurrency_control(
        self,
        resource: str,
        max_concurrent: Optional[int] = None
    ):
        def decorator(func: Callable):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                max_conn = max_concurrent or self.max_concurrent
                active = await self.get_active_count(resource)

                if active >= max_conn:
                    # Enqueue request
                    await self.queue.enqueue(
                        resource,
                        {'args': args, 'kwargs': kwargs}
                    )
                    while await self.get_active_count(resource) >= max_conn:
                        await asyncio.sleep(0.1)

                await self.increment_active(resource)
                try:
                    return await func(*args, **kwargs)
                finally:
                    await self.decrement_active(resource)

            return wrapper
        return decorator

class BatchProcessor:
    def __init__(
        self,
        batch_size: int = 100,
        flush_interval: float = 1.0
    ):
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.items: List[Any] = []
        self.last_flush = time.time()

    async def add(self, item: Any, processor: Callable[[List[Any]], Any]) -> None:
        self.items.append(item)

        if len(self.items) >= self.batch_size or \
           time.time() - self.last_flush >= self.flush_interval:
            await self.flush(processor)

    async def flush(self, processor: Callable[[List[Any]], Any]) -> None:
        if not self.items:
            return

        items_to_process = self.items
        self.items = []
        self.last_flush = time.time()

        try:
            await processor(items_to_process)
        except Exception as e:
            logger.error(f"Error processing batch: {str(e)}")
            # Re-queue failed items
            self.items.extend(items_to_process) 