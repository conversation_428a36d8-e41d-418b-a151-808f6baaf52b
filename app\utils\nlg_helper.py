from typing import Dict, List, Any
import random
from datetime import datetime

class NaturalLanguageGenerator:
    """自然语言生成辅助类，用于生成更自然的回答"""

    def __init__(self):
        self.templates = {
            "greeting": [
                "您好，很高兴为您解读{topic}",
                "让我来为您分析一下{topic}",
                "感谢您的咨询，我将为您详细解读{topic}"
            ],
            "transition": [
                "接下来，让我们看看",
                "下面，我将为您分析",
                "让我们进一步了解",
                "接着，我们来关注"
            ],
            "conclusion": [
                "总的来说，",
                "综上所述，",
                "根据以上分析，",
                "通过全面解读，"
            ],
            "suggestion": [
                "建议您可以",
                "我给您的建议是",
                "您可以考虑",
                "不妨尝试"
            ],
            "fortune_level": {
                "excellent": ["大吉", "上上", "极好"],
                "good": ["吉", "上", "良好"],
                "neutral": ["平", "中", "一般"],
                "poor": ["凶", "下", "欠佳"]
            }
        }
        
        self.time_sensitive_greetings = {
            "morning": "早上好，",
            "afternoon": "下午好，",
            "evening": "晚上好，"
        }

    def get_time_sensitive_greeting(self) -> str:
        """根据当前时间生成适当的问候语"""
        hour = datetime.now().hour
        if 5 <= hour < 12:
            return self.time_sensitive_greetings["morning"]
        elif 12 <= hour < 18:
            return self.time_sensitive_greetings["afternoon"]
        else:
            return self.time_sensitive_greetings["evening"]

    def generate_bazi_response(self, bazi_result: Dict[str, Any]) -> str:
        """生成八字分析的自然语言回答"""
        greeting = random.choice(self.templates["greeting"]).format(topic="您的八字命盘")
        
        # 生成八字描述
        pillars = []
        for pillar in ["year", "month", "day", "hour"]:
            stem = bazi_result[f"{pillar}_pillar"]["stem"]
            branch = bazi_result[f"{pillar}_pillar"]["branch"]
            pillars.append(f"{stem}{branch}")
        
        bazi_str = "您的八字是：" + "、".join(pillars)
        
        # 生成五行分析
        elements = bazi_result.get("favorable_elements", [])
        element_str = f"在五行中，您最适合的是{'、'.join(elements)}"
        
        # 生成建议
        suggestions = [
            f"在事业方面，{bazi_result.get('career_guidance', '')}",
            f"您的人生方向宜往{bazi_result.get('life_direction', '')}发展"
        ]
        
        suggestion_str = self.templates["suggestion"][0] + "；".join(suggestions)
        
        return f"{greeting}\n\n{bazi_str}\n\n{element_str}\n\n{suggestion_str}"

    def generate_ziwei_response(self, ziwei_result: Dict[str, Any]) -> str:
        """生成紫微斗数分析的自然语言回答"""
        greeting = random.choice(self.templates["greeting"]).format(topic="您的紫微命盘")
        
        # 命宫描述
        ming_gong = ziwei_result["ming_gong"]
        ming_gong_str = f"您的命宫在{ming_gong['position']}，主星有{'、'.join(ming_gong['stars'])}"
        
        # 生成各方面分析
        aspects = ziwei_result.get("life_aspects", {})
        
        career = aspects.get("career", {})
        career_str = f"在事业方面，您{career.get('potential', '')}，适合从事{'、'.join(career.get('suitable_fields', []))}等领域"
        
        relationship = aspects.get("relationships", {})
        relationship_str = f"在感情方面，{relationship.get('marriage', '')}，{relationship.get('social', '')}"
        
        health = aspects.get("health", {})
        health_str = f"在健康方面，{health.get('constitution', '')}，建议您{'、'.join(health.get('suggestions', []))}"
        
        conclusion = random.choice(self.templates["conclusion"]) + "您的紫微命盘显示您具有很好的发展潜力"
        
        return f"{greeting}\n\n{ming_gong_str}\n\n{career_str}\n\n{relationship_str}\n\n{health_str}\n\n{conclusion}"

    def generate_yijing_response(self, yijing_result: Dict[str, Any]) -> str:
        """生成易经解读的自然语言回答"""
        greeting = random.choice(self.templates["greeting"]).format(topic="您的卦象")
        
        hexagram = f"您所得到的是{yijing_result['hexagram_name']}卦，象曰：{yijing_result['overall_meaning']}"
        
        guidance = yijing_result.get("specific_guidance", "")
        guidance_str = random.choice(self.templates["suggestion"]) + guidance
        
        elements = yijing_result.get("lucky_elements", [])
        element_str = f"在近期，您比较适合接触{'、'.join(elements)}相关的事物"
        
        return f"{greeting}\n\n{hexagram}\n\n{guidance_str}\n\n{element_str}"

    def generate_fengshui_response(self, fengshui_result: Dict[str, Any]) -> str:
        """生成风水分析的自然语言回答"""
        greeting = random.choice(self.templates["greeting"]).format(topic="您的居所风水")
        
        location = fengshui_result.get("location_quality", {})
        location_str = f"您所在位置的能量场{location.get('energy_level', '')}，周围有{'、'.join(location.get('surrounding_elements', []))}，气场流动{location.get('qi_flow', '')}"
        
        house = fengshui_result.get("house_energy", {})
        house_str = f"房屋整体能量{house.get('overall_energy', '')}，{house.get('flow_status', '')}"
        
        recommendations = fengshui_result.get("recommendations", [])
        if recommendations:
            suggestion_str = "具体建议如下：\n" + "\n".join([
                f"- {rec['aspect']}: {rec['recommendation']} (优先级: {rec['priority']})"
                for rec in recommendations
            ])
        else:
            suggestion_str = ""
        
        return f"{greeting}\n\n{location_str}\n\n{house_str}\n\n{suggestion_str}"

    def generate_wuxing_response(self, wuxing_result: Dict[str, Any]) -> str:
        """生成五行分析的自然语言回答"""
        greeting = random.choice(self.templates["greeting"]).format(topic="您的五行属性")
        
        dominant = f"您的主导五行是{wuxing_result.get('dominant_element', '')}"
        supporting = f"有利五行是{'、'.join(wuxing_result.get('supporting_elements', []))}"
        weak = f"需要补充的五行是{'、'.join(wuxing_result.get('weak_elements', []))}"
        
        recommendations = wuxing_result.get("recommendations", {})
        
        environment = recommendations.get("environment", [])
        env_str = "在环境布置上，" + "，".join(environment)
        
        diet = recommendations.get("diet", [])
        diet_str = "在饮食方面，" + "，".join(diet)
        
        activities = recommendations.get("activities", [])
        activity_str = "建议的活动有：\n" + "\n".join([
            f"- {act['activity']}: {act['benefit']}"
            for act in activities
        ])
        
        return f"{greeting}\n\n{dominant}，{supporting}，{weak}\n\n{env_str}\n\n{diet_str}\n\n{activity_str}"

    def generate_general_fortune_response(self, fortune_result: Dict[str, Any]) -> str:
        """生成综合运势分析的自然语言回答"""
        greeting = self.get_time_sensitive_greeting() + random.choice(self.templates["greeting"]).format(topic="您的运势")
        
        life_path = f"您属于{fortune_result.get('life_path', '')}，性格特点是{fortune_result.get('personality', '')}"
        
        talents = fortune_result.get('talents', [])
        talents_str = f"您的主要优势在于{'、'.join(talents)}"
        
        challenges = fortune_result.get('challenges', [])
        challenges_str = f"需要注意克服{'、'.join(challenges)}"
        
        cycles = fortune_result.get('life_cycles', {})
        cycles_str = "人生阶段特点：\n" + "\n".join([
            f"- {stage}: {description}"
            for stage, description in cycles.items()
        ])
        
        conclusion = random.choice(self.templates["conclusion"]) + "您拥有很好的发展潜力，建议充分发挥自己的优势"
        
        return f"{greeting}\n\n{life_path}\n\n{talents_str}\n\n{challenges_str}\n\n{cycles_str}\n\n{conclusion}"

    def format_yearly_luck(self, yearly_luck: Dict[str, Any]) -> str:
        """格式化年度运势"""
        result = []
        
        overall = yearly_luck.get("overall_luck", "")
        result.append(f"总体运势：{overall}")
        
        aspects = {
            "career": "事业运势",
            "relationships": "人际运势",
            "wealth": "财运",
            "health": "健康运势"
        }
        
        for key, label in aspects.items():
            if key in yearly_luck:
                result.append(f"{label}：{yearly_luck[key]}")
        
        lucky_months = yearly_luck.get("lucky_months", [])
        if lucky_months:
            result.append(f"吉利月份：{'、'.join(map(str, lucky_months))}月")
        
        unlucky_months = yearly_luck.get("unlucky_months", [])
        if unlucky_months:
            result.append(f"需注意月份：{'、'.join(map(str, unlucky_months))}月")
        
        suggestions = yearly_luck.get("suggestions", [])
        if suggestions:
            result.append("\n建议：\n" + "\n".join([f"- {s}" for s in suggestions]))
        
        return "\n\n".join(result)

    def add_emotional_expression(self, text: str) -> str:
        """添加情感表达，使语言更生动"""
        emotions = {
            "positive": ["真不错", "很棒", "很有希望"],
            "encouraging": ["要对自己有信心", "相信一定会有好结果", "保持积极心态"],
            "empathy": ["我理解", "这确实需要注意", "这是很正常的"]
        }
        
        # 根据文本内容选择合适的情感表达
        if "吉" in text or "好" in text:
            text += f"\n\n{random.choice(emotions['positive'])}！"
        elif "建议" in text or "注意" in text:
            text += f"\n\n{random.choice(emotions['encouraging'])}。"
        elif "凶" in text or "差" in text:
            text += f"\n\n{random.choice(emotions['empathy'])}。"
        
        return text 