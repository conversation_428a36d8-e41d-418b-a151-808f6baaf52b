from typing import List, Dict, Any
import re
import jieba
from haystack.schema import Document

class DocumentPreprocessor:
    """
    文档预处理工具
    """
    
    def __init__(
        self,
        chunk_size: int = 500,
        chunk_overlap: int = 50,
        min_chunk_size: int = 100,
    ):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.min_chunk_size = min_chunk_size
    
    def preprocess_text(self, text: str) -> str:
        """
        文本预处理：清理和规范化
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff,.!?，。！？、]', '', text)
        return text.strip()
    
    def split_into_chunks(self, text: str) -> List[str]:
        """
        将长文本分割成适当大小的块
        """
        # 使用 jieba 分词
        sentences = []
        current_chunk = []
        current_length = 0
        
        # 按标点符号分句
        for sentence in re.split(r'([。！？!?])', text):
            if not sentence:
                continue
            
            # 计算句子长度（中文字符）
            sentence_length = len(sentence)
            
            if current_length + sentence_length <= self.chunk_size:
                current_chunk.append(sentence)
                current_length += sentence_length
            else:
                if current_length >= self.min_chunk_size:
                    sentences.append(''.join(current_chunk))
                current_chunk = [sentence]
                current_length = sentence_length
        
        if current_chunk and current_length >= self.min_chunk_size:
            sentences.append(''.join(current_chunk))
        
        return sentences
    
    def process_document(self, content: str, meta: Dict[str, Any] = None) -> List[Document]:
        """
        处理文档内容，返回文档块列表
        """
        if meta is None:
            meta = {}
        
        # 预处理文本
        cleaned_text = self.preprocess_text(content)
        
        # 分块
        chunks = self.split_into_chunks(cleaned_text)
        
        # 创建文档对象
        documents = []
        for i, chunk in enumerate(chunks):
            doc = Document(
                content=chunk,
                meta={
                    **meta,
                    "chunk_id": i,
                    "total_chunks": len(chunks),
                }
            )
            documents.append(doc)
        
        return documents
    
    def process_knowledge_item(self, item: Dict[str, Any]) -> Document:
        """
        处理知识库条目
        """
        content = item.get("content", "")
        question = item.get("question", "")
        
        # 预处理文本
        cleaned_content = self.preprocess_text(content)
        
        # 创建文档对象
        doc = Document(
            content=cleaned_content,
            meta={
                "question": question,
                "category": item.get("category", ""),
                **item.get("meta", {}),
            }
        )
        
        return doc 