{"version": 3, "names": ["_t", "require", "_jsesc", "isAssignmentPattern", "isIdentifier", "lastRawIdentNode", "lastRawIdentResult", "_getRawIdentifier", "node", "name", "token", "tokenMap", "find", "tok", "value", "_originalCode", "slice", "start", "end", "Identifier", "_node$loc", "sourceIdentifierName", "loc", "identifierName", "word", "ArgumentPlaceholder", "RestElement", "print", "argument", "ObjectExpression", "props", "properties", "length", "exit", "enterDelimited", "space", "printList", "shouldPrintTrailingComma", "sourceWithOffset", "ObjectMethod", "printJoin", "decorators", "_methodHead", "body", "ObjectProperty", "computed", "key", "left", "shorthand", "ArrayExpression", "elems", "elements", "len", "i", "elem", "RecordExpression", "startToken", "endToken", "format", "recordAndTupleSyntaxType", "Error", "JSON", "stringify", "TupleExpression", "RegExpLiteral", "pattern", "flags", "<PERSON>olean<PERSON>iter<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NumericLiteral", "raw", "getPossibleRaw", "opts", "jsescOption", "str", "numbers", "number", "jsesc", "minified", "StringLiteral", "undefined", "val", "BigIntLiteral", "validTopicTokenSet", "Set", "TopicReference", "topicToken", "has", "givenTopicTokenJSON", "validTopics", "Array", "from", "v", "join", "PipelineTopicExpression", "expression", "PipelineBareFunction", "callee", "PipelinePrimaryTopicReference"], "sources": ["../../src/generators/types.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport { isAssignmentPattern, isIdentifier } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport jsesc from \"jsesc\";\n\nlet lastRawIdentNode: t.Identifier | null = null;\nlet lastRawIdentResult: string = \"\";\nexport function _getRawIdentifier(this: Printer, node: t.Identifier) {\n  if (node === lastRawIdentNode) return lastRawIdentResult;\n  lastRawIdentNode = node;\n\n  const { name } = node;\n  const token = this.tokenMap.find(node, tok => tok.value === name);\n  if (token) {\n    lastRawIdentResult = this._originalCode.slice(token.start, token.end);\n    return lastRawIdentResult;\n  }\n  return (lastRawIdentResult = node.name);\n}\n\nexport function Identifier(this: Printer, node: t.Identifier) {\n  this.sourceIdentifierName(node.loc?.identifierName || node.name);\n\n  this.word(this.tokenMap ? this._getRawIdentifier(node) : node.name);\n}\n\nexport function ArgumentPlaceholder(this: Printer) {\n  this.token(\"?\");\n}\n\nexport function RestElement(this: Printer, node: t.RestElement) {\n  this.token(\"...\");\n  this.print(node.argument);\n}\n\nexport { RestElement as SpreadElement };\n\nexport function ObjectExpression(this: Printer, node: t.ObjectExpression) {\n  const props = node.properties;\n\n  this.token(\"{\");\n\n  if (props.length) {\n    const exit = this.enterDelimited();\n    this.space();\n    this.printList(props, this.shouldPrintTrailingComma(\"}\"), true, true);\n    this.space();\n    exit();\n  }\n\n  this.sourceWithOffset(\"end\", node.loc, -1);\n\n  this.token(\"}\");\n}\n\nexport { ObjectExpression as ObjectPattern };\n\nexport function ObjectMethod(this: Printer, node: t.ObjectMethod) {\n  this.printJoin(node.decorators);\n  this._methodHead(node);\n  this.space();\n  this.print(node.body);\n}\n\nexport function ObjectProperty(this: Printer, node: t.ObjectProperty) {\n  this.printJoin(node.decorators);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key);\n    this.token(\"]\");\n  } else {\n    // print `({ foo: foo = 5 } = {})` as `({ foo = 5 } = {});`\n    if (\n      isAssignmentPattern(node.value) &&\n      isIdentifier(node.key) &&\n      // @ts-expect-error todo(flow->ts) `.name` does not exist on some types in union\n      node.key.name === node.value.left.name\n    ) {\n      this.print(node.value);\n      return;\n    }\n\n    this.print(node.key);\n\n    // shorthand!\n    if (\n      node.shorthand &&\n      isIdentifier(node.key) &&\n      isIdentifier(node.value) &&\n      node.key.name === node.value.name\n    ) {\n      return;\n    }\n  }\n\n  this.token(\":\");\n  this.space();\n  this.print(node.value);\n}\n\nexport function ArrayExpression(this: Printer, node: t.ArrayExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  this.token(\"[\");\n\n  const exit = this.enterDelimited();\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem);\n      if (i < len - 1 || this.shouldPrintTrailingComma(\"]\")) {\n        this.token(\",\", false, i);\n      }\n    } else {\n      // If the array expression ends with a hole, that hole\n      // will be ignored by the interpreter, but if it ends with\n      // two (or more) holes, we need to write out two (or more)\n      // commas so that the resulting code is interpreted with\n      // both (all) of the holes.\n      this.token(\",\", false, i);\n    }\n  }\n\n  exit();\n\n  this.token(\"]\");\n}\n\nexport { ArrayExpression as ArrayPattern };\n\nexport function RecordExpression(this: Printer, node: t.RecordExpression) {\n  const props = node.properties;\n\n  let startToken;\n  let endToken;\n  if (process.env.BABEL_8_BREAKING) {\n    startToken = \"#{\";\n    endToken = \"}\";\n  } else {\n    if (this.format.recordAndTupleSyntaxType === \"bar\") {\n      startToken = \"{|\";\n      endToken = \"|}\";\n    } else if (\n      this.format.recordAndTupleSyntaxType !== \"hash\" &&\n      this.format.recordAndTupleSyntaxType != null\n    ) {\n      throw new Error(\n        `The \"recordAndTupleSyntaxType\" generator option must be \"bar\" or \"hash\" (${JSON.stringify(\n          this.format.recordAndTupleSyntaxType,\n        )} received).`,\n      );\n    } else {\n      startToken = \"#{\";\n      endToken = \"}\";\n    }\n  }\n\n  this.token(startToken);\n\n  if (props.length) {\n    this.space();\n    this.printList(props, this.shouldPrintTrailingComma(endToken), true, true);\n    this.space();\n  }\n  this.token(endToken);\n}\n\nexport function TupleExpression(this: Printer, node: t.TupleExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  let startToken;\n  let endToken;\n  if (process.env.BABEL_8_BREAKING) {\n    startToken = \"#[\";\n    endToken = \"]\";\n  } else {\n    if (this.format.recordAndTupleSyntaxType === \"bar\") {\n      startToken = \"[|\";\n      endToken = \"|]\";\n    } else if (this.format.recordAndTupleSyntaxType === \"hash\") {\n      startToken = \"#[\";\n      endToken = \"]\";\n    } else {\n      throw new Error(\n        `${this.format.recordAndTupleSyntaxType} is not a valid recordAndTuple syntax type`,\n      );\n    }\n  }\n\n  this.token(startToken);\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem);\n      if (i < len - 1 || this.shouldPrintTrailingComma(endToken)) {\n        this.token(\",\", false, i);\n      }\n    }\n  }\n\n  this.token(endToken);\n}\n\nexport function RegExpLiteral(this: Printer, node: t.RegExpLiteral) {\n  this.word(`/${node.pattern}/${node.flags}`);\n}\n\nexport function BooleanLiteral(this: Printer, node: t.BooleanLiteral) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteral(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function NumericLiteral(this: Printer, node: t.NumericLiteral) {\n  const raw = this.getPossibleRaw(node);\n  const opts = this.format.jsescOption;\n  const value = node.value;\n  const str = value + \"\";\n  if (opts.numbers) {\n    this.number(jsesc(value, opts), value);\n  } else if (raw == null) {\n    this.number(str, value); // normalize\n  } else if (this.format.minified) {\n    this.number(raw.length < str.length ? raw : str, value);\n  } else {\n    this.number(raw, value);\n  }\n}\n\nexport function StringLiteral(this: Printer, node: t.StringLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const val = jsesc(node.value, this.format.jsescOption);\n\n  this.token(val);\n}\n\nexport function BigIntLiteral(this: Printer, node: t.BigIntLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"n\");\n}\n\n// Hack pipe operator\nconst validTopicTokenSet = new Set([\"^^\", \"@@\", \"^\", \"%\", \"#\"]);\nexport function TopicReference(this: Printer) {\n  const { topicToken } = this.format;\n\n  if (validTopicTokenSet.has(topicToken)) {\n    this.token(topicToken);\n  } else {\n    const givenTopicTokenJSON = JSON.stringify(topicToken);\n    const validTopics = Array.from(validTopicTokenSet, v => JSON.stringify(v));\n    throw new Error(\n      `The \"topicToken\" generator option must be one of ` +\n        `${validTopics.join(\", \")} (${givenTopicTokenJSON} received instead).`,\n    );\n  }\n}\n\n// Smart-mix pipe operator\nexport function PipelineTopicExpression(\n  this: Printer,\n  node: t.PipelineTopicExpression,\n) {\n  this.print(node.expression);\n}\n\nexport function PipelineBareFunction(\n  this: Printer,\n  node: t.PipelineBareFunction,\n) {\n  this.print(node.callee);\n}\n\nexport function PipelinePrimaryTopicReference(this: Printer) {\n  this.token(\"#\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAA0B;EAFjBE,mBAAmB;EAAEC;AAAY,IAAAJ,EAAA;AAI1C,IAAIK,gBAAqC,GAAG,IAAI;AAChD,IAAIC,kBAA0B,GAAG,EAAE;AAC5B,SAASC,iBAAiBA,CAAgBC,IAAkB,EAAE;EACnE,IAAIA,IAAI,KAAKH,gBAAgB,EAAE,OAAOC,kBAAkB;EACxDD,gBAAgB,GAAGG,IAAI;EAEvB,MAAM;IAAEC;EAAK,CAAC,GAAGD,IAAI;EACrB,MAAME,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACJ,IAAI,EAAEK,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,IAAI,CAAC;EACjE,IAAIC,KAAK,EAAE;IACTJ,kBAAkB,GAAG,IAAI,CAACS,aAAa,CAACC,KAAK,CAACN,KAAK,CAACO,KAAK,EAAEP,KAAK,CAACQ,GAAG,CAAC;IACrE,OAAOZ,kBAAkB;EAC3B;EACA,OAAQA,kBAAkB,GAAGE,IAAI,CAACC,IAAI;AACxC;AAEO,SAASU,UAAUA,CAAgBX,IAAkB,EAAE;EAAA,IAAAY,SAAA;EAC5D,IAAI,CAACC,oBAAoB,CAAC,EAAAD,SAAA,GAAAZ,IAAI,CAACc,GAAG,qBAARF,SAAA,CAAUG,cAAc,KAAIf,IAAI,CAACC,IAAI,CAAC;EAEhE,IAAI,CAACe,IAAI,CAAC,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACJ,iBAAiB,CAACC,IAAI,CAAC,GAAGA,IAAI,CAACC,IAAI,CAAC;AACrE;AAEO,SAASgB,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAACf,SAAK,GAAI,CAAC;AACjB;AAEO,SAASgB,WAAWA,CAAgBlB,IAAmB,EAAE;EAC9D,IAAI,CAACE,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACiB,KAAK,CAACnB,IAAI,CAACoB,QAAQ,CAAC;AAC3B;AAIO,SAASC,gBAAgBA,CAAgBrB,IAAwB,EAAE;EACxE,MAAMsB,KAAK,GAAGtB,IAAI,CAACuB,UAAU;EAE7B,IAAI,CAACrB,SAAK,IAAI,CAAC;EAEf,IAAIoB,KAAK,CAACE,MAAM,EAAE;IAChB,MAAMC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAClC,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACN,KAAK,EAAE,IAAI,CAACO,wBAAwB,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IACrE,IAAI,CAACF,KAAK,CAAC,CAAC;IACZF,IAAI,CAAC,CAAC;EACR;EAEA,IAAI,CAACK,gBAAgB,CAAC,KAAK,EAAE9B,IAAI,CAACc,GAAG,EAAE,CAAC,CAAC,CAAC;EAE1C,IAAI,CAACZ,SAAK,IAAI,CAAC;AACjB;AAIO,SAAS6B,YAAYA,CAAgB/B,IAAoB,EAAE;EAChE,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAACiC,UAAU,CAAC;EAC/B,IAAI,CAACC,WAAW,CAAClC,IAAI,CAAC;EACtB,IAAI,CAAC2B,KAAK,CAAC,CAAC;EACZ,IAAI,CAACR,KAAK,CAACnB,IAAI,CAACmC,IAAI,CAAC;AACvB;AAEO,SAASC,cAAcA,CAAgBpC,IAAsB,EAAE;EACpE,IAAI,CAACgC,SAAS,CAAChC,IAAI,CAACiC,UAAU,CAAC;EAE/B,IAAIjC,IAAI,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAACnC,SAAK,GAAI,CAAC;IACf,IAAI,CAACiB,KAAK,CAACnB,IAAI,CAACsC,GAAG,CAAC;IACpB,IAAI,CAACpC,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IAEL,IACEP,mBAAmB,CAACK,IAAI,CAACM,KAAK,CAAC,IAC/BV,YAAY,CAACI,IAAI,CAACsC,GAAG,CAAC,IAEtBtC,IAAI,CAACsC,GAAG,CAACrC,IAAI,KAAKD,IAAI,CAACM,KAAK,CAACiC,IAAI,CAACtC,IAAI,EACtC;MACA,IAAI,CAACkB,KAAK,CAACnB,IAAI,CAACM,KAAK,CAAC;MACtB;IACF;IAEA,IAAI,CAACa,KAAK,CAACnB,IAAI,CAACsC,GAAG,CAAC;IAGpB,IACEtC,IAAI,CAACwC,SAAS,IACd5C,YAAY,CAACI,IAAI,CAACsC,GAAG,CAAC,IACtB1C,YAAY,CAACI,IAAI,CAACM,KAAK,CAAC,IACxBN,IAAI,CAACsC,GAAG,CAACrC,IAAI,KAAKD,IAAI,CAACM,KAAK,CAACL,IAAI,EACjC;MACA;IACF;EACF;EAEA,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACyB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACR,KAAK,CAACnB,IAAI,CAACM,KAAK,CAAC;AACxB;AAEO,SAASmC,eAAeA,CAAgBzC,IAAuB,EAAE;EACtE,MAAM0C,KAAK,GAAG1C,IAAI,CAAC2C,QAAQ;EAC3B,MAAMC,GAAG,GAAGF,KAAK,CAAClB,MAAM;EAExB,IAAI,CAACtB,SAAK,GAAI,CAAC;EAEf,MAAMuB,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAElC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAClB,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACrB,IAAIC,IAAI,EAAE;MACR,IAAID,CAAC,GAAG,CAAC,EAAE,IAAI,CAAClB,KAAK,CAAC,CAAC;MACvB,IAAI,CAACR,KAAK,CAAC2B,IAAI,CAAC;MAChB,IAAID,CAAC,GAAGD,GAAG,GAAG,CAAC,IAAI,IAAI,CAACf,wBAAwB,CAAC,GAAG,CAAC,EAAE;QACrD,IAAI,CAAC3B,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE2C,CAAC,CAAC;MAC3B;IACF,CAAC,MAAM;MAML,IAAI,CAAC3C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE2C,CAAC,CAAC;IAC3B;EACF;EAEApB,IAAI,CAAC,CAAC;EAEN,IAAI,CAACvB,SAAK,GAAI,CAAC;AACjB;AAIO,SAAS6C,gBAAgBA,CAAgB/C,IAAwB,EAAE;EACxE,MAAMsB,KAAK,GAAGtB,IAAI,CAACuB,UAAU;EAE7B,IAAIyB,UAAU;EACd,IAAIC,QAAQ;EAIL;IACL,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,KAAK,EAAE;MAClDH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM,IACL,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,MAAM,IAC/C,IAAI,CAACD,MAAM,CAACC,wBAAwB,IAAI,IAAI,EAC5C;MACA,MAAM,IAAIC,KAAK,CACb,4EAA4EC,IAAI,CAACC,SAAS,CACxF,IAAI,CAACJ,MAAM,CAACC,wBACd,CAAC,aACH,CAAC;IACH,CAAC,MAAM;MACLH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,GAAG;IAChB;EACF;EAEA,IAAI,CAAC/C,KAAK,CAAC8C,UAAU,CAAC;EAEtB,IAAI1B,KAAK,CAACE,MAAM,EAAE;IAChB,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACN,KAAK,EAAE,IAAI,CAACO,wBAAwB,CAACoB,QAAQ,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1E,IAAI,CAACtB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACzB,KAAK,CAAC+C,QAAQ,CAAC;AACtB;AAEO,SAASM,eAAeA,CAAgBvD,IAAuB,EAAE;EACtE,MAAM0C,KAAK,GAAG1C,IAAI,CAAC2C,QAAQ;EAC3B,MAAMC,GAAG,GAAGF,KAAK,CAAClB,MAAM;EAExB,IAAIwB,UAAU;EACd,IAAIC,QAAQ;EAIL;IACL,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,KAAK,EAAE;MAClDH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,MAAM,EAAE;MAC1DH,UAAU,GAAG,IAAI;MACjBC,QAAQ,GAAG,GAAG;IAChB,CAAC,MAAM;MACL,MAAM,IAAIG,KAAK,CACb,GAAG,IAAI,CAACF,MAAM,CAACC,wBAAwB,4CACzC,CAAC;IACH;EACF;EAEA,IAAI,CAACjD,KAAK,CAAC8C,UAAU,CAAC;EAEtB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAClB,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACrB,IAAIC,IAAI,EAAE;MACR,IAAID,CAAC,GAAG,CAAC,EAAE,IAAI,CAAClB,KAAK,CAAC,CAAC;MACvB,IAAI,CAACR,KAAK,CAAC2B,IAAI,CAAC;MAChB,IAAID,CAAC,GAAGD,GAAG,GAAG,CAAC,IAAI,IAAI,CAACf,wBAAwB,CAACoB,QAAQ,CAAC,EAAE;QAC1D,IAAI,CAAC/C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE2C,CAAC,CAAC;MAC3B;IACF;EACF;EAEA,IAAI,CAAC3C,KAAK,CAAC+C,QAAQ,CAAC;AACtB;AAEO,SAASO,aAAaA,CAAgBxD,IAAqB,EAAE;EAClE,IAAI,CAACgB,IAAI,CAAC,IAAIhB,IAAI,CAACyD,OAAO,IAAIzD,IAAI,CAAC0D,KAAK,EAAE,CAAC;AAC7C;AAEO,SAASC,cAAcA,CAAgB3D,IAAsB,EAAE;EACpE,IAAI,CAACgB,IAAI,CAAChB,IAAI,CAACM,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;AAC1C;AAEO,SAASsD,WAAWA,CAAA,EAAgB;EACzC,IAAI,CAAC5C,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAAS6C,cAAcA,CAAgB7D,IAAsB,EAAE;EACpE,MAAM8D,GAAG,GAAG,IAAI,CAACC,cAAc,CAAC/D,IAAI,CAAC;EACrC,MAAMgE,IAAI,GAAG,IAAI,CAACd,MAAM,CAACe,WAAW;EACpC,MAAM3D,KAAK,GAAGN,IAAI,CAACM,KAAK;EACxB,MAAM4D,GAAG,GAAG5D,KAAK,GAAG,EAAE;EACtB,IAAI0D,IAAI,CAACG,OAAO,EAAE;IAChB,IAAI,CAACC,MAAM,CAACC,MAAK,CAAC/D,KAAK,EAAE0D,IAAI,CAAC,EAAE1D,KAAK,CAAC;EACxC,CAAC,MAAM,IAAIwD,GAAG,IAAI,IAAI,EAAE;IACtB,IAAI,CAACM,MAAM,CAACF,GAAG,EAAE5D,KAAK,CAAC;EACzB,CAAC,MAAM,IAAI,IAAI,CAAC4C,MAAM,CAACoB,QAAQ,EAAE;IAC/B,IAAI,CAACF,MAAM,CAACN,GAAG,CAACtC,MAAM,GAAG0C,GAAG,CAAC1C,MAAM,GAAGsC,GAAG,GAAGI,GAAG,EAAE5D,KAAK,CAAC;EACzD,CAAC,MAAM;IACL,IAAI,CAAC8D,MAAM,CAACN,GAAG,EAAExD,KAAK,CAAC;EACzB;AACF;AAEO,SAASiE,aAAaA,CAAgBvE,IAAqB,EAAE;EAClE,MAAM8D,GAAG,GAAG,IAAI,CAACC,cAAc,CAAC/D,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACkD,MAAM,CAACoB,QAAQ,IAAIR,GAAG,KAAKU,SAAS,EAAE;IAC9C,IAAI,CAACtE,KAAK,CAAC4D,GAAG,CAAC;IACf;EACF;EAEA,MAAMW,GAAG,GAAGJ,MAAK,CAACrE,IAAI,CAACM,KAAK,EAAE,IAAI,CAAC4C,MAAM,CAACe,WAAW,CAAC;EAEtD,IAAI,CAAC/D,KAAK,CAACuE,GAAG,CAAC;AACjB;AAEO,SAASC,aAAaA,CAAgB1E,IAAqB,EAAE;EAClE,MAAM8D,GAAG,GAAG,IAAI,CAACC,cAAc,CAAC/D,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACkD,MAAM,CAACoB,QAAQ,IAAIR,GAAG,KAAKU,SAAS,EAAE;IAC9C,IAAI,CAACxD,IAAI,CAAC8C,GAAG,CAAC;IACd;EACF;EACA,IAAI,CAAC9C,IAAI,CAAChB,IAAI,CAACM,KAAK,GAAG,GAAG,CAAC;AAC7B;AAGA,MAAMqE,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,SAASC,cAAcA,CAAA,EAAgB;EAC5C,MAAM;IAAEC;EAAW,CAAC,GAAG,IAAI,CAAC5B,MAAM;EAElC,IAAIyB,kBAAkB,CAACI,GAAG,CAACD,UAAU,CAAC,EAAE;IACtC,IAAI,CAAC5E,KAAK,CAAC4E,UAAU,CAAC;EACxB,CAAC,MAAM;IACL,MAAME,mBAAmB,GAAG3B,IAAI,CAACC,SAAS,CAACwB,UAAU,CAAC;IACtD,MAAMG,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACR,kBAAkB,EAAES,CAAC,IAAI/B,IAAI,CAACC,SAAS,CAAC8B,CAAC,CAAC,CAAC;IAC1E,MAAM,IAAIhC,KAAK,CACb,mDAAmD,GACjD,GAAG6B,WAAW,CAACI,IAAI,CAAC,IAAI,CAAC,KAAKL,mBAAmB,qBACrD,CAAC;EACH;AACF;AAGO,SAASM,uBAAuBA,CAErCtF,IAA+B,EAC/B;EACA,IAAI,CAACmB,KAAK,CAACnB,IAAI,CAACuF,UAAU,CAAC;AAC7B;AAEO,SAASC,oBAAoBA,CAElCxF,IAA4B,EAC5B;EACA,IAAI,CAACmB,KAAK,CAACnB,IAAI,CAACyF,MAAM,CAAC;AACzB;AAEO,SAASC,6BAA6BA,CAAA,EAAgB;EAC3D,IAAI,CAACxF,SAAK,GAAI,CAAC;AACjB", "ignoreList": []}