# Swagger API 文档

## 概述

本项目使用 FastAPI 内置的 Swagger/OpenAPI 支持，提供了交互式 API 文档。您可以通过以下地址访问：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`
- OpenAPI JSON: `http://localhost:8000/openapi.json`

## Swagger 配置

### 1. FastAPI 应用配置

```python
# main.py
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

app = FastAPI(
    title="ChatGLM Knowledge Base QA System",
    description="基于 ChatGLM-6B 和 Haystack 的知识库问答系统",
    version="1.0.0",
    openapi_url="/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc"
)

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
        
    openapi_schema = get_openapi(
        title="ChatGLM Knowledge Base QA System",
        version="1.0.0",
        description="基于 ChatGLM-6B 和 Haystack 的知识库问答系统",
        routes=app.routes,
    )
    
    # 自定义配置
    openapi_schema["info"]["x-logo"] = {
        "url": "https://your-logo-url.com/logo.png"
    }
    
    # 添加安全配置
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {
            "type": "apiKey",
            "in": "header",
            "name": "Authorization",
            "description": "Enter: **'Bearer &lt;JWT&gt;'**"
        }
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi
```

### 2. 路由文档示例

```python
# app/api/routes/chat.py
from fastapi import APIRouter, Path, Query, Body
from typing import Optional
from pydantic import BaseModel, Field

class ChatRequest(BaseModel):
    """聊天请求模型"""
    query: str = Field(
        ...,
        title="用户问题",
        description="用户输入的问题文本",
        min_length=1,
        max_length=1000,
        example="什么是人工智能？"
    )
    context: Optional[str] = Field(
        None,
        title="上下文信息",
        description="可选的上下文信息",
        example="人工智能是计算机科学的一个分支..."
    )
    max_length: int = Field(
        2048,
        title="最大长度",
        description="生成答案的最大长度",
        ge=1,
        le=4096
    )
    
class ChatResponse(BaseModel):
    """聊天响应模型"""
    answer: str = Field(
        ...,
        title="答案",
        description="模型生成的答案"
    )
    confidence: float = Field(
        ...,
        title="置信度",
        description="答案的置信度",
        ge=0,
        le=1
    )
    
router = APIRouter()

@router.post(
    "/chat",
    response_model=ChatResponse,
    summary="聊天接口",
    description="向模型发送问题并获取回答",
    response_description="返回模型生成的答案和置信度",
    tags=["对话"]
)
async def chat_endpoint(
    request: ChatRequest = Body(
        ...,
        example={
            "query": "什么是人工智能？",
            "context": None,
            "max_length": 2048
        }
    )
):
    """
    聊天接口
    
    接收用户问题，返回AI助手的回答
    
    - **query**: 用户问题
    - **context**: 可选的上下文信息
    - **max_length**: 生成答案的最大长度
    
    返回:
    - **answer**: 生成的答案
    - **confidence**: 答案的置信度
    """
    pass
```

## API 分组

### 1. 标签配置

```python
# app/api/tags.py
from fastapi import FastAPI

tags_metadata = [
    {
        "name": "对话",
        "description": "聊天相关接口"
    },
    {
        "name": "用户反馈",
        "description": "用户反馈相关接口"
    },
    {
        "name": "系统监控",
        "description": "系统性能监控相关接口"
    }
]

app = FastAPI(
    openapi_tags=tags_metadata
)
```

### 2. 请求/响应示例

```python
# app/api/routes/feedback.py
from fastapi import APIRouter, Body
from pydantic import BaseModel, Field
from typing import Optional

class FeedbackRequest(BaseModel):
    """反馈请求模型"""
    query_id: str = Field(
        ...,
        title="查询ID",
        description="原始查询的唯一标识",
        example="q123"
    )
    rating: int = Field(
        ...,
        title="评分",
        description="用户评分（1-5）",
        ge=1,
        le=5
    )
    
    class Config:
        schema_extra = {
            "example": {
                "query_id": "q123",
                "rating": 5,
                "feedback_text": "回答非常准确"
            }
        }

@router.post(
    "/feedback",
    tags=["用户反馈"],
    summary="提交反馈",
    response_model=FeedbackResponse
)
async def submit_feedback(
    feedback: FeedbackRequest = Body(...)
):
    """
    提交用户反馈
    
    允许用户对AI助手的回答提供评分和文本反馈
    
    参数:
    - **feedback**: 反馈信息
        - query_id: 查询ID
        - rating: 评分（1-5）
        - feedback_text: 文本反馈
    """
    pass
```

## 安全配置

### 1. API密钥认证

```python
# app/api/dependencies/auth.py
from fastapi import Security, HTTPException
from fastapi.security import APIKeyHeader
from starlette.status import HTTP_403_FORBIDDEN

api_key_header = APIKeyHeader(name="Authorization", auto_error=False)

async def get_api_key(
    api_key: str = Security(api_key_header)
):
    if not api_key:
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN,
            detail="Could not validate API key"
        )
    return api_key
```

### 2. OAuth2认证

```python
# app/api/dependencies/oauth2.py
from fastapi.security import OAuth2PasswordBearer
from fastapi import Depends, HTTPException
from starlette.status import HTTP_401_UNAUTHORIZED

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

async def get_current_user(token: str = Depends(oauth2_scheme)):
    user = decode_token(token)
    if not user:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user
```

## 使用指南

### 1. 访问文档

1. 启动服务器：
```bash
uvicorn main:app --reload
```

2. 访问以下URL之一：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 2. 认证

1. 点击 Swagger UI 右上角的 "Authorize" 按钮
2. 输入API密钥：`Bearer your-api-key`
3. 点击 "Authorize" 确认

### 3. 测试API

1. 选择要测试的接口
2. 点击 "Try it out"
3. 填写请求参数
4. 点击 "Execute"
5. 查看响应结果

### 4. 导出API文档

```bash
# 导出OpenAPI规范
curl http://localhost:8000/openapi.json > openapi.json

# 使用swagger-cli生成HTML文档
npx swagger-cli bundle openapi.json --outfile swagger.html --type html
```

## 最佳实践

1. **请求/响应模型**
   - 使用Pydantic模型定义
   - 添加详细的字段描述
   - 提供示例数据

2. **接口文档**
   - 添加清晰的接口描述
   - 使用标签分组接口
   - 提供详细的参数说明

3. **安全性**
   - 配置适当的认证方式
   - 设置请求速率限制
   - 处理错误响应

4. **示例代码**
   - 提供多语言的调用示例
   - 包含错误处理
   - 说明认证方式 