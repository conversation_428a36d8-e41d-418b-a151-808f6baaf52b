 apiVersion: v1
kind: ConfigMap
metadata:
  name: fortune-config
  namespace: fortune
data:
  db_host: "postgres"
  db_port: "5432"
  db_name: "fortune_db"
  redis_host: "redis"
  redis_port: "6379"
  config.yaml: |
    app:
      name: "AI Fortune Telling API"
      version: "1.0.0"
      debug: false
      environment: "production"
    
    server:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      reload: false
    
    monitoring:
      prometheus:
        enabled: true
        port: 9090
      logging:
        level: "INFO"
        format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        file: "logs/app.log"
        max_size: 100
        backup_count: 5
    
    model:
      base_path: "models/"
      default_model: "THUDM/chatglm-6b"
      device: "cuda"
      batch_size: 32
      max_length: 2048
      cache_size: 1000
      quantization: "4bit"

---
apiVersion: v1
kind: Secret
metadata:
  name: fortune-secrets
  namespace: fortune
type: Opaque
stringData:
  db_user: "fortune"
  db_password: "fortune123"  # 在生产环境中使用更安全的密码
  secret_key: "your-secret-key-here"  # 在生产环境中使用更安全的密钥
  redis_password: ""  # 如果需要Redis认证

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fortune-models-pvc
  namespace: fortune
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: fortune-logs-pvc
  namespace: fortune
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard