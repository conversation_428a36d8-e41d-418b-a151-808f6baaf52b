from fastapi import APIRouter, HTTPException, Depends, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from app.core.model_manager import model_manager
from app.models.user import User
from app.core.deps import get_current_user
from app.utils.enhanced_knowledge_base import enhanced_kb

router = APIRouter(
    prefix="/api",
    tags=["Fortune Telling"],
    responses={
        401: {"description": "认证失败"},
        403: {"description": "权限不足"},
        500: {"description": "服务器内部错误"}
    }
)

class QueryInput(BaseModel):
    query: str = Field(..., description="用户的查询文本")
    context: Optional[Dict[str, Any]] = Field(None, description="查询的上下文信息")

    class Config:
        schema_extra = {
            "example": {
                "query": "请介绍一下人工智能的发展历史",
                "context": {
                    "topic": "AI",
                    "depth": "detailed"
                }
            }
        }

class FortuneInput(BaseModel):
    birth_year: int = Field(..., description="出生年份", example=1990, ge=1900, le=2100)
    birth_month: int = Field(..., description="出生月份", example=1, ge=1, le=12)
    birth_day: int = Field(..., description="出生日期", example=1, ge=1, le=31)
    birth_hour: int = Field(..., description="出生小时", example=12, ge=0, le=23)
    birth_minute: int = Field(..., description="出生分钟", example=0, ge=0, le=59)
    query_type: str = Field(..., description="算命类型", example="bazi")

class YijingInput(BaseModel):
    question: str = Field(..., description="卜卦问题")
    method: str = Field(..., description="占卜方法，可选 'coin' 或 'yarrow'")
    timestamp: Optional[datetime] = Field(None, description="占卜时间")

    class Config:
        schema_extra = {
            "example": {
                "question": "我今年的事业运势如何？",
                "method": "coin",
                "timestamp": "2024-01-01T12:00:00"
            }
        }

class FengshuiInput(BaseModel):
    location: Dict[str, float] = Field(..., description="位置的经纬度")
    house_type: str = Field(..., description="房屋类型")
    facing_direction: str = Field(..., description="房屋朝向")
    birth_info: Optional[Dict[str, Any]] = Field(None, description="出生信息")

    class Config:
        schema_extra = {
            "example": {
                "location": {"latitude": 39.9042, "longitude": 116.4074},
                "house_type": "apartment",
                "facing_direction": "south",
                "birth_info": {"year": 1990, "month": 1, "day": 1}
            }
        }

class WuxingInput(BaseModel):
    birth_datetime: datetime = Field(..., description="出生日期时间")
    name: Optional[str] = Field(None, description="姓名")
    question: Optional[str] = Field(None, description="咨询问题")

    class Config:
        schema_extra = {
            "example": {
                "birth_datetime": "1990-01-01T12:00:00",
                "name": "张三",
                "question": "我的五行属性是什么？"
            }
        }

@router.post(
    "/chat",
    response_model=Dict[str, Any],
    summary="智能对话",
    description="""
    与AI助手进行对话的接口。
    
    功能特点：
    - 支持上下文理解
    - 支持多轮对话
    - 支持知识库检索
    
    限制说明：
    - 单次请求最大token数为2048
    - 响应时间可能因模型处理时间而变化
    """,
    response_description="返回AI助手的回复内容"
)
async def chat_endpoint(
    input_data: QueryInput,
    current_user: User = Depends(get_current_user)
):
    """
    智能对话接口
    
    Args:
        input_data: 用户的查询输入
        current_user: 当前登录用户
        
    Returns:
        Dict[str, Any]: 包含AI响应的字典
    """
    try:
        response = await model_manager.generate_response(
            input_data.query,
            context=input_data.context
        )
        return {
            "status": "success",
            "response": response,
            "request_id": str(datetime.now().timestamp())
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post(
    "/fortune",
    response_model=Dict[str, Any],
    summary="AI占卜预测",
    description="""
    基于用户生辰八字进行AI占卜预测。
    
    支持的占卜类型：
    - bazi: 八字分析
    - ziwei: 紫微斗数
    - general: 综合运势
    
    注意事项：
    - 请确保输入的生日信息准确
    - 占卜结果仅供参考
    """,
    response_description="返回占卜预测结果"
)
async def fortune_telling(
    input_data: FortuneInput,
    current_user: User = Depends(get_current_user)
):
    """
    AI占卜预测接口
    
    Args:
        input_data: 用户的生辰八字信息
        current_user: 当前登录用户
        
    Returns:
        Dict[str, Any]: 包含占卜结果的字典
    """
    try:
        result = await model_manager.generate_fortune(
            birth_info={
                "year": input_data.birth_year,
                "month": input_data.birth_month,
                "day": input_data.birth_day,
                "hour": input_data.birth_hour,
                "minute": input_data.birth_minute
            },
            fortune_type=input_data.query_type
        )
        return {
            "status": "success",
            "fortune": result,
            "request_id": str(datetime.now().timestamp())
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get(
    "/knowledge_categories",
    response_model=Dict[str, Any],
    summary="获取知识库分类",
    description="返回所有可用的知识分类及其统计信息",
    response_description="返回知识分类统计信息"
)
async def get_knowledge_categories():
    """获取知识库分类"""
    return {
        "status": "success",
        "categories": enhanced_kb.get_statistics()
    }

@router.post(
    "/feedback",
    response_model=Dict[str, str],
    summary="提交反馈",
    description="记录用户对回答的反馈信息",
    response_description="返回反馈提交状态"
)
async def submit_feedback(
    entry_id: int = Field(..., description="知识条目ID"),
    is_helpful: bool = Field(..., description="是否有帮助")
):
    """提交反馈"""
    enhanced_kb.record_feedback(entry_id, is_helpful)
    return {"status": "success"}

@router.post(
    "/yijing/divine",
    response_model=Dict[str, Any],
    summary="易经卦象分析",
    description="""
    基于易经进行卦象分析。
    
    支持两种占卜方法：
    - coin: 铜钱起卦
    - yarrow: 蓍草起卦
    """,
    response_description="返回卦象分析结果"
)
async def yijing_divination(input_data: YijingInput):
    """易经卦象分析"""
    try:
        result = model_manager.process_yijing(
            input_data.question,
            input_data.method
        )
        return {
            "status": "success",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post(
    "/fengshui/analyze",
    response_model=Dict[str, Any],
    summary="风水分析",
    description="""
    基于位置信息和房屋特征进行风水分析。
    
    分析内容包括：
    - 房屋坐向分析
    - 环境气场评估
    - 五行配置建议
    """,
    response_description="返回风水分析结果"
)
async def fengshui_analysis(input_data: FengshuiInput):
    """风水分析接口"""
    try:
        result = model_manager.process_fengshui(input_data.dict())
        return {
            "status": "success",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post(
    "/wuxing/analyze",
    response_model=Dict[str, Any],
    summary="五行分析",
    description="""
    基于生辰八字进行五行分析。
    
    分析内容包括：
    - 五行属性
    - 五行平衡状态
    - 调理建议
    """,
    response_description="返回五行分析结果"
)
async def wuxing_analysis(input_data: WuxingInput):
    """五行分析接口"""
    try:
        result = model_manager.process_wuxing(input_data.dict())
        return {
            "status": "success",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 