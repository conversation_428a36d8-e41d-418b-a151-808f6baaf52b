from typing import Dict, Any, List, Optional
import json
from pathlib import Path
from datetime import datetime
from app.utils.document_store import DocumentStoreManager
from app.utils.preprocessor import DocumentPreprocessor
from app.core.config import settings

class KnowledgeBaseManager:
    """
    知识库管理器，支持动态配置和学习
    """
    
    def __init__(
        self,
        base_dir: Path = settings.KNOWLEDGE_DIR,
        document_store: Optional[DocumentStoreManager] = None,
        preprocessor: Optional[DocumentPreprocessor] = None,
    ):
        self.base_dir = base_dir
        self.document_store = document_store or DocumentStoreManager()
        self.preprocessor = preprocessor or DocumentPreprocessor()
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载知识库配置
        self.config_file = self.base_dir / "config.json"
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载知识库配置
        """
        if self.config_file.exists():
            with self.config_file.open("r", encoding="utf-8") as f:
                return json.load(f)
        return {
            "categories": {},
            "sources": {},
            "last_updated": None,
            "total_documents": 0,
        }
    
    def _save_config(self) -> None:
        """
        保存知识库配置
        """
        with self.config_file.open("w", encoding="utf-8") as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def add_knowledge(
        self,
        content: str,
        category: str,
        source: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        添加新知识
        """
        if metadata is None:
            metadata = {}
        
        # 更新配置
        if category not in self.config["categories"]:
            self.config["categories"][category] = {
                "count": 0,
                "last_updated": None,
            }
        if source not in self.config["sources"]:
            self.config["sources"][source] = {
                "count": 0,
                "last_updated": None,
            }
        
        # 处理文档
        doc = self.preprocessor.process_document(
            content,
            meta={
                "category": category,
                "source": source,
                "added_at": datetime.now().isoformat(),
                **metadata,
            }
        )
        
        # 添加到文档存储
        self.document_store.add_documents(doc)
        
        # 更新统计信息
        self.config["categories"][category]["count"] += 1
        self.config["categories"][category]["last_updated"] = datetime.now().isoformat()
        self.config["sources"][source]["count"] += 1
        self.config["sources"][source]["last_updated"] = datetime.now().isoformat()
        self.config["total_documents"] += 1
        self.config["last_updated"] = datetime.now().isoformat()
        
        self._save_config()
    
    def learn_from_feedback(
        self,
        query: str,
        answer: str,
        feedback: bool,
        context: Optional[List[Dict[str, Any]]] = None,
    ) -> None:
        """
        从用户反馈中学习
        """
        feedback_file = self.base_dir / "feedback.jsonl"
        
        feedback_data = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "answer": answer,
            "is_helpful": feedback,
            "context": context,
        }
        
        # 追加反馈记录
        with feedback_file.open("a", encoding="utf-8") as f:
            f.write(json.dumps(feedback_data, ensure_ascii=False) + "\n")
        
        # 如果反馈为正面，可以将问答对添加到知识库
        if feedback and context:
            self.add_knowledge(
                content=f"Q: {query}\nA: {answer}",
                category="learned_qa",
                source="user_feedback",
                metadata={
                    "original_context": context,
                    "feedback_score": 1,
                }
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        """
        return {
            "total_documents": self.config["total_documents"],
            "categories": self.config["categories"],
            "sources": self.config["sources"],
            "last_updated": self.config["last_updated"],
        }
    
    def search_knowledge(
        self,
        query: str,
        category: Optional[str] = None,
        source: Optional[str] = None,
        top_k: int = 5,
    ) -> List[Dict[str, Any]]:
        """
        搜索知识库
        """
        filters = {}
        if category:
            filters["category"] = category
        if source:
            filters["source"] = source
        
        results = self.document_store.get_document_store().query(
            query=query,
            top_k=top_k,
            filters=filters,
        )
        
        return [doc.to_dict() for doc in results]

# 全局知识库管理器实例
knowledge_manager = KnowledgeBaseManager() 