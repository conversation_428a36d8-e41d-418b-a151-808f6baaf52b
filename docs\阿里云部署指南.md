# 卦里乾坤企业微信小程序 - 阿里云部署指南

## 目录

1. [部署方案概述](#部署方案概述)
2. [环境准备](#环境准备)
3. [阿里云ECS部署](#阿里云ECS部署)
4. [阿里云函数计算部署](#阿里云函数计算部署)
5. [域名和SSL配置](#域名和SSL配置)
6. [监控和维护](#监控和维护)

## 部署方案概述

本项目提供两种部署方案：

1. **阿里云ECS部署**
   - 适合需要完全控制服务器环境的场景
   - 支持更复杂的系统配置
   - 可以部署完整的开发环境

2. **阿里云函数计算部署**
   - Serverless架构，按需付费
   - 自动扩缩容
   - 适合流量波动大的场景

## 环境准备

### 1. 基础环境要求

- Python 3.12
- Poetry（依赖管理）
- Git
- 阿里云账号和访问密钥
- 企业微信应用配置

### 2. 安装必要工具

```bash
# 安装 Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装阿里云命令行工具
pip install aliyun-cli

# 安装Serverless Devs工具（用于函数计算）
npm install @serverless-devs/s -g
```

### 3. 配置阿里云凭证

```bash
# 配置阿里云访问密钥
aliyun configure set \
  --access-key-id YOUR_ACCESS_KEY_ID \
  --access-key-secret YOUR_ACCESS_KEY_SECRET \
  --region cn-hangzhou
```

## 阿里云ECS部署

### 1. ECS实例准备

1. 登录阿里云控制台，创建ECS实例
   - 推荐配置：
     - 操作系统：CentOS 7.9
     - 实例规格：至少2核4G
     - 带宽：按需配置

2. 安全组配置：
   ```
   入方向：
   - 80/443端口（HTTP/HTTPS）
   - 22端口（SSH）
   出方向：
   - 全部开放
   ```

### 2. 环境配置

1. 连接到ECS实例：
   ```bash
   ssh root@your_ecs_ip
   ```

2. 运行部署脚本：
   ```bash
   # 克隆项目
   git clone https://github.com/your-repo/chatbot_project.git
   cd chatbot_project

   # 执行部署脚本
   bash deploy/aliyun_ecs_deploy.sh
   ```

3. 配置环境变量：
   ```bash
   # 编辑.env文件
   vim /var/www/chatbot-api/.env

   # 添加必要的环境变量
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=chatbot_db
   DB_USER=chatbot_user
   DB_PASSWORD=your_password
   
   REDIS_HOST=localhost
   REDIS_PORT=6379
   
   WXWORK_CORP_ID=your_corp_id
   WXWORK_CORP_SECRET=your_corp_secret
   WXWORK_AGENT_ID=your_agent_id
   ```

### 3. 服务启动

```bash
# 启动服务
sudo systemctl start chatbot-api
sudo systemctl start nginx

# 检查服务状态
sudo systemctl status chatbot-api
sudo systemctl status nginx
```

## 阿里云函数计算部署

### 1. 函数计算准备

1. 创建服务和函数：
   ```bash
   # 进入项目目录
   cd chatbot_project

   # 运行函数计算部署脚本
   python deploy/aliyun_fc_deploy.py
   ```

2. 配置函数计算触发器：
   - 创建HTTP触发器
   - 配置自定义域名（可选）

### 2. 部署应用

1. 使用Serverless Devs部署：
   ```bash
   # 进入部署目录
   cd deploy/fc

   # 部署应用
   s deploy
   ```

2. 配置环境变量：
   - 在阿里云函数计算控制台配置环境变量
   - 或通过s.yaml文件配置

### 3. 验证部署

1. 测试API接口：
   ```bash
   # 测试健康检查接口
   curl https://your-fc-domain.cn-hangzhou.fc.aliyuncs.com/health
   ```

2. 查看日志和监控：
   - 在函数计算控制台查看执行日志
   - 监控函数执行情况

## 域名和SSL配置

### 1. 域名配置

1. 在阿里云购买域名（如果没有）

2. 配置DNS解析：
   ```
   # ECS部署
   Type: A
   Host: api
   Value: Your_ECS_IP
   
   # 函数计算部署
   Type: CNAME
   Host: api
   Value: your-fc-domain.cn-hangzhou.fc.aliyuncs.com
   ```

### 2. SSL证书

1. 申请SSL证书：
   - 在阿里云SSL证书服务申请免费或付费证书
   - 或使用Let's Encrypt

2. 安装SSL证书：
   ```bash
   # ECS部署 - Nginx配置
   sudo mkdir -p /etc/nginx/ssl
   sudo cp your-cert.pem /etc/nginx/ssl/
   sudo cp your-key.pem /etc/nginx/ssl/
   
   # 更新Nginx配置
   sudo vim /etc/nginx/conf.d/chatbot-api.conf
   sudo nginx -t
   sudo nginx -s reload
   ```

## 监控和维护

### 1. 监控配置

1. 设置告警规则：
   - CPU使用率
   - 内存使用率
   - 请求响应时间
   - 错误率

2. 日志管理：
   - 配置日志服务SLS
   - 设置日志轮转策略

### 2. 备份策略

1. 数据库备份：
   ```bash
   # 创建备份脚本
   vim /root/backup/backup_db.sh

   #!/bin/bash
   DATE=$(date +%Y%m%d)
   mysqldump -u root -p chatbot_db > /root/backup/chatbot_db_$DATE.sql
   ```

2. 配置定时任务：
   ```bash
   # 编辑crontab
   crontab -e

   # 每天凌晨3点执行备份
   0 3 * * * /root/backup/backup_db.sh
   ```

### 3. 更新维护

1. 代码更新流程：
   ```bash
   # ECS部署
   cd /var/www/chatbot-api
   git pull
   poetry install
   sudo systemctl restart chatbot-api

   # 函数计算部署
   s deploy
   ```

2. 定期维护：
   - 系统更新
   - 依赖包更新
   - 安全补丁安装
   - 性能优化

## 故障排查

### 1. 常见问题

1. 服务无法访问：
   - 检查安全组配置
   - 验证服务状态
   - 查看错误日志

2. 数据库连接失败：
   - 检查数据库服务状态
   - 验证连接参数
   - 查看数据库日志

### 2. 日志查看

```bash
# ECS部署
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/gunicorn/error.log

# 函数计算部署
s logs
```

## 联系与支持

如遇到部署问题，请联系技术支持：
- 邮箱：<EMAIL>
- 企业微信群：xxx 