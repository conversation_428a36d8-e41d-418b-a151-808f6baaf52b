"""
主应用程序
卦里乾坤API服务的入口文件
"""
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import time
import uvicorn

# 导入API路由
from app.api.miniprogram_api import router as miniprogram_router
from app.database import init_database, health_check as db_health_check
from app.utils.cache import periodic_cleanup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    # 启动时的初始化
    logger.info("🚀 启动卦里乾坤API服务...")
    
    # 初始化数据库
    try:
        init_success = init_database()
        if not init_success:
            logger.error("数据库初始化失败")
        else:
            logger.info("✅ 数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化异常: {e}")
    
    # 启动缓存清理任务
    # asyncio.create_task(periodic_cleanup())
    logger.info("✅ 缓存清理任务已启动")
    
    logger.info("🎉 卦里乾坤API服务启动完成")
    
    yield
    
    # 关闭时的清理
    logger.info("👋 正在关闭卦里乾坤API服务...")
    logger.info("✅ 服务关闭完成")

# 创建FastAPI应用实例
app = FastAPI(
    title="卦里乾坤API",
    description="专业的命理测算和AI咨询服务API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # 开发环境
        "https://your-domain.com",  # 生产环境域名
        "*"  # 临时允许所有（生产环境需要限制）
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件（生产环境推荐）
# app.add_middleware(
#     TrustedHostMiddleware, 
#     allowed_hosts=["your-domain.com", "*.your-domain.com"]
# )

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url.path} - {request.client.host}")
    
    # 处理请求
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        logger.info(f"📤 {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
        
        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        # 记录错误
        process_time = time.time() - start_time
        logger.error(f"❌ {request.method} {request.url.path} - 错误: {e} - {process_time:.3f}s")
        raise

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常捕获: {type(exc).__name__}: {exc}")
    
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.detail,
                "type": "http_exception"
            }
        )
    
    # 未知异常
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "服务器内部错误，请稍后重试",
            "type": "internal_server_error"
        }
    )

# 404处理器
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """404错误处理"""
    return JSONResponse(
        status_code=404,
        content={
            "error": True,
            "message": "请求的资源不存在",
            "path": request.url.path,
            "type": "not_found"
        }
    )

# 根路径
@app.get("/", tags=["基础"])
async def root():
    """API根路径"""
    return {
        "message": "🔮 欢迎使用卦里乾坤API服务",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

# 健康检查
@app.get("/health", tags=["基础"])
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库
        db_status = db_health_check()
        
        # 检查缓存
        from app.utils.cache import cache_manager
        cache_stats = cache_manager.get_stats()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "database": db_status,
            "cache": {
                "status": "healthy",
                "stats": cache_stats
            },
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": time.time(),
                "error": str(e)
            }
        )

# API信息
@app.get("/api/info", tags=["基础"])
async def api_info():
    """获取API信息"""
    return {
        "name": "卦里乾坤API",
        "version": "1.0.0",
        "description": "专业的命理测算和AI咨询服务",
        "features": [
            "微信小程序登录认证",
            "八字命理分析",
            "易经卦象占卜",
            "风水布局分析",
            "紫微斗数分析",
            "AI智能聊天",
            "运势预测查询",
            "用户数据管理"
        ],
        "endpoints": {
            "miniprogram": "/api/miniprogram",
            "docs": "/docs",
            "health": "/health"
        }
    }

# 注册路由
app.include_router(
    miniprogram_router,
    tags=["小程序API"]
)

# 添加更多路由组（如果需要）
# app.include_router(admin_router, prefix="/api/admin", tags=["管理后台"])
# app.include_router(webhook_router, prefix="/api/webhook", tags=["Webhook"])

if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 