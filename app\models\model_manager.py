from typing import Dict, Any, List, Optional
from app.core.schema import Document, Answer
from app.core.config import settings
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
import logging

logger = logging.getLogger(__name__)

class ModelManager:
    """
    模型管理器
    
    负责模型的加载、切换和推理，
    支持多种模型类型和参数配置。
    """
    
    def __init__(self):
        """初始化模型管理器"""
        self.model = None
        self.tokenizer = None
        self.device = settings.MODEL_DEVICE
        self.load_model()
        
    def load_model(self) -> None:
        """加载模型和分词器"""
        try:
            logger.info(f"Loading model from {settings.MODEL_PATH}")
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                settings.MODEL_PATH,
                trust_remote_code=True,
                torch_dtype=torch.float16 if settings.MODEL_PRECISION == "fp16" else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None
            )
            
            # 加载分词器
            self.tokenizer = AutoTokenizer.from_pretrained(
                settings.MODEL_PATH,
                trust_remote_code=True
            )
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}", exc_info=True)
            raise
            
    def generate_answer(
        self,
        query: str,
        documents: List[Document],
        params: Optional[Dict[str, Any]] = None
    ) -> Answer:
        """
        生成答案
        
        Args:
            query: 用户问题
            documents: 相关文档
            params: 生成参数
            
        Returns:
            生成的答案
        """
        try:
            # 准备输入
            context = "\n".join(doc.content for doc in documents)
            input_text = f"Question: {query}\nContext: {context}\nAnswer:"
            
            # 设置生成参数
            gen_params = {
                "max_length": params.get("max_length", 2048),
                "top_p": params.get("top_p", 0.7),
                "temperature": params.get("temperature", 0.95),
                "do_sample": params.get("do_sample", True),
                "num_return_sequences": 1
            }
            
            # 编码输入
            inputs = self.tokenizer(
                input_text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=gen_params["max_length"]
            ).to(self.model.device)
            
            # 生成答案
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    **gen_params
                )
                
            # 解码输出
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 构建答案对象
            answer = Answer(
                answer=response,
                score=1.0,  # 可以根据需要计算置信度
                meta={
                    "model": settings.MODEL_PATH,
                    "params": gen_params
                }
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Error generating answer: {str(e)}", exc_info=True)
            raise
            
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": settings.MODEL_PATH,
            "device": self.device,
            "precision": settings.MODEL_PRECISION
        }

# 创建全局模型管理器实例
model_manager = ModelManager() 