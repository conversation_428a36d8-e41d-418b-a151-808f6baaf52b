# 使用Python官方镜像作为基础镜像
FROM python:3.8-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
COPY scripts ./scripts
COPY app ./app
COPY config ./config
COPY run.sh .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 下载模型（如果需要）
RUN python scripts/download_model.py

# 暴露端口
EXPOSE 8085

# 启动命令
CMD ["./run.sh"] 