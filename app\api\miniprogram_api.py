"""
小程序专用API接口
支持卦里乾坤小程序的所有功能模块
"""
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, date
import hashlib
import jwt
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User, DivinationRecord, UserProfile
from app.services.bazi_service import BaziService
from app.services.yijing_service import YijingService
from app.services.fengshui_service import FengshuiService
from app.services.ziwei_service import ZiweiService
from app.services.ai_chat_service import AIChatService
from app.services.api_client import APIClient, get_api_client
from app.models.api_models import (
    APIResponse, WxLoginRequest, WxLoginResponse, UserInfo,
    BirthInfoRequest, BirthInfo, AIChatRequest, AIChatResponse,
    AnalysisRecord, PointBalance, PointRecord, KnowledgeSearchRequest,
    MessageType, AnalysisType, PointType, PointSource
)
from app.utils.auth import verify_token, create_access_token
from app.utils.cache import cache_result
import logging
from app.core.ai_config import get_ai_status

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/miniprogram", tags=["小程序接口"])
security = HTTPBearer()

# ===== 请求/响应模型 =====

class WxLoginRequest(BaseModel):
    """微信小程序登录请求"""
    code: str = Field(..., description="微信登录code")
    user_info: Optional[Dict] = Field(None, description="用户信息")

class UserInfoResponse(BaseModel):
    """用户信息响应"""
    user_id: str
    nickname: str
    avatar_url: str
    gender: Optional[int] = None
    points: int = 0
    level: int = 1
    created_at: datetime

class BirthInfoRequest(BaseModel):
    """出生信息请求"""
    year: int = Field(..., ge=1900, le=2100, description="出生年份")
    month: int = Field(..., ge=1, le=12, description="出生月份")
    day: int = Field(..., ge=1, le=31, description="出生日期")
    hour: int = Field(..., ge=0, le=23, description="出生时辰")
    minute: int = Field(0, ge=0, le=59, description="出生分钟")
    gender: int = Field(..., ge=0, le=1, description="性别：0女，1男")
    lunar: bool = Field(False, description="是否农历")
    timezone: str = Field("Asia/Shanghai", description="时区")

class BaziAnalysisRequest(BaseModel):
    """八字分析请求"""
    birth_info: BirthInfoRequest
    analysis_type: str = Field("basic", description="分析类型：basic/detailed/marriage")

class BaziAnalysisResponse(BaseModel):
    """八字分析响应"""
    bazi_chart: Dict = Field(..., description="八字排盘")
    five_elements: Dict = Field(..., description="五行分析")
    personality: Dict = Field(..., description="性格特征")
    career: Dict = Field(..., description="事业运势")
    wealth: Dict = Field(..., description="财运分析")
    health: Dict = Field(..., description="健康状况")
    marriage: Dict = Field(..., description="婚姻感情")
    lucky_elements: List[str] = Field(..., description="幸运元素")
    advice: str = Field(..., description="建议指导")
    score: int = Field(..., ge=0, le=100, description="综合评分")

class YijingDivinationRequest(BaseModel):
    """易经占卜请求"""
    question: str = Field(..., min_length=1, max_length=200, description="占卜问题")
    method: str = Field("manual", description="起卦方式：manual/auto/time")
    coins: Optional[List[int]] = Field(None, description="手动投币结果")

class YijingDivinationResponse(BaseModel):
    """易经占卜响应"""
    hexagram: Dict = Field(..., description="卦象信息")
    interpretation: Dict = Field(..., description="卦象解读")
    advice: str = Field(..., description="指导建议")
    score: int = Field(..., ge=0, le=100, description="吉凶评分")
    lucky_elements: List[str] = Field(..., description="有利因素")

class FengshuiAnalysisRequest(BaseModel):
    """风水分析请求"""
    house_type: str = Field(..., description="房屋类型：apartment/house/office")
    direction: str = Field(..., description="朝向：north/south/east/west/northeast/northwest/southeast/southwest")
    rooms: List[Dict] = Field(..., description="房间布局信息")
    birth_info: Optional[BirthInfoRequest] = Field(None, description="主人出生信息")

class FengshuiAnalysisResponse(BaseModel):
    """风水分析响应"""
    overall_score: int = Field(..., ge=0, le=100, description="整体评分")
    room_analysis: List[Dict] = Field(..., description="各房间分析")
    suggestions: List[Dict] = Field(..., description="改善建议")
    lucky_directions: List[str] = Field(..., description="吉位方向")
    colors: List[str] = Field(..., description="推荐颜色")
    items: List[str] = Field(..., description="推荐物品")

class ZiweiAnalysisRequest(BaseModel):
    """紫微斗数分析请求"""
    birth_info: BirthInfoRequest
    analysis_type: str = Field("basic", description="分析类型：basic/detailed/yearly")

class ZiweiAnalysisResponse(BaseModel):
    """紫微斗数分析响应"""
    main_star: str = Field(..., description="主星")
    palace_analysis: Dict = Field(..., description="十二宫分析")
    yearly_fortune: Dict = Field(..., description="流年运势")
    personality: Dict = Field(..., description="性格特质")
    career: Dict = Field(..., description="事业发展")
    wealth: Dict = Field(..., description="财富状况")
    relationships: Dict = Field(..., description="人际关系")
    health: Dict = Field(..., description="健康状况")
    advice: str = Field(..., description="生活建议")
    score: int = Field(..., ge=0, le=100, description="综合评分")

class AIChatRequest(BaseModel):
    """AI聊天请求"""
    message: str = Field(..., min_length=1, max_length=500, description="用户消息")
    context: Optional[List[Dict]] = Field(None, description="聊天上下文")
    feature_type: Optional[str] = Field(None, description="功能类型：bazi/yijing/fengshui/general")

class AIChatResponse(BaseModel):
    """AI聊天响应"""
    response: str = Field(..., description="AI回复内容")
    suggestions: List[str] = Field([], description="建议问题")
    function_calls: Optional[List[Dict]] = Field(None, description="功能调用建议")

class FortuneRequest(BaseModel):
    """运势查询请求"""
    fortune_type: str = Field(..., description="运势类型：daily/weekly/monthly/yearly")
    birth_info: Optional[BirthInfoRequest] = Field(None, description="出生信息")
    date: Optional[date] = Field(None, description="查询日期")

class FortuneResponse(BaseModel):
    """运势查询响应"""
    period: str = Field(..., description="时期")
    overall_score: int = Field(..., ge=0, le=100, description="总体运势")
    career: int = Field(..., ge=0, le=5, description="事业运")
    wealth: int = Field(..., ge=0, le=5, description="财运")
    love: int = Field(..., ge=0, le=5, description="感情运")
    health: int = Field(..., ge=0, le=5, description="健康运")
    summary: str = Field(..., description="运势总结")
    advice: str = Field(..., description="运势建议")
    lucky_numbers: List[int] = Field(..., description="幸运数字")
    lucky_colors: List[str] = Field(..., description="幸运颜色")

# ===== 依赖注入 =====

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """获取当前用户"""
    try:
        token = credentials.credentials
        payload = verify_token(token)
        user_id = payload.get("user_id")
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在"
            )
        return user
    except Exception as e:
        logger.error(f"Token验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败"
        )

# ===== 用户认证接口 =====

@router.post("/auth/wx-login", response_model=Dict)
async def wx_login(request: WxLoginRequest, db: Session = Depends(get_db)):
    """
    微信小程序登录
    通过微信code换取openid并创建/更新用户信息
    """
    try:
        # TODO: 调用微信API获取openid和session_key
        # 这里使用模拟数据
        openid = f"mock_openid_{hashlib.md5(request.code.encode()).hexdigest()[:8]}"
        
        # 查找或创建用户
        user = db.query(User).filter(User.openid == openid).first()
        
        if not user:
            # 创建新用户
            user = User(
                openid=openid,
                nickname=request.user_info.get("nickName", "用户") if request.user_info else "用户",
                avatar_url=request.user_info.get("avatarUrl", "") if request.user_info else "",
                gender=request.user_info.get("gender", 0) if request.user_info else 0,
                points=100,  # 新用户赠送积分
                level=1
            )
            db.add(user)
            db.commit()
            db.refresh(user)
        else:
            # 更新用户信息
            if request.user_info:
                user.nickname = request.user_info.get("nickName", user.nickname)
                user.avatar_url = request.user_info.get("avatarUrl", user.avatar_url)
                user.last_login = datetime.now()
                db.commit()
        
        # 生成访问令牌
        access_token = create_access_token({"user_id": str(user.id)})
        
        return {
            "status": "success",
            "access_token": access_token,
            "user_info": {
                "user_id": str(user.id),
                "nickname": user.nickname,
                "avatar_url": user.avatar_url,
                "points": user.points,
                "level": user.level
            }
        }
        
    except Exception as e:
        logger.error(f"微信登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请重试"
        )

@router.get("/auth/user-info", response_model=UserInfoResponse)
async def get_user_info(current_user: User = Depends(get_current_user)):
    """获取用户信息"""
    return UserInfoResponse(
        user_id=str(current_user.id),
        nickname=current_user.nickname,
        avatar_url=current_user.avatar_url,
        gender=current_user.gender,
        points=current_user.points,
        level=current_user.level,
        created_at=current_user.created_at
    )

# ===== 出生信息管理 =====

@router.post("/birth-info")
async def save_birth_info(
    birth_info: BirthInfoRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """保存用户出生信息"""
    try:
        # 查找或创建用户档案
        profile = db.query(UserProfile).filter(UserProfile.user_id == current_user.id).first()
        
        if not profile:
            profile = UserProfile(user_id=current_user.id)
            db.add(profile)
        
        # 更新出生信息
        profile.birth_year = birth_info.year
        profile.birth_month = birth_info.month
        profile.birth_day = birth_info.day
        profile.birth_hour = birth_info.hour
        profile.birth_minute = birth_info.minute
        profile.gender = birth_info.gender
        profile.is_lunar = birth_info.lunar
        profile.timezone = birth_info.timezone
        profile.updated_at = datetime.now()
        
        db.commit()
        
        return {"status": "success", "message": "出生信息保存成功"}
        
    except Exception as e:
        logger.error(f"保存出生信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存失败，请重试"
        )

@router.get("/birth-info")
async def get_birth_info(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """获取用户出生信息"""
    profile = db.query(UserProfile).filter(UserProfile.user_id == current_user.id).first()
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="请先完善出生信息"
        )
    
    return {
        "year": profile.birth_year,
        "month": profile.birth_month,
        "day": profile.birth_day,
        "hour": profile.birth_hour,
        "minute": profile.birth_minute,
        "gender": profile.gender,
        "lunar": profile.is_lunar,
        "timezone": profile.timezone
    }

# ===== 八字分析接口 =====

@router.post("/bazi/analysis", response_model=BaziAnalysisResponse)
@cache_result(expire=3600)  # 缓存1小时
async def bazi_analysis(
    request: BaziAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """八字命理分析"""
    try:
        # 初始化八字服务
        bazi_service = BaziService()
        
        # 执行八字分析
        result = await bazi_service.analyze(
            birth_info=request.birth_info.dict(),
            analysis_type=request.analysis_type
        )
        
        # 保存分析记录
        record = DivinationRecord(
            user_id=current_user.id,
            type="bazi",
            request_data=request.dict(),
            result_data=result,
            created_at=datetime.now()
        )
        db.add(record)
        db.commit()
        
        return BaziAnalysisResponse(**result)
        
    except Exception as e:
        logger.error(f"八字分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分析失败，请重试"
        )

# ===== 易经占卜接口 =====

@router.post("/yijing/divination", response_model=YijingDivinationResponse)
async def yijing_divination(
    request: YijingDivinationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """易经卦象占卜"""
    try:
        # 初始化易经服务
        yijing_service = YijingService()
        
        # 执行占卜分析
        result = await yijing_service.divine(
            question=request.question,
            method=request.method,
            coins=request.coins
        )
        
        # 保存占卜记录
        record = DivinationRecord(
            user_id=current_user.id,
            type="yijing",
            request_data=request.dict(),
            result_data=result,
            created_at=datetime.now()
        )
        db.add(record)
        db.commit()
        
        return YijingDivinationResponse(**result)
        
    except Exception as e:
        logger.error(f"易经占卜失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="占卜失败，请重试"
        )

# ===== 风水分析接口 =====

@router.post("/fengshui/analysis", response_model=FengshuiAnalysisResponse)
async def fengshui_analysis(
    request: FengshuiAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """风水布局分析"""
    try:
        # 初始化风水服务
        fengshui_service = FengshuiService()
        
        # 执行风水分析
        result = await fengshui_service.analyze(
            house_type=request.house_type,
            direction=request.direction,
            rooms=request.rooms,
            birth_info=request.birth_info.dict() if request.birth_info else None
        )
        
        # 保存分析记录
        record = DivinationRecord(
            user_id=current_user.id,
            type="fengshui",
            request_data=request.dict(),
            result_data=result,
            created_at=datetime.now()
        )
        db.add(record)
        db.commit()
        
        return FengshuiAnalysisResponse(**result)
        
    except Exception as e:
        logger.error(f"风水分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分析失败，请重试"
        )

# ===== 紫微斗数接口 =====

@router.post("/ziwei/analysis", response_model=ZiweiAnalysisResponse)
async def ziwei_analysis(
    request: ZiweiAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """紫微斗数分析"""
    try:
        # 初始化紫微服务
        ziwei_service = ZiweiService()
        
        # 执行紫微分析
        result = await ziwei_service.analyze(
            birth_info=request.birth_info.dict(),
            analysis_type=request.analysis_type
        )
        
        # 保存分析记录
        record = DivinationRecord(
            user_id=current_user.id,
            type="ziwei",
            request_data=request.dict(),
            result_data=result,
            created_at=datetime.now()
        )
        db.add(record)
        db.commit()
        
        return ZiweiAnalysisResponse(**result)
        
    except Exception as e:
        logger.error(f"紫微斗数分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="分析失败，请重试"
        )

# ===== AI聊天接口 =====

@router.post("/ai/chat", response_model=AIChatResponse)
async def ai_chat(
    request: AIChatRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """AI智能聊天"""
    try:
        # 初始化AI聊天服务
        ai_service = AIChatService()
        
        # 获取用户档案以提供个性化服务
        profile = db.query(UserProfile).filter(UserProfile.user_id == current_user.id).first()
        
        # 执行AI对话
        result = await ai_service.chat(
            message=request.message,
            context=request.context,
            feature_type=request.feature_type,
            user_profile=profile.dict() if profile else None
        )
        
        # 添加AI状态信息到响应中
        ai_status = get_ai_status()
        result["ai_status"] = {
            "enabled": ai_status["enabled"],
            "provider": ai_status["default_provider"] if ai_status["enabled"] else None
        }
        
        return AIChatResponse(**result)
        
    except Exception as e:
        logger.error(f"AI聊天失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="AI暂时无法回复，请稍后重试"
        )

# ===== 运势查询接口 =====

@router.post("/fortune/query", response_model=FortuneResponse)
@cache_result(expire=1800)  # 缓存30分钟
async def fortune_query(
    request: FortuneRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """运势查询"""
    try:
        # 获取用户出生信息
        birth_info = None
        if request.birth_info:
            birth_info = request.birth_info.dict()
        else:
            profile = db.query(UserProfile).filter(UserProfile.user_id == current_user.id).first()
            if profile:
                birth_info = {
                    "year": profile.birth_year,
                    "month": profile.birth_month,
                    "day": profile.birth_day,
                    "hour": profile.birth_hour,
                    "minute": profile.birth_minute,
                    "gender": profile.gender,
                    "lunar": profile.is_lunar,
                    "timezone": profile.timezone
                }
        
        if not birth_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先完善出生信息"
            )
        
        # 初始化运势服务（可以复用八字服务）
        bazi_service = BaziService()
        
        # 查询运势
        result = await bazi_service.get_fortune(
            birth_info=birth_info,
            fortune_type=request.fortune_type,
            query_date=request.date
        )
        
        return FortuneResponse(**result)
        
    except Exception as e:
        logger.error(f"运势查询失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询失败，请重试"
        )

# ===== 用户数据管理 =====

@router.get("/user/records")
async def get_user_records(
    page: int = 1,
    size: int = 10,
    record_type: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户测算记录"""
    try:
        query = db.query(DivinationRecord).filter(DivinationRecord.user_id == current_user.id)
        
        if record_type:
            query = query.filter(DivinationRecord.type == record_type)
        
        total = query.count()
        records = query.offset((page - 1) * size).limit(size).all()
        
        return {
            "total": total,
            "page": page,
            "size": size,
            "records": [
                {
                    "id": record.id,
                    "type": record.type,
                    "created_at": record.created_at,
                    "summary": record.result_data.get("summary", "")[:100] if record.result_data else ""
                }
                for record in records
            ]
        }
        
    except Exception as e:
        logger.error(f"获取用户记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取记录失败"
        )

@router.get("/user/stats")
async def get_user_stats(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """获取用户统计信息"""
    try:
        # 查询用户各类记录数量
        total_records = db.query(DivinationRecord).filter(DivinationRecord.user_id == current_user.id).count()
        bazi_count = db.query(DivinationRecord).filter(
            DivinationRecord.user_id == current_user.id,
            DivinationRecord.type == "bazi"
        ).count()
        yijing_count = db.query(DivinationRecord).filter(
            DivinationRecord.user_id == current_user.id,
            DivinationRecord.type == "yijing"
        ).count()
        
        return {
            "total_tests": total_records,
            "bazi_tests": bazi_count,
            "yijing_tests": yijing_count,
            "points": current_user.points,
            "level": current_user.level,
            "member_days": (datetime.now() - current_user.created_at).days
        }
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计失败"
        )

# ===== 健康检查 =====

@router.get("/health")
async def health_check():
    """健康检查接口"""
    # 检查AI服务状态
    ai_status = get_ai_status()
    
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "1.0.0",
        "ai_service": ai_status
    } 