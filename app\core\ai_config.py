"""
AI配置管理模块
支持多种AI提供商的配置和管理
"""
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class AIModelConfig:
    """AI模型配置类"""
    provider: str  # openai, ernie, local, ollama
    model_name: str
    api_key: str
    base_url: str
    max_tokens: int = 2048
    temperature: float = 0.7
    timeout: int = 30
    enabled: bool = True

class AIConfigManager:
    """AI配置管理器"""
    
    def __init__(self):
        self.configs = {}
        self.default_provider = "local"
        self.load_configs()
    
    def load_configs(self):
        """加载AI配置"""
        try:
            # OpenAI配置
            self.configs["openai"] = AIModelConfig(
                provider="openai",
                model_name=os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
                api_key=os.getenv("OPENAI_API_KEY", ""),
                base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
                max_tokens=int(os.getenv("OPENAI_MAX_TOKENS", "2048")),
                temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.7")),
                enabled=bool(os.getenv("OPENAI_ENABLED", "false").lower() == "true")
            )
            
            # 百度文心一言配置
            self.configs["ernie"] = AIModelConfig(
                provider="ernie",
                model_name=os.getenv("ERNIE_MODEL", "ernie-bot"),
                api_key=os.getenv("ERNIE_API_KEY", ""),
                base_url=os.getenv("ERNIE_BASE_URL", "https://aip.baidubce.com"),
                max_tokens=int(os.getenv("ERNIE_MAX_TOKENS", "2048")),
                temperature=float(os.getenv("ERNIE_TEMPERATURE", "0.7")),
                enabled=bool(os.getenv("ERNIE_ENABLED", "false").lower() == "true")
            )
            
            # 本地Ollama配置
            self.configs["ollama"] = AIModelConfig(
                provider="ollama",
                model_name=os.getenv("OLLAMA_MODEL", "llama2"),
                api_key="",  # Ollama不需要API密钥
                base_url=os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                max_tokens=int(os.getenv("OLLAMA_MAX_TOKENS", "2048")),
                temperature=float(os.getenv("OLLAMA_TEMPERATURE", "0.7")),
                enabled=bool(os.getenv("OLLAMA_ENABLED", "false").lower() == "true")
            )
            
            # 本地模拟配置
            self.configs["local"] = AIModelConfig(
                provider="local",
                model_name="local-fallback",
                api_key="",
                base_url="",
                max_tokens=2048,
                temperature=0.7,
                enabled=True  # 本地回退总是启用
            )
            
            # 确定默认提供商
            for provider in ["openai", "ernie", "ollama"]:
                if self.configs[provider].enabled and self.configs[provider].api_key:
                    self.default_provider = provider
                    break
            
            logger.info(f"AI配置加载完成，默认提供商：{self.default_provider}")
            
        except Exception as e:
            logger.error(f"加载AI配置失败：{e}")
            # 确保至少有本地回退
            if "local" not in self.configs:
                self.configs["local"] = AIModelConfig(
                    provider="local",
                    model_name="local-fallback",
                    api_key="",
                    base_url="",
                    enabled=True
                )
    
    def get_config(self, provider: Optional[str] = None) -> Optional[AIModelConfig]:
        """获取AI配置"""
        if provider is None:
            provider = self.default_provider
        
        config = self.configs.get(provider)
        if not config or not config.enabled:
            # 如果指定的提供商不可用，返回本地回退
            return self.configs.get("local")
        
        return config
    
    def is_available(self, provider: Optional[str] = None) -> bool:
        """检查AI提供商是否可用"""
        config = self.get_config(provider)
        if not config:
            return False
        
        # 本地提供商总是可用
        if config.provider == "local":
            return True
        
        # 其他提供商需要API密钥
        return config.enabled and bool(config.api_key)
    
    def get_status(self) -> Dict[str, Any]:
        """获取AI状态信息"""
        status = {
            "enabled": False,
            "default_provider": self.default_provider,
            "providers": {}
        }
        
        for provider, config in self.configs.items():
            provider_status = {
                "enabled": config.enabled,
                "available": self.is_available(provider),
                "model": config.model_name
            }
            
            if config.provider != "local":
                provider_status["has_api_key"] = bool(config.api_key)
            
            status["providers"][provider] = provider_status
            
            # 如果任何提供商可用，则AI服务启用
            if provider_status["available"]:
                status["enabled"] = True
        
        return status

# 全局AI配置管理器实例
_ai_config_manager = AIConfigManager()

def get_ai_config(provider: Optional[str] = None) -> Optional[AIModelConfig]:
    """获取AI配置"""
    return _ai_config_manager.get_config(provider)

def is_ai_available(provider: Optional[str] = None) -> bool:
    """检查AI是否可用"""
    return _ai_config_manager.is_available(provider)

def get_ai_status() -> Dict[str, Any]:
    """获取AI状态"""
    return _ai_config_manager.get_status()

def reload_ai_config():
    """重新加载AI配置"""
    global _ai_config_manager
    _ai_config_manager = AIConfigManager()
    logger.info("AI配置已重新加载") 