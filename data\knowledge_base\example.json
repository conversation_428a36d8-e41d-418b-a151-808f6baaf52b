[{"question": "什么是 ChatGLM-6B？", "content": "ChatGLM-6B 是一个开源的、支持中英双语的对话语言模型，基于 General Language Model (GLM) 架构，具有 62 亿参数。它使用了和 ChatGPT 相似的技术，针对中文进行了优化。该模型具有如下特点：1. 开源开放：完整开源模型参数，允许研究者和开发者进行二次开发；2. 中英双语：同时具备中文和英文的对话能力；3. 性能优化：支持 INT4/INT8 量化，可在消费级显卡上运行。", "category": "模型介绍", "meta": {"source": "官方文档", "date": "2023"}}, {"question": "如何使用 Haystack 框架？", "content": "Haystack 是一个端到端的框架，用于构建基于大型语言模型和 Transformer 的应用。主要步骤包括：1. 文档存储：使用 DocumentStore 存储和管理文档；2. 检索：使用 Retriever 组件检索相关文档；3. 生成：使用 Generator 组件生成答案。Haystack 支持多种文档存储后端（Elasticsearch、FAISS等）和不同的检索方法（BM25、Dense Passage Retrieval等）。", "category": "框架使用", "meta": {"source": "Haystack 文档", "date": "2023"}}, {"question": "什么是语义检索？", "content": "语义检索是一种基于深度学习的文本检索方法，它通过理解文本的语义而不是简单的关键词匹配来找到相关文档。在 Haystack 中，可以使用 DensePassageRetriever 实现语义检索，它会将查询和文档转换为稠密向量，然后使用向量相似度计算来找到最相关的文档。相比传统的基于关键词的检索（如 BM25），语义检索能够更好地理解查询的上下文和意图。", "category": "技术原理", "meta": {"source": "技术博客", "date": "2023"}}]