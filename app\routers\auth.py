from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2PasswordBearer, OAuth2PasswordRequestForm
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, EmailStr, Field
from jose import jwt
from app.core.config import settings
from app.models.user import User, UserCreate
from app.core.security import create_access_token, get_password_hash, verify_password
from app.core.deps import get_current_user
from app.db.session import get_db

router = APIRouter(
    prefix="/api/auth",
    tags=["Authentication"],
    responses={
        401: {"description": "认证失败"},
        403: {"description": "权限不足"},
        404: {"description": "资源不存在"}
    }
)

class Token(BaseModel):
    access_token: str = Field(..., description="JWT访问令牌")
    token_type: str = Field(..., description="令牌类型，固定为'bearer'")

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer"
            }
        }

class TokenData(BaseModel):
    username: Optional[str] = Field(None, description="用户名")

class WeChatLoginRequest(BaseModel):
    code: str = Field(..., description="微信登录code")
    user_info: Optional[Dict[str, Any]] = Field(None, description="微信用户信息")

    class Config:
        schema_extra = {
            "example": {
                "code": "0819xKll2TMK874Qxjol2g3r0P29xKlW",
                "user_info": {
                    "nickName": "张三",
                    "avatarUrl": "https://example.com/avatar.jpg"
                }
            }
        }

@router.post(
    "/register",
    response_model=User,
    summary="用户注册",
    description="注册新用户并返回用户信息",
    response_description="返回新创建的用户信息"
)
async def register(user: UserCreate, db = Depends(get_db)):
    """
    注册新用户
    
    Args:
        user: 用户注册信息，包含邮箱、密码等
        
    Returns:
        User: 新创建的用户信息
        
    Raises:
        HTTPException: 当邮箱已被注册时
    """
    # 检查用户是否已存在
    db_user = await db.user.get_by_email(user.email)
    if db_user:
        raise HTTPException(
            status_code=400,
            detail="Email already registered"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user.password)
    db_user = await db.user.create(
        email=user.email,
        hashed_password=hashed_password,
        username=user.username,
        full_name=user.full_name
    )
    return db_user

@router.post(
    "/login",
    response_model=Token,
    summary="用户登录",
    description="使用邮箱和密码登录，返回JWT访问令牌",
    response_description="返回JWT访问令牌"
)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db = Depends(get_db)
):
    """
    用户登录
    
    Args:
        form_data: 表单数据，包含用户名(邮箱)和密码
        
    Returns:
        Token: JWT访问令牌
        
    Raises:
        HTTPException: 当邮箱或密码错误时
    """
    user = await db.user.authenticate(
        email=form_data.username,
        password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = create_access_token(
        data={"sub": user.email}
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.post(
    "/wx-login",
    response_model=Token,
    summary="微信登录",
    description="使用微信code登录，返回JWT访问令牌",
    response_description="返回JWT访问令牌"
)
async def wechat_login(
    request: WeChatLoginRequest,
    db = Depends(get_db)
):
    """
    微信登录
    
    Args:
        request: 微信登录请求，包含code和用户信息
        
    Returns:
        Token: JWT访问令牌
        
    Raises:
        HTTPException: 当微信code无效时
    """
    # 获取微信用户信息
    wx_user = await get_wechat_user_info(request.code)
    if not wx_user:
        raise HTTPException(
            status_code=400,
            detail="Invalid WeChat code"
        )
    
    # 获取或创建用户
    user = await get_or_create_wx_user(
        db=db,
        openid=wx_user.openid,
        user_info=request.user_info
    )
    
    # 创建访问令牌
    access_token = create_access_token(
        data={"sub": user.email}
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get(
    "/me",
    response_model=User,
    summary="获取当前用户信息",
    description="获取当前登录用户的详细信息",
    response_description="返回当前用户信息"
)
async def read_users_me(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息
    
    Args:
        current_user: 当前登录用户，通过token获取
        
    Returns:
        User: 当前用户信息
    """
    return current_user

@router.post(
    "/refresh",
    response_model=Token,
    summary="刷新访问令牌",
    description="使用当前有效的访问令牌获取新的访问令牌",
    response_description="返回新的JWT访问令牌"
)
async def refresh_token(
    current_user: User = Depends(get_current_user)
):
    """
    刷新访问令牌
    
    Args:
        current_user: 当前登录用户，通过token获取
        
    Returns:
        Token: 新的JWT访问令牌
    """
    access_token = create_access_token(
        data={"sub": current_user.email}
    )
    return {"access_token": access_token, "token_type": "bearer"} 