#!/bin/bash

# 阿里云ECS服务器部署脚本
# 用于部署卦里乾坤企业微信小程序API服务

echo "开始阿里云ECS部署..."

# 1. 更新系统
sudo yum update -y

# 2. 安装Python 3.9+
sudo yum install -y python3 python3-pip python3-devel

# 3. 安装Nginx
sudo yum install -y nginx

# 4. 安装Redis
sudo yum install -y redis
sudo systemctl start redis
sudo systemctl enable redis

# 5. 安装MySQL
sudo yum install -y mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 6. 创建项目目录
sudo mkdir -p /var/www/chatbot-api
sudo chown -R $USER:$USER /var/www/chatbot-api
cd /var/www/chatbot-api

# 7. 克隆项目代码（假设已上传到git仓库）
# git clone https://github.com/your-repo/chatbot_project.git .

# 8. 复制项目文件（如果是手动上传）
echo "请将项目文件上传到 /var/www/chatbot-api 目录"

# 9. 安装Python依赖
pip3 install -r requirements.txt
pip3 install gunicorn

# 10. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install gunicorn

# 11. 配置环境变量
cat > .env << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=chatbot_db
DB_USER=chatbot_user
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# API配置
API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your_secret_key_here

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your_access_key
ALIYUN_ACCESS_KEY_SECRET=your_access_secret
ALIYUN_REGION=cn-hangzhou

# 企业微信配置
WXWORK_CORP_ID=your_corp_id
WXWORK_CORP_SECRET=your_corp_secret
WXWORK_AGENT_ID=your_agent_id
EOF

# 12. 创建Gunicorn配置
cat > gunicorn.conf.py << EOF
# Gunicorn配置文件
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
user = "$USER"
group = "$USER"
daemon = False
pidfile = "/var/run/gunicorn.pid"
accesslog = "/var/log/gunicorn/access.log"
errorlog = "/var/log/gunicorn/error.log"
loglevel = "info"
EOF

# 13. 创建日志目录
sudo mkdir -p /var/log/gunicorn
sudo chown -R $USER:$USER /var/log/gunicorn

# 14. 创建systemd服务文件
sudo tee /etc/systemd/system/chatbot-api.service > /dev/null << EOF
[Unit]
Description=ChatBot API Service
After=network.target

[Service]
Type=notify
User=$USER
Group=$USER
RuntimeDirectory=chatbot-api
WorkingDirectory=/var/www/chatbot-api
Environment=PATH=/var/www/chatbot-api/venv/bin
ExecStart=/var/www/chatbot-api/venv/bin/gunicorn -c gunicorn.conf.py FastAPI:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 15. 配置Nginx
sudo tee /etc/nginx/conf.d/chatbot-api.conf > /dev/null << EOF
upstream chatbot_api {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    # 重定向HTTP到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;  # 替换为您的域名
    
    # SSL证书配置（请配置您的SSL证书）
    ssl_certificate /etc/ssl/certs/your-cert.pem;
    ssl_certificate_key /etc/ssl/private/your-key.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE+AESGCM:ECDHE+AES256:ECDHE+AES128:!aNULL:!MD5:!DSS;
    ssl_prefer_server_ciphers on;
    
    # 上传文件大小限制
    client_max_body_size 10M;
    
    # API代理
    location / {
        proxy_pass http://chatbot_api;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 静态文件
    location /static/ {
        alias /var/www/chatbot-api/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://chatbot_api/health;
        access_log off;
    }
}
EOF

# 16. 启动服务
sudo systemctl daemon-reload
sudo systemctl enable chatbot-api
sudo systemctl start chatbot-api
sudo systemctl enable nginx
sudo systemctl restart nginx

# 17. 配置防火墙
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload

echo "阿里云ECS部署完成！"
echo "请确保："
echo "1. 配置正确的域名和SSL证书"
echo "2. 设置正确的环境变量"
echo "3. 配置数据库用户和权限"
echo "4. 在阿里云控制台开放80和443端口" 