from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1, max_length=2048)
    temperature: float = Field(0.7, ge=0.0, le=1.0)
    max_length: Optional[int] = Field(None, ge=1, le=4096)

class ChatResponse(BaseModel):
    message: str
    user_id: int
    timestamp: datetime = Field(default_factory=datetime.now)

class ChatHistory(BaseModel):
    user_id: int
    messages: List[ChatResponse]
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        orm_mode = True 