#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估和性能监控脚本
"""

import json
import torch
import numpy as np
from typing import List, Dict, Tuple
from pathlib import Path
import logging
from transformers import AutoTokenizer, AutoModelForCausalLM
from sklearn.metrics import bleu_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, model_path: str, tokenizer_path: str = None):
        self.model_path = model_path
        self.tokenizer_path = tokenizer_path or model_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(self.tokenizer_path)
        self.model = AutoModelForCausalLM.from_pretrained(self.model_path)
        self.model.to(self.device)
        self.model.eval()
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_test_data(self, file_path: str) -> List[Dict]:
        """加载测试数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"加载了 {len(data)} 条测试数据")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败: {e}")
            return []
    
    def generate_response(self, input_text: str, max_length: int = 512) -> str:
        """生成模型回复"""
        try:
            # 编码输入
            inputs = self.tokenizer.encode(
                input_text, 
                return_tensors="pt", 
                max_length=max_length//2,
                truncation=True
            ).to(self.device)
            
            # 生成回复
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=max_length,
                    num_return_sequences=1,
                    temperature=0.7,
                    top_p=0.9,
                    top_k=50,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码输出
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 移除输入部分，只保留生成的回复
            if input_text in response:
                response = response.replace(input_text, "").strip()
            
            return response
            
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "抱歉，生成回复时出现错误。"
    
    def calculate_bleu_score(self, predictions: List[str], references: List[str]) -> float:
        """计算BLEU分数"""
        try:
            # 将字符串转换为字符列表（中文）
            pred_chars = [[list(pred)] for pred in predictions]
            ref_chars = [[list(ref)] for ref in references]
            
            # 计算BLEU分数
            bleu = bleu_score.corpus_bleu(ref_chars, pred_chars)
            return bleu
        except Exception as e:
            logger.error(f"计算BLEU分数失败: {e}")
            return 0.0
    
    def calculate_response_quality(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
        """计算回复质量指标"""
        metrics = {
            "avg_length": np.mean([len(pred) for pred in predictions]),
            "length_variance": np.var([len(pred) for pred in predictions]),
            "repetition_rate": 0.0,
            "relevance_score": 0.0
        }
        
        # 计算重复率
        repetitive_responses = 0
        for pred in predictions:
            words = pred.split()
            if len(words) > 1:
                unique_words = set(words)
                repetition_rate = 1 - len(unique_words) / len(words)
                if repetition_rate > 0.5:  # 如果重复率超过50%
                    repetitive_responses += 1
        
        metrics["repetition_rate"] = repetitive_responses / len(predictions)
        
        # 简单的相关性评分（基于关键词匹配）
        relevance_scores = []
        for pred, ref in zip(predictions, references):
            pred_words = set(pred.split())
            ref_words = set(ref.split())
            if len(ref_words) > 0:
                overlap = len(pred_words.intersection(ref_words))
                relevance = overlap / len(ref_words)
                relevance_scores.append(relevance)
        
        metrics["relevance_score"] = np.mean(relevance_scores) if relevance_scores else 0.0
        
        return metrics
    
    def evaluate_model(self, test_data: List[Dict]) -> Dict[str, float]:
        """评估模型性能"""
        logger.info("开始评估模型...")
        
        predictions = []
        references = []
        
        for i, item in enumerate(test_data):
            if i % 10 == 0:
                logger.info(f"评估进度: {i}/{len(test_data)}")
            
            input_text = item['input']
            reference = item['response']
            
            # 生成预测
            prediction = self.generate_response(input_text)
            
            predictions.append(prediction)
            references.append(reference)
        
        # 计算各种指标
        bleu = self.calculate_bleu_score(predictions, references)
        quality_metrics = self.calculate_response_quality(predictions, references)
        
        results = {
            "bleu_score": bleu,
            **quality_metrics,
            "total_samples": len(test_data)
        }
        
        logger.info("评估完成!")
        return results, predictions, references
    
    def generate_evaluation_report(self, results: Dict, predictions: List[str], 
                                 references: List[str], output_dir: str = "evaluation_results"):
        """生成评估报告"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 保存详细结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存评估指标
        metrics_file = output_path / f"metrics_{timestamp}.json"
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 保存预测结果样例
        samples_file = output_path / f"samples_{timestamp}.json"
        samples = []
        for i in range(min(20, len(predictions))):  # 保存前20个样例
            samples.append({
                "input": "示例输入",  # 这里应该是原始输入，但为了简化省略
                "prediction": predictions[i],
                "reference": references[i]
            })
        
        with open(samples_file, 'w', encoding='utf-8') as f:
            json.dump(samples, f, ensure_ascii=False, indent=2)
        
        # 生成可视化图表
        self.create_visualizations(results, output_path, timestamp)
        
        # 生成文本报告
        report_file = output_path / f"report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("模型评估报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试样本数: {results['total_samples']}\n\n")
            f.write("性能指标:\n")
            f.write(f"BLEU分数: {results['bleu_score']:.4f}\n")
            f.write(f"平均回复长度: {results['avg_length']:.2f}\n")
            f.write(f"长度方差: {results['length_variance']:.2f}\n")
            f.write(f"重复率: {results['repetition_rate']:.4f}\n")
            f.write(f"相关性分数: {results['relevance_score']:.4f}\n\n")
            
            # 性能评级
            overall_score = (results['bleu_score'] + results['relevance_score'] - results['repetition_rate']) / 2
            if overall_score > 0.7:
                grade = "优秀"
            elif overall_score > 0.5:
                grade = "良好"
            elif overall_score > 0.3:
                grade = "一般"
            else:
                grade = "需要改进"
            
            f.write(f"综合评级: {grade} (分数: {overall_score:.4f})\n")
        
        logger.info(f"评估报告已保存到 {output_path}")
    
    def create_visualizations(self, results: Dict, output_path: Path, timestamp: str):
        """创建可视化图表"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建指标对比图
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            
            # BLEU分数
            axes[0, 0].bar(['BLEU分数'], [results['bleu_score']], color='skyblue')
            axes[0, 0].set_title('BLEU分数')
            axes[0, 0].set_ylim(0, 1)
            
            # 回复长度分布
            axes[0, 1].bar(['平均长度'], [results['avg_length']], color='lightgreen')
            axes[0, 1].set_title('平均回复长度')
            
            # 重复率
            axes[1, 0].bar(['重复率'], [results['repetition_rate']], color='salmon')
            axes[1, 0].set_title('重复率')
            axes[1, 0].set_ylim(0, 1)
            
            # 相关性分数
            axes[1, 1].bar(['相关性'], [results['relevance_score']], color='gold')
            axes[1, 1].set_title('相关性分数')
            axes[1, 1].set_ylim(0, 1)
            
            plt.tight_layout()
            plt.savefig(output_path / f"metrics_chart_{timestamp}.png", dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.warning(f"创建可视化图表失败: {e}")

def main():
    """主函数"""
    # 配置参数
    model_path = "models/ai/chatbot/checkpoints/best_model"  # 模型路径
    test_data_path = "data/eval.json"  # 测试数据路径
    
    # 检查文件是否存在
    if not Path(model_path).exists():
        logger.error(f"模型路径不存在: {model_path}")
        return
    
    if not Path(test_data_path).exists():
        logger.error(f"测试数据路径不存在: {test_data_path}")
        return
    
    # 创建评估器
    evaluator = ModelEvaluator(model_path)
    
    # 加载测试数据
    test_data = evaluator.load_test_data(test_data_path)
    if not test_data:
        logger.error("没有可用的测试数据")
        return
    
    # 评估模型
    results, predictions, references = evaluator.evaluate_model(test_data)
    
    # 生成报告
    evaluator.generate_evaluation_report(results, predictions, references)
    
    # 打印结果摘要
    logger.info("评估结果摘要:")
    logger.info(f"BLEU分数: {results['bleu_score']:.4f}")
    logger.info(f"平均回复长度: {results['avg_length']:.2f}")
    logger.info(f"重复率: {results['repetition_rate']:.4f}")
    logger.info(f"相关性分数: {results['relevance_score']:.4f}")

if __name__ == "__main__":
    main()
