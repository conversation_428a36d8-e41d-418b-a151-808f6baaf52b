from typing import Any
from pydantic import BaseModel
import pandas as pd

class BaseModelConfig(BaseModel):
    """Base model configuration for all Pydantic models in the application"""
    
    class Config:
        arbitrary_types_allowed = True  # Allow arbitrary types like pandas DataFrame
        from_attributes = True  # Allow ORM model parsing
        populate_by_name = True  # Allow population by field name as well as alias
        json_encoders = {
            pd.DataFrame: lambda df: df.to_dict(orient='records')
        } 