from typing import List, Dict, Any, Optional
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import Trainer, TrainingArguments, AutoTokenizer, AutoModelForCausalLM
from pydantic import BaseModel
import json
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm

class FortuneDataset(Dataset):
    """算命数据集类"""
    
    def __init__(self, data_path: str, tokenizer, max_length: int = 512):
        """
        初始化数据集
        
        Args:
            data_path: 数据文件路径
            tokenizer: 分词器
            max_length: 最大序列长度
        """
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.examples = self._load_data(data_path)
        
    def _load_data(self, data_path: str) -> List[Dict[str, str]]:
        """加载训练数据"""
        with open(data_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data["examples"]
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        example = self.examples[idx]
        
        # 构建输入文本
        input_text = f"问题：{example['question']}\n上下文：{example.get('context', '')}\n回答：{example['answer']}"
        
        # 编码
        encoding = self.tokenizer(
            input_text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].squeeze(),
            "attention_mask": encoding["attention_mask"].squeeze(),
            "labels": encoding["input_ids"].squeeze()
        }

class FineTuningConfig(BaseModel):
    """微调配置"""
    model_name: str
    output_dir: str
    num_train_epochs: int = 3
    per_device_train_batch_size: int = 4
    gradient_accumulation_steps: int = 4
    learning_rate: float = 2e-5
    warmup_steps: int = 100
    logging_steps: int = 10
    save_steps: int = 100
    max_length: int = 512

class ModelTuner:
    """模型微调类"""
    
    def __init__(self, config: FineTuningConfig):
        """
        初始化模型微调器
        
        Args:
            config: 微调配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化分词器和模型
        self.tokenizer = AutoTokenizer.from_pretrained(
            config.model_name,
            trust_remote_code=True
        )
        
        self.model = AutoModelForCausalLM.from_pretrained(
            config.model_name,
            trust_remote_code=True,
            torch_dtype=torch.float16
        )
        
    def prepare_dataset(self, data_path: str) -> FortuneDataset:
        """
        准备数据集
        
        Args:
            data_path: 数据文件路径
            
        Returns:
            FortuneDataset: 处理后的数据集
        """
        return FortuneDataset(
            data_path=data_path,
            tokenizer=self.tokenizer,
            max_length=self.config.max_length
        )
    
    def train(self, train_dataset: FortuneDataset, eval_dataset: Optional[FortuneDataset] = None):
        """
        训练模型
        
        Args:
            train_dataset: 训练数据集
            eval_dataset: 评估数据集（可选）
        """
        # 准备训练参数
        training_args = TrainingArguments(
            output_dir=self.config.output_dir,
            num_train_epochs=self.config.num_train_epochs,
            per_device_train_batch_size=self.config.per_device_train_batch_size,
            gradient_accumulation_steps=self.config.gradient_accumulation_steps,
            learning_rate=self.config.learning_rate,
            warmup_steps=self.config.warmup_steps,
            logging_steps=self.config.logging_steps,
            save_steps=self.config.save_steps,
            evaluation_strategy="steps" if eval_dataset else "no",
            fp16=True,
            load_best_model_at_end=True if eval_dataset else False
        )
        
        # 初始化训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=self.tokenizer
        )
        
        # 开始训练
        self.logger.info("Starting model fine-tuning...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        self.tokenizer.save_pretrained(self.config.output_dir)
        
        self.logger.info(f"Model fine-tuning completed. Model saved to {self.config.output_dir}")
    
    def evaluate(self, test_dataset: FortuneDataset) -> Dict[str, float]:
        """
        评估模型
        
        Args:
            test_dataset: 测试数据集
            
        Returns:
            Dict[str, float]: 评估指标
        """
        trainer = Trainer(
            model=self.model,
            tokenizer=self.tokenizer
        )
        
        metrics = trainer.evaluate(test_dataset)
        return metrics
    
    def save_metrics(self, metrics: Dict[str, float], output_path: str):
        """
        保存评估指标
        
        Args:
            metrics: 评估指标
            output_path: 输出路径
        """
        metrics["timestamp"] = datetime.now().isoformat()
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(metrics, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"Evaluation metrics saved to {output_path}")

# 使用示例
if __name__ == "__main__":
    # 配置
    config = FineTuningConfig(
        model_name="THUDM/chatglm-6b",
        output_dir="models/fortune_telling",
        num_train_epochs=3,
        per_device_train_batch_size=4
    )
    
    # 初始化微调器
    tuner = ModelTuner(config)
    
    # 准备数据集
    train_dataset = tuner.prepare_dataset("data/fortune_train.json")
    eval_dataset = tuner.prepare_dataset("data/fortune_eval.json")
    
    # 训练模型
    tuner.train(train_dataset, eval_dataset)
    
    # 评估模型
    test_dataset = tuner.prepare_dataset("data/fortune_test.json")
    metrics = tuner.evaluate(test_dataset)
    
    # 保存评估指标
    tuner.save_metrics(metrics, "models/fortune_telling/metrics.json") 