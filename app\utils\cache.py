"""
缓存工具模块
提供装饰器缓存、Redis缓存等功能
"""
import json
import hashlib
import functools
from typing import Any, Optional, Callable, Dict
from datetime import datetime, timedelta
import asyncio
import logging

logger = logging.getLogger(__name__)

class MemoryCache:
    """内存缓存类"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, Dict] = {}
        self.max_size = max_size
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key in self.cache:
            entry = self.cache[key]
            
            # 检查是否过期
            if entry['expires'] and datetime.now() > entry['expires']:
                del self.cache[key]
                return None
            
            # 更新访问时间
            entry['last_accessed'] = datetime.now()
            return entry['value']
        
        return None
    
    def set(self, key: str, value: Any, expire: Optional[int] = None):
        """设置缓存值"""
        # 如果缓存已满，删除最老的条目
        if len(self.cache) >= self.max_size:
            self._evict_oldest()
        
        expires = None
        if expire:
            expires = datetime.now() + timedelta(seconds=expire)
        
        self.cache[key] = {
            'value': value,
            'created': datetime.now(),
            'last_accessed': datetime.now(),
            'expires': expires
        }
    
    def delete(self, key: str):
        """删除缓存"""
        if key in self.cache:
            del self.cache[key]
    
    def clear(self):
        """清空所有缓存"""
        self.cache.clear()
    
    def _evict_oldest(self):
        """清除最老的缓存条目"""
        if not self.cache:
            return
        
        oldest_key = min(self.cache.keys(), 
                        key=lambda k: self.cache[k]['last_accessed'])
        del self.cache[oldest_key]
    
    def get_stats(self) -> Dict:
        """获取缓存统计信息"""
        now = datetime.now()
        expired_count = 0
        
        for entry in self.cache.values():
            if entry['expires'] and now > entry['expires']:
                expired_count += 1
        
        return {
            'total_keys': len(self.cache),
            'expired_keys': expired_count,
            'max_size': self.max_size
        }

# 全局内存缓存实例
memory_cache = MemoryCache()

def generate_cache_key(*args, **kwargs) -> str:
    """生成缓存键"""
    # 将参数转换为字符串
    key_parts = []
    
    for arg in args:
        if hasattr(arg, '__dict__'):
            # 对象类型，使用其字典表示
            key_parts.append(str(sorted(arg.__dict__.items())))
        else:
            key_parts.append(str(arg))
    
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}={v}")
    
    # 生成哈希值作为键
    key_string = "|".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()

def cache_result(expire: int = 3600, key_prefix: str = ""):
    """
    缓存装饰器
    
    Args:
        expire: 过期时间（秒）
        key_prefix: 键前缀
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成缓存键
            func_name = f"{func.__module__}.{func.__qualname__}"
            cache_key = f"{key_prefix}{func_name}:{generate_cache_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = memory_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                memory_cache.set(cache_key, result, expire)
                logger.debug(f"缓存设置: {cache_key}")
                return result
                
            except Exception as e:
                logger.error(f"函数执行失败: {func_name}, 错误: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成缓存键
            func_name = f"{func.__module__}.{func.__qualname__}"
            cache_key = f"{key_prefix}{func_name}:{generate_cache_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_result = memory_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            try:
                result = func(*args, **kwargs)
                memory_cache.set(cache_key, result, expire)
                logger.debug(f"缓存设置: {cache_key}")
                return result
                
            except Exception as e:
                logger.error(f"函数执行失败: {func_name}, 错误: {e}")
                raise
        
        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.memory_cache = memory_cache
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        return self.memory_cache.get(key)
    
    def set(self, key: str, value: Any, expire: Optional[int] = None):
        """设置缓存"""
        self.memory_cache.set(key, value, expire)
    
    def delete(self, key: str):
        """删除缓存"""
        self.memory_cache.delete(key)
    
    def clear(self):
        """清空缓存"""
        self.memory_cache.clear()
    
    def get_json(self, key: str) -> Optional[Any]:
        """获取JSON格式的缓存"""
        value = self.get(key)
        if value is not None:
            try:
                return json.loads(value) if isinstance(value, str) else value
            except json.JSONDecodeError:
                return value
        return None
    
    def set_json(self, key: str, value: Any, expire: Optional[int] = None):
        """设置JSON格式的缓存"""
        try:
            json_value = json.dumps(value, ensure_ascii=False) if not isinstance(value, str) else value
            self.set(key, json_value, expire)
        except TypeError:
            # 如果无法序列化为JSON，直接存储
            self.set(key, value, expire)
    
    def get_stats(self) -> Dict:
        """获取缓存统计"""
        return self.memory_cache.get_stats()
    
    def cleanup_expired(self):
        """清理过期缓存"""
        now = datetime.now()
        expired_keys = []
        
        for key, entry in self.memory_cache.cache.items():
            if entry['expires'] and now > entry['expires']:
                expired_keys.append(key)
        
        for key in expired_keys:
            self.memory_cache.delete(key)
        
        logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
        return len(expired_keys)

# 全局缓存管理器实例
cache_manager = CacheManager()

def invalidate_cache_pattern(pattern: str):
    """
    根据模式删除缓存
    
    Args:
        pattern: 匹配模式（简单的字符串匹配）
    """
    keys_to_delete = []
    
    for key in memory_cache.cache.keys():
        if pattern in key:
            keys_to_delete.append(key)
    
    for key in keys_to_delete:
        memory_cache.delete(key)
    
    logger.info(f"根据模式 '{pattern}' 删除了 {len(keys_to_delete)} 个缓存项")

def cache_user_data(user_id: str, data: Any, expire: int = 1800):
    """
    缓存用户数据
    
    Args:
        user_id: 用户ID
        data: 要缓存的数据
        expire: 过期时间（秒）
    """
    key = f"user_data:{user_id}"
    cache_manager.set_json(key, data, expire)

def get_user_data(user_id: str) -> Optional[Any]:
    """
    获取用户缓存数据
    
    Args:
        user_id: 用户ID
        
    Returns:
        缓存的用户数据
    """
    key = f"user_data:{user_id}"
    return cache_manager.get_json(key)

def invalidate_user_cache(user_id: str):
    """
    清除用户相关缓存
    
    Args:
        user_id: 用户ID
    """
    invalidate_cache_pattern(f"user_data:{user_id}")
    invalidate_cache_pattern(f"bazi_analysis:{user_id}")
    invalidate_cache_pattern(f"fortune_query:{user_id}")

# 定期清理任务（需要在应用启动时设置）
async def periodic_cleanup():
    """定期清理过期缓存"""
    while True:
        try:
            cache_manager.cleanup_expired()
            await asyncio.sleep(3600)  # 每小时清理一次
        except Exception as e:
            logger.error(f"缓存清理任务异常: {e}")
            await asyncio.sleep(300)  # 出错后5分钟重试 