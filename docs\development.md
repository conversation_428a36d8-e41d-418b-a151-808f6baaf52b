# 开发指南

## 开发环境设置

### 1. 环境要求

- Python 3.8+
- CUDA 11.6+ (GPU版本)
- Node.js 14+ (前端开发)
- Docker & Docker Compose
- Git

### 2. 开发环境配置

```bash
# 1. 克隆项目
git clone [项目地址]
cd chatglm-qa-system

# 2. 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt
pip install -r requirements_dev.txt  # 开发依赖
```

### 3. 环境变量配置

```bash
# .env.development
DEBUG=True
HOST=localhost
PORT=8000
MODEL_PATH=./model_cache
DB_URL=mysql://user:pass@localhost/db
REDIS_URL=redis://localhost:6379
```

## 代码规范

### 1. Python代码规范

```python
# 示例代码风格
from typing import Optional, List
from pydantic import BaseModel

class UserResponse(BaseModel):
    """用户响应模型"""
    
    user_id: int
    username: str
    email: Optional[str] = None
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        return self.username or f"User_{self.user_id}"

def process_user_data(
    users: List[UserResponse],
    include_email: bool = False
) -> dict:
    """
    处理用户数据
    
    Args:
        users: 用户列表
        include_email: 是否包含邮箱
        
    Returns:
        处理后的用户数据字典
    """
    return {
        user.user_id: {
            "name": user.get_display_name(),
            **({"email": user.email} if include_email else {})
        }
        for user in users
    }
```

### 2. 代码注释规范

- 文件头部注释
```python
"""
Module: user_service.py
Description: 用户服务相关功能
Author: [作者名]
Date: 2024-01-01
"""
```

- 函数注释
```python
def calculate_metrics(
    predictions: List[float],
    targets: List[float]
) -> Dict[str, float]:
    """
    计算模型预测指标
    
    Args:
        predictions: 预测值列表
        targets: 目标值列表
        
    Returns:
        包含各项指标的字典：
        - accuracy: 准确率
        - precision: 精确率
        - recall: 召回率
        
    Raises:
        ValueError: 当输入列表长度不匹配时
    """
    pass
```

### 3. Git提交规范

```bash
# 提交格式
<type>(<scope>): <subject>

# 类型
feat: 新功能
fix: 修复
docs: 文档
style: 格式
refactor: 重构
test: 测试
chore: 构建

# 示例
git commit -m "feat(user): add user authentication"
git commit -m "fix(api): resolve response timeout issue"
```

## 项目结构

```
app/
├── api/                # API接口
│   ├── routes/        # 路由定义
│   ├── models/        # 数据模型
│   └── dependencies/  # 依赖项
├── core/              # 核心功能
│   ├── config.py      # 配置
│   ├── logging.py     # 日志
│   └── metrics.py     # 监控
├── models/            # AI模型
│   ├── chatglm.py     # ChatGLM模型
│   └── pipeline.py    # 处理管道
└── utils/             # 工具函数
```

## 开发流程

### 1. 功能开发

```bash
# 1. 创建功能分支
git checkout -b feature/user-auth

# 2. 开发功能
# 3. 运行测试
pytest tests/

# 4. 提交代码
git add .
git commit -m "feat(auth): implement user authentication"

# 5. 推送分支
git push origin feature/user-auth

# 6. 创建Pull Request
```

### 2. 测试规范

```python
# tests/test_user_service.py
import pytest
from app.services.user_service import UserService

def test_user_creation():
    """测试用户创建功能"""
    service = UserService()
    user = service.create_user(
        username="test_user",
        email="<EMAIL>"
    )
    
    assert user.username == "test_user"
    assert user.email == "<EMAIL>"

@pytest.mark.asyncio
async def test_user_authentication():
    """测试用户认证功能"""
    service = UserService()
    result = await service.authenticate(
        username="test_user",
        password="password123"
    )
    
    assert result.is_authenticated
    assert result.token is not None
```

### 3. API开发

```python
# app/api/routes/chat.py
from fastapi import APIRouter, Depends
from app.core.metrics import ModelMetrics
from app.models.chatglm import ChatGLM

router = APIRouter()
model = ChatGLM()

@router.post("/chat")
@ModelMetrics.monitor_performance("chat")
async def chat_endpoint(
    query: str,
    context: Optional[str] = None
):
    """
    聊天接口
    
    Args:
        query: 用户问题
        context: 上下文信息
    """
    try:
        response = await model.generate(
            query=query,
            context=context
        )
        return response
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )
```

## 调试技巧

### 1. 日志调试

```python
from app.core.logging import setup_logger

logger = setup_logger(__name__, 'debug.log')

def process_data(data):
    logger.debug(f"Input data: {data}")
    # 处理逻辑
    logger.debug(f"Processed result: {result}")
    return result
```

### 2. 性能分析

```python
import cProfile
import pstats

def profile_function(func):
    def wrapper(*args, **kwargs):
        profiler = cProfile.Profile()
        result = profiler.runcall(func, *args, **kwargs)
        stats = pstats.Stats(profiler)
        stats.sort_stats('cumulative').print_stats(20)
        return result
    return wrapper

@profile_function
def expensive_operation():
    # 耗时操作
    pass
```

## 部署流程

### 1. 开发环境

```bash
# 启动开发服务器
./start-dev.ps1
```

### 2. 测试环境

```bash
# 构建测试镜像
docker build -t chatglm-qa:test .

# 启动测试环境
docker-compose -f docker-compose.test.yml up
```

### 3. 生产环境

```bash
# 构建生产镜像
docker build -t chatglm-qa:prod .

# 部署服务
docker-compose -f docker-compose.prod.yml up -d
```

## 常见问题

### 1. 内存问题

```python
# 使用生成器处理大数据
def process_large_data(data_path):
    with open(data_path) as f:
        for line in f:
            yield process_line(line)

# 使用内存监控
@ModelMetrics.track_memory_usage
def memory_intensive_task():
    pass
```

### 2. 性能优化

```python
# 使用缓存
from functools import lru_cache

@lru_cache(maxsize=1000)
def expensive_computation(x):
    pass

# 批处理
async def batch_process(items, batch_size=100):
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        await process_batch(batch)
```

## 参考资源

- [FastAPI文档](https://fastapi.tiangolo.com/)
- [ChatGLM-6B文档](https://github.com/THUDM/ChatGLM-6B)
- [Prometheus文档](https://prometheus.io/docs/introduction/overview/)
- [Docker文档](https://docs.docker.com/) 