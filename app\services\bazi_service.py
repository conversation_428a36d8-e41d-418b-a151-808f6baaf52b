"""
八字分析服务
提供完整的八字排盘、分析和运势预测功能
"""
import asyncio
from datetime import datetime, date
from typing import Dict, List, Optional, Any
import calendar
import random
from app.utils.chinese_calendar import ChineseCalendar
from app.utils.wuxing import WuXingAnalyzer
import logging

logger = logging.getLogger(__name__)

class BaziService:
    """八字分析服务类"""
    
    def __init__(self):
        self.tiangan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
        self.dizhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
        self.wuxing = {
            "甲": "木", "乙": "木", "丙": "火", "丁": "火", "戊": "土", 
            "己": "土", "庚": "金", "辛": "金", "壬": "水", "癸": "水",
            "子": "水", "丑": "土", "寅": "木", "卯": "木", "辰": "土", 
            "巳": "火", "午": "火", "未": "土", "申": "金", "酉": "金", 
            "戌": "土", "亥": "水"
        }
        self.shengxiao = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        
    async def analyze(self, birth_info: Dict, analysis_type: str = "basic") -> Dict:
        """
        执行八字分析
        
        Args:
            birth_info: 出生信息字典
            analysis_type: 分析类型 (basic/detailed/marriage)
            
        Returns:
            分析结果字典
        """
        try:
            logger.info(f"开始八字分析：{birth_info}, 类型：{analysis_type}")
            
            # 解析出生信息
            birth_datetime = self._parse_birth_info(birth_info)
            
            # 生成八字排盘
            bazi_chart = await self._generate_bazi_chart(birth_datetime, birth_info)
            
            # 五行分析
            five_elements = self._analyze_five_elements(bazi_chart)
            
            # 性格分析
            personality = self._analyze_personality(bazi_chart, five_elements)
            
            # 运势分析
            career = self._analyze_career(bazi_chart, five_elements)
            wealth = self._analyze_wealth(bazi_chart, five_elements)
            health = self._analyze_health(bazi_chart, five_elements)
            marriage = self._analyze_marriage(bazi_chart, five_elements, birth_info.get("gender", 1))
            
            # 幸运元素
            lucky_elements = self._get_lucky_elements(five_elements)
            
            # 生成建议
            advice = self._generate_advice(five_elements, personality, career, wealth, health, marriage)
            
            # 计算综合评分
            score = self._calculate_score(five_elements, personality, career, wealth, health, marriage)
            
            result = {
                "bazi_chart": bazi_chart,
                "five_elements": five_elements,
                "personality": personality,
                "career": career,
                "wealth": wealth,
                "health": health,
                "marriage": marriage,
                "lucky_elements": lucky_elements,
                "advice": advice,
                "score": score
            }
            
            logger.info(f"八字分析完成，评分：{score}")
            return result
            
        except Exception as e:
            logger.error(f"八字分析失败：{e}")
            raise Exception(f"八字分析失败：{str(e)}")
    
    async def get_fortune(self, birth_info: Dict, fortune_type: str, query_date: Optional[date] = None) -> Dict:
        """
        获取运势信息
        
        Args:
            birth_info: 出生信息
            fortune_type: 运势类型 (daily/weekly/monthly/yearly)
            query_date: 查询日期
            
        Returns:
            运势结果
        """
        try:
            if not query_date:
                query_date = date.today()
            
            # 生成基础八字
            birth_datetime = self._parse_birth_info(birth_info)
            bazi_chart = await self._generate_bazi_chart(birth_datetime, birth_info)
            five_elements = self._analyze_five_elements(bazi_chart)
            
            # 根据类型生成运势
            if fortune_type == "daily":
                period = f"{query_date.year}年{query_date.month}月{query_date.day}日"
                duration_days = 1
            elif fortune_type == "weekly":
                period = f"{query_date.year}年第{query_date.isocalendar()[1]}周"
                duration_days = 7
            elif fortune_type == "monthly":
                period = f"{query_date.year}年{query_date.month}月"
                duration_days = calendar.monthrange(query_date.year, query_date.month)[1]
            elif fortune_type == "yearly":
                period = f"{query_date.year}年"
                duration_days = 366 if calendar.isleap(query_date.year) else 365
            else:
                raise ValueError(f"不支持的运势类型：{fortune_type}")
            
            # 基于五行和时间计算运势
            fortune_base = self._calculate_fortune_base(five_elements, query_date, duration_days)
            
            # 生成各项运势评分（1-5星）
            career = min(5, max(1, fortune_base + random.randint(-1, 1)))
            wealth = min(5, max(1, fortune_base + random.randint(-1, 1)))
            love = min(5, max(1, fortune_base + random.randint(-1, 1)))
            health = min(5, max(1, fortune_base + random.randint(-1, 1)))
            
            # 总体评分（0-100）
            overall_score = int((career + wealth + love + health) * 20)
            
            # 生成运势总结和建议
            summary = self._generate_fortune_summary(overall_score, career, wealth, love, health)
            advice = self._generate_fortune_advice(five_elements, career, wealth, love, health)
            
            # 幸运数字和颜色
            lucky_numbers = self._get_lucky_numbers(five_elements, query_date)
            lucky_colors = self._get_lucky_colors(five_elements)
            
            return {
                "period": period,
                "overall_score": overall_score,
                "career": career,
                "wealth": wealth,
                "love": love,
                "health": health,
                "summary": summary,
                "advice": advice,
                "lucky_numbers": lucky_numbers,
                "lucky_colors": lucky_colors
            }
            
        except Exception as e:
            logger.error(f"运势查询失败：{e}")
            raise Exception(f"运势查询失败：{str(e)}")
    
    def _parse_birth_info(self, birth_info: Dict) -> datetime:
        """解析出生信息为datetime对象"""
        year = birth_info.get("year")
        month = birth_info.get("month")
        day = birth_info.get("day")
        hour = birth_info.get("hour", 0)
        minute = birth_info.get("minute", 0)
        
        if not all([year, month, day]):
            raise ValueError("出生年月日信息不完整")
        
        return datetime(year, month, day, hour, minute)
    
    async def _generate_bazi_chart(self, birth_datetime: datetime, birth_info: Dict) -> Dict:
        """生成八字排盘"""
        # 简化的八字计算（实际应该使用准确的历法计算）
        year_offset = (birth_datetime.year - 1984) % 60
        month_offset = (birth_datetime.month - 1) % 12
        day_offset = birth_datetime.timetuple().tm_yday % 60
        hour_offset = birth_datetime.hour // 2
        
        # 年柱
        year_tg = self.tiangan[year_offset % 10]
        year_dz = self.dizhi[year_offset % 12]
        
        # 月柱
        month_tg = self.tiangan[(year_offset * 2 + month_offset) % 10]
        month_dz = self.dizhi[month_offset]
        
        # 日柱
        day_tg = self.tiangan[day_offset % 10]
        day_dz = self.dizhi[day_offset % 12]
        
        # 时柱
        hour_tg = self.tiangan[(day_offset * 2 + hour_offset) % 10]
        hour_dz = self.dizhi[hour_offset % 12]
        
        # 生肖
        shengxiao = self.shengxiao[year_offset % 12]
        
        return {
            "年柱": {"天干": year_tg, "地支": year_dz},
            "月柱": {"天干": month_tg, "地支": month_dz},
            "日柱": {"天干": day_tg, "地支": day_dz},
            "时柱": {"天干": hour_tg, "地支": hour_dz},
            "生肖": shengxiao,
            "八字": f"{year_tg}{year_dz} {month_tg}{month_dz} {day_tg}{day_dz} {hour_tg}{hour_dz}"
        }
    
    def _analyze_five_elements(self, bazi_chart: Dict) -> Dict:
        """分析五行分布"""
        elements_count = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}
        
        # 统计天干地支的五行
        for zhu in ["年柱", "月柱", "日柱", "时柱"]:
            tg = bazi_chart[zhu]["天干"]
            dz = bazi_chart[zhu]["地支"]
            elements_count[self.wuxing[tg]] += 1
            elements_count[self.wuxing[dz]] += 1
        
        total = sum(elements_count.values())
        elements_percent = {k: round(v/total*100, 1) for k, v in elements_count.values()}
        
        # 找出最强和最弱的元素
        strongest = max(elements_count, key=elements_count.get)
        weakest = min(elements_count, key=elements_count.get)
        
        # 分析五行平衡度
        max_count = max(elements_count.values())
        min_count = min(elements_count.values())
        balance_score = round(100 - (max_count - min_count) * 10, 1)
        
        return {
            "distribution": elements_count,
            "percentage": elements_percent,
            "strongest": strongest,
            "weakest": weakest,
            "balance_score": balance_score,
            "analysis": self._get_wuxing_analysis(elements_count, strongest, weakest)
        }
    
    def _analyze_personality(self, bazi_chart: Dict, five_elements: Dict) -> Dict:
        """分析性格特征"""
        day_master = bazi_chart["日柱"]["天干"]
        day_master_element = self.wuxing[day_master]
        strongest_element = five_elements["strongest"]
        
        # 基于日主和最强五行分析性格
        personality_traits = self._get_personality_traits(day_master_element, strongest_element)
        
        # 计算性格评分
        scores = self._calculate_personality_scores(five_elements)
        
        return {
            "day_master": day_master,
            "day_master_element": day_master_element,
            "main_traits": personality_traits,
            "scores": scores,
            "summary": self._generate_personality_summary(personality_traits, scores)
        }
    
    def _analyze_career(self, bazi_chart: Dict, five_elements: Dict) -> Dict:
        """分析事业运势"""
        day_master = bazi_chart["日柱"]["天干"]
        day_element = self.wuxing[day_master]
        
        # 基于五行分析适合的行业
        suitable_industries = self._get_suitable_industries(day_element, five_elements)
        
        # 事业发展阶段分析
        career_phases = self._analyze_career_phases(five_elements)
        
        # 事业运势评分
        career_score = self._calculate_career_score(five_elements)
        
        return {
            "score": career_score,
            "suitable_industries": suitable_industries,
            "career_phases": career_phases,
            "advice": self._get_career_advice(day_element, five_elements)
        }
    
    def _analyze_wealth(self, bazi_chart: Dict, five_elements: Dict) -> Dict:
        """分析财运"""
        day_master = bazi_chart["日柱"]["天干"]
        day_element = self.wuxing[day_master]
        
        # 财运评分
        wealth_score = self._calculate_wealth_score(five_elements)
        
        # 财运类型分析
        wealth_type = self._analyze_wealth_type(day_element, five_elements)
        
        # 理财建议
        financial_advice = self._get_financial_advice(day_element, five_elements)
        
        return {
            "score": wealth_score,
            "wealth_type": wealth_type,
            "financial_advice": financial_advice,
            "investment_suggestions": self._get_investment_suggestions(day_element)
        }
    
    def _analyze_health(self, bazi_chart: Dict, five_elements: Dict) -> Dict:
        """分析健康状况"""
        # 基于五行平衡分析健康
        balance_score = five_elements["balance_score"]
        weakest_element = five_elements["weakest"]
        
        # 健康评分
        health_score = self._calculate_health_score(five_elements)
        
        # 需要注意的健康问题
        health_concerns = self._get_health_concerns(weakest_element, five_elements)
        
        # 养生建议
        health_advice = self._get_health_advice(weakest_element, five_elements)
        
        return {
            "score": health_score,
            "balance_score": balance_score,
            "health_concerns": health_concerns,
            "health_advice": health_advice
        }
    
    def _analyze_marriage(self, bazi_chart: Dict, five_elements: Dict, gender: int) -> Dict:
        """分析婚姻感情"""
        day_master = bazi_chart["日柱"]["天干"]
        day_element = self.wuxing[day_master]
        
        # 感情运势评分
        marriage_score = self._calculate_marriage_score(five_elements, gender)
        
        # 感情特征分析
        emotional_traits = self._analyze_emotional_traits(day_element, five_elements, gender)
        
        # 理想伴侣类型
        ideal_partner = self._get_ideal_partner_type(day_element, gender)
        
        # 感情建议
        relationship_advice = self._get_relationship_advice(day_element, five_elements, gender)
        
        return {
            "score": marriage_score,
            "emotional_traits": emotional_traits,
            "ideal_partner": ideal_partner,
            "relationship_advice": relationship_advice
        }
    
    def _get_lucky_elements(self, five_elements: Dict) -> List[str]:
        """获取幸运元素"""
        weakest = five_elements["weakest"]
        
        # 根据相生关系确定幸运元素
        shengxu = {
            "木": ["水", "木"],
            "火": ["木", "火"], 
            "土": ["火", "土"],
            "金": ["土", "金"],
            "水": ["金", "水"]
        }
        
        return shengxu.get(weakest, ["木", "水"])
    
    def _generate_advice(self, five_elements: Dict, personality: Dict, career: Dict, 
                        wealth: Dict, health: Dict, marriage: Dict) -> str:
        """生成综合建议"""
        advice_parts = []
        
        # 五行调节建议
        if five_elements["balance_score"] < 70:
            advice_parts.append(f"您的五行偏{five_elements['strongest']}弱{five_elements['weakest']}，建议多接触{five_elements['weakest']}属性的事物来调和五行。")
        
        # 事业建议
        advice_parts.append(f"事业方面，建议关注{career['suitable_industries'][0]}等领域。")
        
        # 健康建议
        if health["score"] < 80:
            advice_parts.append("注意身体健康，建议规律作息，适度运动。")
        
        # 感情建议
        if marriage["score"] < 75:
            advice_parts.append("感情方面要多包容理解，培养良好的沟通习惯。")
        
        return " ".join(advice_parts)
    
    def _calculate_score(self, five_elements: Dict, personality: Dict, career: Dict, 
                        wealth: Dict, health: Dict, marriage: Dict) -> int:
        """计算综合评分"""
        scores = [
            five_elements["balance_score"],
            career["score"],
            wealth["score"], 
            health["score"],
            marriage["score"]
        ]
        return int(sum(scores) / len(scores))
    
    # 辅助方法的实现
    def _get_wuxing_analysis(self, elements_count: Dict, strongest: str, weakest: str) -> str:
        """获取五行分析文字"""
        if strongest == weakest:
            return "五行相对平衡，整体运势较为稳定。"
        else:
            return f"五行偏{strongest}，{weakest}相对较弱，建议通过调节来达到平衡。"
    
    def _get_personality_traits(self, day_element: str, strongest_element: str) -> List[str]:
        """获取性格特征"""
        traits_map = {
            "木": ["善良", "有责任心", "成长型", "创新"],
            "火": ["热情", "积极", "外向", "领导力"],
            "土": ["稳重", "可靠", "务实", "包容"],
            "金": ["果断", "理性", "有原则", "执行力"],
            "水": ["智慧", "灵活", "适应性强", "洞察力"]
        }
        return traits_map.get(day_element, ["平和", "均衡"])
    
    def _calculate_personality_scores(self, five_elements: Dict) -> Dict:
        """计算性格评分"""
        balance = five_elements["balance_score"]
        return {
            "外向性": min(100, max(0, balance + random.randint(-20, 20))),
            "责任心": min(100, max(0, balance + random.randint(-15, 15))),
            "开放性": min(100, max(0, balance + random.randint(-10, 25))),
            "情绪稳定性": min(100, max(0, balance + random.randint(-25, 10))),
            "宜人性": min(100, max(0, balance + random.randint(-10, 20)))
        }
    
    def _generate_personality_summary(self, traits: List[str], scores: Dict) -> str:
        """生成性格总结"""
        main_trait = max(scores, key=scores.get)
        return f"您的性格特点主要体现在{main_trait}方面，具有{', '.join(traits[:3])}等特质。"
    
    def _get_suitable_industries(self, day_element: str, five_elements: Dict) -> List[str]:
        """获取适合的行业"""
        industry_map = {
            "木": ["教育", "出版", "园艺", "环保", "医疗"],
            "火": ["传媒", "广告", "娱乐", "能源", "餐饮"],
            "土": ["房地产", "建筑", "农业", "矿业", "陶瓷"],
            "金": ["金融", "机械", "汽车", "珠宝", "军警"],
            "水": ["贸易", "运输", "通讯", "水利", "渔业"]
        }
        return industry_map.get(day_element, ["综合性行业"])
    
    def _analyze_career_phases(self, five_elements: Dict) -> Dict:
        """分析事业发展阶段"""
        return {
            "早期(20-30岁)": "打基础阶段，适合学习积累",
            "中期(30-45岁)": "发展阶段，适合拓展事业",
            "后期(45岁+)": "成熟阶段，适合稳固发展"
        }
    
    def _calculate_career_score(self, five_elements: Dict) -> int:
        """计算事业评分"""
        balance = five_elements["balance_score"]
        return min(100, max(60, int(balance + random.randint(-10, 20))))
    
    def _get_career_advice(self, day_element: str, five_elements: Dict) -> str:
        """获取事业建议"""
        return f"基于您的{day_element}特质，建议在事业发展中发挥自身优势，同时注意团队合作。"
    
    def _calculate_wealth_score(self, five_elements: Dict) -> int:
        """计算财运评分"""
        balance = five_elements["balance_score"]
        return min(100, max(50, int(balance + random.randint(-15, 25))))
    
    def _analyze_wealth_type(self, day_element: str, five_elements: Dict) -> str:
        """分析财运类型"""
        wealth_types = {
            "木": "稳健型财运，适合长期投资",
            "火": "快速型财运，把握机会很重要",
            "土": "稳定型财运，适合储蓄理财",
            "金": "投资型财运，适合多元化投资",
            "水": "灵活型财运，适合流动性投资"
        }
        return wealth_types.get(day_element, "综合型财运")
    
    def _get_financial_advice(self, day_element: str, five_elements: Dict) -> str:
        """获取理财建议"""
        return f"建议根据自身{day_element}特质制定理财计划，注意风险控制。"
    
    def _get_investment_suggestions(self, day_element: str) -> List[str]:
        """获取投资建议"""
        suggestions_map = {
            "木": ["绿色能源", "环保概念", "教育产业"],
            "火": ["科技股", "新兴产业", "消费概念"],
            "土": ["房地产", "基建概念", "农业股"],
            "金": ["金融股", "贵金属", "制造业"],
            "水": ["航运股", "水务概念", "流通业"]
        }
        return suggestions_map.get(day_element, ["多元化投资"])
    
    def _calculate_health_score(self, five_elements: Dict) -> int:
        """计算健康评分"""
        balance = five_elements["balance_score"]
        return min(100, max(70, int(balance + random.randint(-10, 15))))
    
    def _get_health_concerns(self, weakest_element: str, five_elements: Dict) -> List[str]:
        """获取健康注意事项"""
        concerns_map = {
            "木": ["肝胆系统", "眼部保养", "筋骨健康"],
            "火": ["心血管系统", "血压调节", "情绪管理"],
            "土": ["脾胃系统", "消化功能", "免疫力"],
            "金": ["呼吸系统", "皮肤保养", "肺部健康"],
            "水": ["肾脏系统", "生殖系统", "水盐平衡"]
        }
        return concerns_map.get(weakest_element, ["整体调节"])
    
    def _get_health_advice(self, weakest_element: str, five_elements: Dict) -> str:
        """获取养生建议"""
        advice_map = {
            "木": "多食绿色蔬菜，适当运动，保持心情舒畅",
            "火": "注意心脏保养，避免过度兴奋，适度晒太阳",
            "土": "规律饮食，健脾养胃，适当补充维生素",
            "金": "深呼吸练习，多食白色食物，保持环境清洁",
            "水": "多喝水，适当补肾，注意保暖"
        }
        return advice_map.get(weakest_element, "保持良好生活习惯")
    
    def _calculate_marriage_score(self, five_elements: Dict, gender: int) -> int:
        """计算婚姻评分"""
        balance = five_elements["balance_score"]
        base_score = min(100, max(60, int(balance + random.randint(-15, 20))))
        return base_score
    
    def _analyze_emotional_traits(self, day_element: str, five_elements: Dict, gender: int) -> List[str]:
        """分析感情特征"""
        traits_map = {
            "木": ["温和体贴", "重视感情", "专一持久"],
            "火": ["热情如火", "表达直接", "浪漫主义"],
            "土": ["稳重可靠", "家庭观念强", "包容性强"],
            "金": ["理性冷静", "有原则性", "重视承诺"],
            "水": ["温柔如水", "善解人意", "感情细腻"]
        }
        return traits_map.get(day_element, ["平和稳定"])
    
    def _get_ideal_partner_type(self, day_element: str, gender: int) -> str:
        """获取理想伴侣类型"""
        # 根据五行相生相克关系
        compatible_elements = {
            "木": "火或水属性的人",
            "火": "土或木属性的人", 
            "土": "金或火属性的人",
            "金": "水或土属性的人",
            "水": "木或金属性的人"
        }
        return compatible_elements.get(day_element, "性格互补的人")
    
    def _get_relationship_advice(self, day_element: str, five_elements: Dict, gender: int) -> str:
        """获取感情建议"""
        return f"感情中要发挥{day_element}的优势，同时学会包容和理解，建立良好的沟通模式。"
    
    def _calculate_fortune_base(self, five_elements: Dict, query_date: date, duration_days: int) -> int:
        """计算运势基础分数"""
        balance = five_elements["balance_score"]
        # 基于五行平衡和日期计算基础运势
        date_factor = (query_date.timetuple().tm_yday % 10) / 10
        base_score = int((balance / 100) * 3 + date_factor * 2)
        return min(5, max(1, base_score))
    
    def _generate_fortune_summary(self, overall_score: int, career: int, wealth: int, love: int, health: int) -> str:
        """生成运势总结"""
        if overall_score >= 85:
            return "整体运势非常好，各方面都有不错的表现，是行动的好时机。"
        elif overall_score >= 70:
            return "整体运势较好，保持积极心态，会有意外收获。"
        elif overall_score >= 55:
            return "整体运势平稳，注意调节心态，稳中求进。"
        else:
            return "整体运势一般，建议低调行事，注意休息调养。"
    
    def _generate_fortune_advice(self, five_elements: Dict, career: int, wealth: int, love: int, health: int) -> str:
        """生成运势建议"""
        advice_parts = []
        
        if career >= 4:
            advice_parts.append("事业运佳，可以主动出击")
        elif career <= 2:
            advice_parts.append("事业需谨慎，避免重大决策")
            
        if wealth >= 4:
            advice_parts.append("财运不错，适当投资")
        elif wealth <= 2:
            advice_parts.append("财运一般，控制支出")
            
        if love >= 4:
            advice_parts.append("感情顺利，珍惜良缘")
        elif love <= 2:
            advice_parts.append("感情需要更多沟通")
            
        if health <= 2:
            advice_parts.append("注意身体健康，适度休息")
        
        return "；".join(advice_parts) if advice_parts else "保持平常心，顺其自然。"
    
    def _get_lucky_numbers(self, five_elements: Dict, query_date: date) -> List[int]:
        """获取幸运数字"""
        # 基于五行和日期生成幸运数字
        base_numbers = {
            "木": [3, 8, 13, 18],
            "火": [2, 7, 12, 17],
            "土": [5, 10, 15, 20],
            "金": [4, 9, 14, 19],
            "水": [1, 6, 11, 16]
        }
        
        strongest = five_elements["strongest"]
        lucky_base = base_numbers.get(strongest, [1, 6, 11, 16])
        
        # 添加日期因子
        date_factor = query_date.day % 10
        result = []
        for num in lucky_base[:3]:
            result.append((num + date_factor) % 50 + 1)
        
        return sorted(list(set(result)))
    
    def _get_lucky_colors(self, five_elements: Dict) -> List[str]:
        """获取幸运颜色"""
        color_map = {
            "木": ["绿色", "青色", "蓝色"],
            "火": ["红色", "橙色", "紫色"],
            "土": ["黄色", "棕色", "米色"],
            "金": ["白色", "银色", "金色"],
            "水": ["黑色", "深蓝", "灰色"]
        }
        
        strongest = five_elements["strongest"]
        weakest = five_elements["weakest"]
        
        # 结合最强和最弱元素的颜色
        colors = color_map.get(strongest, ["白色"])
        if weakest != strongest:
            colors.extend(color_map.get(weakest, []))
        
        return list(set(colors))[:3] 