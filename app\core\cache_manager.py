from typing import Any, Optional, Callable
import json
import asyncio
from redis import Redis
from datetime import datetime, timedelta
from functools import wraps

class CacheManager:
    def __init__(self, redis: Redis, default_ttl: int = 3600):
        self.redis = redis
        self.default_ttl = default_ttl
        self._preload_keys = set()

    async def get_or_set(
        self,
        key: str,
        fetch_func: Callable,
        ttl: Optional[int] = None,
        version_key: Optional[str] = None
    ) -> Any:
        """Get from cache or set if missing with version checking"""
        cached = await self.redis.get(key)
        current_version = await self.redis.get(version_key) if version_key else None

        if cached:
            data = json.loads(cached)
            if not version_key or data.get('version') == current_version:
                return data['value']

        value = await fetch_func()
        await self.set(key, value, ttl, version_key)
        return value

    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        version_key: Optional[str] = None
    ) -> None:
        """Set cache with version tracking"""
        data = {
            'value': value,
            'version': await self.redis.get(version_key) if version_key else None,
            'updated_at': datetime.utcnow().isoformat()
        }
        await self.redis.setex(key, ttl or self.default_ttl, json.dumps(data))

    async def invalidate(self, key: str, pattern: bool = False) -> None:
        """Invalidate cache keys"""
        if pattern:
            keys = await self.redis.keys(key)
            if keys:
                await self.redis.delete(*keys)
        else:
            await self.redis.delete(key)

    async def preload(self, key: str, fetch_func: Callable) -> None:
        """Preload cache data"""
        self._preload_keys.add(key)
        value = await fetch_func()
        await self.set(key, value)

    async def start_preload(self) -> None:
        """Start cache preloading"""
        tasks = []
        for key in self._preload_keys:
            if not await self.redis.exists(key):
                tasks.append(self.preload(key))
        if tasks:
            await asyncio.gather(*tasks)

    def cache_with_version(self, version_key: str, ttl: Optional[int] = None):
        """Decorator for version-aware caching"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
                return await self.get_or_set(
                    cache_key,
                    lambda: func(*args, **kwargs),
                    ttl,
                    version_key
                )
            return wrapper
        return decorator

    async def maintain_consistency(self, db_value: Any, cache_key: str) -> bool:
        """Check and maintain cache-db consistency"""
        cached = await self.redis.get(cache_key)
        if not cached:
            return False

        cached_value = json.loads(cached)['value']
        return self._compare_values(cached_value, db_value)

    def _compare_values(self, cached_value: Any, db_value: Any) -> bool:
        """Compare cached and database values"""
        if isinstance(cached_value, dict) and isinstance(db_value, dict):
            return all(
                self._compare_values(cached_value.get(k), db_value.get(k))
                for k in set(cached_value) | set(db_value)
            )
        return cached_value == db_value 