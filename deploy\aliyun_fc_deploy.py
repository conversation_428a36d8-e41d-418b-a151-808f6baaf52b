#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云函数计算部署脚本
用于将API服务部署到阿里云函数计算
"""

import os
import json
import zipfile
import requests
from pathlib import Path

# 阿里云函数计算配置
FC_CONFIG = {
    "region": "cn-hangzhou",  # 阿里云地域
    "service_name": "chatbot-api-service",
    "function_name": "wxwork-api",
    "runtime": "python3.9",
    "handler": "index.handler",
    "timeout": 30,
    "memory_size": 512,
    "environment_variables": {
        "PYTHONPATH": "/code",
        "WXWORK_CORP_ID": "your_corp_id",
        "WXWORK_CORP_SECRET": "your_corp_secret",
        "WXWORK_AGENT_ID": "your_agent_id",
        "SECRET_KEY": "your_secret_key"
    }
}

def create_fc_handler():
    """
    创建函数计算入口文件
    """
    handler_code = '''
import json
import sys
import os

# 添加项目路径到Python路径
sys.path.insert(0, '/code')

from fastapi import FastAPI
from mangum import Mangum

# 导入主应用
try:
    from FastAPI import app
except ImportError:
    # 如果无法导入，创建基础应用
    app = FastAPI(title="ChatBot API")
    
    @app.get("/health")
    async def health():
        return {"status": "ok"}

# 创建Mangum适配器，用于在函数计算中运行FastAPI
handler = Mangum(app, lifespan="off")

def main_handler(event, context):
    """
    阿里云函数计算入口函数
    """
    try:
        # 处理HTTP事件
        return handler(event, context)
    except Exception as e:
        return {
            "statusCode": 500,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps({"error": str(e)})
        }

# 设置别名以兼容不同调用方式
handler = main_handler
'''
    
    with open('index.py', 'w', encoding='utf-8') as f:
        f.write(handler_code)
    
    print("✅ 创建函数计算入口文件成功")

def create_requirements():
    """
    创建函数计算专用requirements.txt
    """
    fc_requirements = '''
fastapi==0.104.1
mangum==0.17.0
pydantic==2.5.0
uvicorn==0.24.0
redis==5.0.1
requests==2.31.0
python-multipart==0.0.6
'''
    
    with open('requirements_fc.txt', 'w', encoding='utf-8') as f:
        f.write(fc_requirements.strip())
    
    print("✅ 创建函数计算依赖文件成功")

def create_template_yaml():
    """
    创建Serverless Devs模板文件
    """
    template = {
        "edition": "1.0.0",
        "name": "chatbot-api",
        "access": "default",
        "services": {
            "chatbot-api": {
                "component": "fc",
                "props": {
                    "region": FC_CONFIG["region"],
                    "service": {
                        "name": FC_CONFIG["service_name"],
                        "description": "卦里乾坤企业微信小程序API服务",
                        "internetAccess": True,
                        "logConfig": "auto",
                        "nasConfig": "auto",
                        "vpcConfig": "auto"
                    },
                    "function": {
                        "name": FC_CONFIG["function_name"],
                        "description": "企业微信聊天API函数",
                        "codeUri": "./",
                        "handler": FC_CONFIG["handler"],
                        "memorySize": FC_CONFIG["memory_size"],
                        "runtime": FC_CONFIG["runtime"],
                        "timeout": FC_CONFIG["timeout"],
                        "environmentVariables": FC_CONFIG["environment_variables"],
                        "instanceConcurrency": 100,
                        "instanceType": "e1"
                    },
                    "triggers": [
                        {
                            "name": "httpTrigger",
                            "type": "http",
                            "config": {
                                "authType": "anonymous",
                                "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                                "disableURLInternet": False
                            }
                        }
                    ],
                    "customDomains": [
                        {
                            "domainName": "auto",
                            "protocol": "HTTP,HTTPS",
                            "routeConfigs": [
                                {
                                    "path": "/*",
                                    "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
                                }
                            ]
                        }
                    ]
                }
            }
        }
    }
    
    with open('s.yaml', 'w', encoding='utf-8') as f:
        f.write(f"# Serverless Devs 配置文件\n")
        f.write(f"# 用于部署到阿里云函数计算\n\n")
        import yaml
        yaml.dump(template, f, default_flow_style=False, allow_unicode=True)
    
    print("✅ 创建Serverless Devs配置文件成功")

def create_dockerfile():
    """
    创建Docker配置（可选）
    """
    dockerfile_content = '''
FROM registry.cn-hangzhou.aliyuncs.com/aliyun_fc/runtime-python3.9:build-1.9.21

# 设置工作目录
WORKDIR /code

# 复制依赖文件
COPY requirements_fc.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_fc.txt -t .

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/code

# 暴露端口
EXPOSE 9000

# 启动命令
CMD ["python", "index.py"]
'''
    
    with open('Dockerfile.fc', 'w', encoding='utf-8') as f:
        f.write(dockerfile_content)
    
    print("✅ 创建Docker配置文件成功")

def create_deployment_script():
    """
    创建一键部署脚本
    """
    deploy_script = '''#!/bin/bash

# 阿里云函数计算一键部署脚本

echo "🚀 开始部署到阿里云函数计算..."

# 1. 检查Serverless Devs是否安装
if ! command -v s &> /dev/null; then
    echo "❌ Serverless Devs未安装，正在安装..."
    npm install -g @serverless-devs/s
fi

# 2. 配置阿里云访问凭证（如果未配置）
echo "📋 配置阿里云访问凭证..."
echo "请确保已配置阿里云AccessKey，如未配置请运行："
echo "s config add --AccessKeyID your_access_key_id --AccessKeySecret your_access_key_secret"

# 3. 部署服务
echo "📦 开始部署服务..."
s deploy --use-local

# 4. 获取部署结果
echo "✅ 部署完成！"
echo "🔗 获取访问地址："
s info

echo ""
echo "📌 部署后续步骤："
echo "1. 记录函数计算的HTTP触发器地址"
echo "2. 在小程序中更新API配置"
echo "3. 配置自定义域名（可选）"
echo "4. 配置SSL证书（生产环境）"
'''
    
    with open('deploy_fc.sh', 'w', encoding='utf-8') as f:
        f.write(deploy_script)
    
    os.chmod('deploy_fc.sh', 0o755)
    print("✅ 创建一键部署脚本成功")

def main():
    """
    主函数，创建所有必要的部署文件
    """
    print("🔧 准备阿里云函数计算部署文件...")
    
    # 创建部署目录
    os.makedirs('deploy/fc', exist_ok=True)
    os.chdir('deploy/fc')
    
    # 创建各种配置文件
    create_fc_handler()
    create_requirements()
    create_template_yaml()
    create_dockerfile()
    create_deployment_script()
    
    print("\n🎉 阿里云函数计算部署文件创建完成！")
    print("\n📋 下一步操作：")
    print("1. cd deploy/fc")
    print("2. 复制项目文件到当前目录")
    print("3. 配置阿里云AccessKey：s config add")
    print("4. 执行部署：./deploy_fc.sh")
    print("\n📚 详细文档：https://www.serverless-devs.com/fc/readme")

if __name__ == "__main__":
    main() 