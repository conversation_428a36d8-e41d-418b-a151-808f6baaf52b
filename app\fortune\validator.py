from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import json
from pathlib import Path
import logging
from datetime import datetime
import re

class ValidationRule(BaseModel):
    """验证规则模型"""
    id: str
    type: str
    criteria: Dict[str, Any]
    message: str
    severity: str
    enabled: bool = True

class ValidationResult(BaseModel):
    """验证结果模型"""
    valid: bool
    issues: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class FortuneValidator:
    """算命结果验证器"""
    
    def __init__(self, rules_dir: str = "config/validation"):
        """
        初始化验证器
        
        Args:
            rules_dir: 验证规则目录
        """
        self.rules_dir = Path(rules_dir)
        self.rules: Dict[str, ValidationRule] = {}
        self.logger = logging.getLogger(__name__)
        self._load_rules()
    
    def _load_rules(self):
        """加载验证规则"""
        if not self.rules_dir.exists():
            self.logger.warning(f"Validation rules directory {self.rules_dir} does not exist")
            return
            
        for file in self.rules_dir.glob("*.json"):
            try:
                with open(file, "r", encoding="utf-8") as f:
                    rules_data = json.load(f)
                    for rule_data in rules_data.get("rules", []):
                        rule = ValidationRule(**rule_data)
                        self.rules[rule.id] = rule
            except Exception as e:
                self.logger.error(f"Failed to load validation rules from {file}: {e}")
    
    def validate_format(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        验证结果格式
        
        Args:
            result: 算命结果
            
        Returns:
            List[Dict[str, Any]]: 格式问题列表
        """
        issues = []
        
        # 检查必需字段
        required_fields = ["birth_info", "analysis", "predictions"]
        for field in required_fields:
            if field not in result:
                issues.append({
                    "type": "missing_field",
                    "field": field,
                    "severity": "error",
                    "message": f"缺少必需字段 {field}"
                })
        
        # 检查字段类型
        if "birth_info" in result and not isinstance(result["birth_info"], dict):
            issues.append({
                "type": "invalid_type",
                "field": "birth_info",
                "severity": "error",
                "message": "birth_info 必须是字典类型"
            })
            
        if "analysis" in result and not isinstance(result["analysis"], (str, list)):
            issues.append({
                "type": "invalid_type",
                "field": "analysis",
                "severity": "error",
                "message": "analysis 必须是字符串或列表类型"
            })
            
        if "predictions" in result and not isinstance(result["predictions"], list):
            issues.append({
                "type": "invalid_type",
                "field": "predictions",
                "severity": "error",
                "message": "predictions 必须是列表类型"
            })
        
        return issues
    
    def validate_logic(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        验证结果逻辑
        
        Args:
            result: 算命结果
            
        Returns:
            List[Dict[str, Any]]: 逻辑问题列表
        """
        issues = []
        
        # 检查生日信息逻辑
        if "birth_info" in result:
            birth_info = result["birth_info"]
            
            # 检查年份范围
            if "year" in birth_info:
                year = birth_info["year"]
                if not (1900 <= year <= datetime.now().year):
                    issues.append({
                        "type": "invalid_year",
                        "severity": "error",
                        "message": f"年份 {year} 超出有效范围"
                    })
            
            # 检查月份范围
            if "month" in birth_info:
                month = birth_info["month"]
                if not (1 <= month <= 12):
                    issues.append({
                        "type": "invalid_month",
                        "severity": "error",
                        "message": f"月份 {month} 超出有效范围"
                    })
            
            # 检查日期范围
            if all(key in birth_info for key in ["year", "month", "day"]):
                try:
                    datetime(birth_info["year"], birth_info["month"], birth_info["day"])
                except ValueError:
                    issues.append({
                        "type": "invalid_date",
                        "severity": "error",
                        "message": "无效的日期组合"
                    })
        
        # 检查分析结果逻辑
        if "analysis" in result:
            analysis = result["analysis"]
            if isinstance(analysis, list) and len(analysis) > 0:
                # 检查分析结果是否有重复
                if len(analysis) != len(set(analysis)):
                    issues.append({
                        "type": "duplicate_analysis",
                        "severity": "warning",
                        "message": "分析结果中存在重复内容"
                    })
        
        # 检查预测结果逻辑
        if "predictions" in result:
            predictions = result["predictions"]
            if len(predictions) > 0:
                # 检查时间范围
                for pred in predictions:
                    if "time_range" in pred:
                        if not self._validate_time_range(pred["time_range"]):
                            issues.append({
                                "type": "invalid_time_range",
                                "severity": "error",
                                "message": f"无效的时间范围: {pred['time_range']}"
                            })
        
        return issues
    
    def validate_professional(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        验证专业性
        
        Args:
            result: 算命结果
            
        Returns:
            List[Dict[str, Any]]: 专业性问题列表
        """
        issues = []
        
        # 加载专业术语表
        terms_file = self.rules_dir / "professional_terms.json"
        if terms_file.exists():
            with open(terms_file, "r", encoding="utf-8") as f:
                terms_data = json.load(f)
                required_terms = terms_data.get("required_terms", [])
                forbidden_terms = terms_data.get("forbidden_terms", [])
                
                # 检查必需术语
                content = str(result)
                for term in required_terms:
                    if term["term"] not in content:
                        issues.append({
                            "type": "missing_term",
                            "term": term["term"],
                            "severity": term["severity"],
                            "message": f"缺少必需术语: {term['term']}"
                        })
                
                # 检查禁用术语
                for term in forbidden_terms:
                    if term["term"] in content:
                        issues.append({
                            "type": "forbidden_term",
                            "term": term["term"],
                            "severity": term["severity"],
                            "message": f"使用了禁用术语: {term['term']}"
                        })
        
        # 检查格式规范
        if "analysis" in result:
            analysis = result["analysis"]
            if isinstance(analysis, str):
                # 检查段落结构
                if not re.search(r"\n\n", analysis):
                    issues.append({
                        "type": "format_issue",
                        "severity": "warning",
                        "message": "分析内容缺少适当的段落分隔"
                    })
                
                # 检查标点符号使用
                if re.search(r"[，。；：！？]+[，。；：！？]+", analysis):
                    issues.append({
                        "type": "punctuation_issue",
                        "severity": "warning",
                        "message": "存在不规范的标点符号使用"
                    })
        
        return issues
    
    def _validate_time_range(self, time_range: str) -> bool:
        """
        验证时间范围格式
        
        Args:
            time_range: 时间范围字符串
            
        Returns:
            bool: 是否有效
        """
        # 支持的格式：
        # - 2024年
        # - 2024年1月
        # - 2024年1月-2024年12月
        # - 2024-2025
        patterns = [
            r"^\d{4}年$",
            r"^\d{4}年\d{1,2}月$",
            r"^\d{4}年\d{1,2}月-\d{4}年\d{1,2}月$",
            r"^\d{4}-\d{4}$"
        ]
        
        return any(re.match(pattern, time_range) for pattern in patterns)
    
    def validate(self, result: Dict[str, Any]) -> ValidationResult:
        """
        验证算命结果
        
        Args:
            result: 算命结果
            
        Returns:
            ValidationResult: 验证结果
        """
        all_issues = []
        
        # 收集所有验证问题
        all_issues.extend(self.validate_format(result))
        all_issues.extend(self.validate_logic(result))
        all_issues.extend(self.validate_professional(result))
        
        # 应用自定义规则
        for rule in self.rules.values():
            if not rule.enabled:
                continue
                
            try:
                if rule.type == "content":
                    if not self._validate_content(result, rule.criteria):
                        all_issues.append({
                            "type": "custom_rule",
                            "rule_id": rule.id,
                            "severity": rule.severity,
                            "message": rule.message
                        })
                elif rule.type == "structure":
                    if not self._validate_structure(result, rule.criteria):
                        all_issues.append({
                            "type": "custom_rule",
                            "rule_id": rule.id,
                            "severity": rule.severity,
                            "message": rule.message
                        })
            except Exception as e:
                self.logger.error(f"Failed to apply rule {rule.id}: {e}")
        
        # 确定整体验证结果
        is_valid = not any(issue["severity"] == "error" for issue in all_issues)
        
        return ValidationResult(
            valid=is_valid,
            issues=all_issues,
            metadata={
                "timestamp": datetime.now().isoformat(),
                "total_issues": len(all_issues),
                "error_count": sum(1 for issue in all_issues if issue["severity"] == "error"),
                "warning_count": sum(1 for issue in all_issues if issue["severity"] == "warning")
            }
        )
    
    def _validate_content(self, result: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """
        验证内容规则
        
        Args:
            result: 算命结果
            criteria: 验证标准
            
        Returns:
            bool: 是否符合标准
        """
        content = str(result)
        
        if "min_length" in criteria and len(content) < criteria["min_length"]:
            return False
            
        if "max_length" in criteria and len(content) > criteria["max_length"]:
            return False
            
        if "required_patterns" in criteria:
            for pattern in criteria["required_patterns"]:
                if not re.search(pattern, content):
                    return False
                    
        if "forbidden_patterns" in criteria:
            for pattern in criteria["forbidden_patterns"]:
                if re.search(pattern, content):
                    return False
        
        return True
    
    def _validate_structure(self, result: Dict[str, Any], criteria: Dict[str, Any]) -> bool:
        """
        验证结构规则
        
        Args:
            result: 算命结果
            criteria: 验证标准
            
        Returns:
            bool: 是否符合标准
        """
        if "required_fields" in criteria:
            for field in criteria["required_fields"]:
                if field not in result:
                    return False
                    
        if "field_types" in criteria:
            for field, expected_type in criteria["field_types"].items():
                if field in result:
                    actual_type = type(result[field]).__name__
                    if actual_type != expected_type:
                        return False
        
        return True

# 使用示例
if __name__ == "__main__":
    # 初始化验证器
    validator = FortuneValidator()
    
    # 测试数据
    result = {
        "birth_info": {
            "year": 1990,
            "month": 1,
            "day": 1,
            "hour": 12,
            "minute": 0
        },
        "analysis": [
            "根据八字分析，您的命盘显示...",
            "从五行配置来看，金木水火土分布..."
        ],
        "predictions": [
            {
                "aspect": "事业",
                "time_range": "2024年",
                "content": "事业运势良好，有升职加薪机会"
            },
            {
                "aspect": "感情",
                "time_range": "2024年1月-2024年12月",
                "content": "感情生活稳定，可能遇到心仪对象"
            }
        ]
    }
    
    # 验证结果
    validation_result = validator.validate(result)
    print(json.dumps(validation_result.dict(), ensure_ascii=False, indent=2)) 