#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版主应用
根据API接口文档规范实现的完整应用
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import time
from datetime import datetime
import uvicorn

# 导入路由
from app.api.enhanced_api import router as enhanced_router
from app.api.miniprogram_api import router as miniprogram_router

# 导入核心模块
from app.core.config import settings
from app.core.ai_config import get_ai_status
from app.database import engine, Base
from app.models.api_models import APIResponse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动卦里乾坤API服务...")
    
    # 创建数据库表
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
    
    # 检查AI服务状态
    ai_status = get_ai_status()
    if ai_status["enabled"]:
        logger.info(f"✅ AI服务已启用 - 提供商: {ai_status['default_provider']}")
    else:
        logger.warning("⚠️ AI服务未启用，将使用模板回复")
    
    logger.info("🎉 服务启动完成!")
    
    yield
    
    # 关闭时执行
    logger.info("👋 正在关闭服务...")

# 创建FastAPI应用
app = FastAPI(
    title="卦里乾坤 API",
    description="""
    ## 🔮 卦里乾坤 - 专业命理测算API

    ### 功能特色
    - 🤖 **AI智能聊天** - 专业命理咨询助手
    - 📊 **八字分析** - 详细的命理解读
    - 🎯 **易经占卜** - 传统六爻占卜
    - 🏠 **风水分析** - 环境布局优化
    - ⭐ **紫微斗数** - 精准的星盘分析
    - 💰 **积分系统** - 完整的用户激励机制
    - 📱 **微信小程序** - 无缝集成支持

    ### API规范
    - 遵循RESTful设计原则
    - 统一的响应格式
    - 完整的错误处理
    - 详细的接口文档

    ### 技术栈
    - **后端**: FastAPI + Python 3.9+
    - **数据库**: MySQL 8.0 + Redis 6.0
    - **AI**: OpenAI GPT / 百度文心一言 / 本地模型
    - **部署**: Docker + Kubernetes
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # 生产环境应该限制具体主机
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url.path} - {request.client.host}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录响应信息
    logger.info(f"📤 {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
    
    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)
    response.headers["X-API-Version"] = "2.0.0"
    
    return response

# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": "error",
            "message": exc.detail,
            "data": None,
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url.path)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理异常: {type(exc).__name__} - {str(exc)}")
    
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "服务器内部错误，请稍后重试",
            "data": None,
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url.path)
        }
    )

# 注册路由
app.include_router(
    enhanced_router,
    tags=["Enhanced API v2.0"]
)

app.include_router(
    miniprogram_router,
    prefix="/api/miniprogram",
    tags=["小程序API v1.0"]
)

# 根路径
@app.get("/", tags=["基础"])
async def root():
    """API根路径"""
    return {
        "name": "卦里乾坤 API",
        "version": "2.0.0",
        "description": "专业的命理测算和AI咨询服务",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "微信小程序登录认证",
            "AI智能聊天咨询",
            "八字命理分析",
            "易经卦象占卜",
            "风水布局分析",
            "紫微斗数分析",
            "积分系统管理",
            "用户数据管理"
        ],
        "endpoints": {
            "enhanced_api": "/api",
            "miniprogram_api": "/api/miniprogram",
            "docs": "/docs",
            "redoc": "/redoc",
            "health": "/health"
        }
    }

# 健康检查
@app.get("/health", tags=["基础"])
async def health_check():
    """健康检查接口"""
    try:
        # 检查AI服务状态
        ai_status = get_ai_status()
        
        # 检查数据库连接（简化实现）
        db_status = {"status": "healthy", "connection": "ok"}
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0",
            "services": {
                "api": {"status": "healthy"},
                "database": db_status,
                "ai_service": ai_status,
                "cache": {"status": "healthy"}
            },
            "uptime": "运行中",
            "environment": getattr(settings, 'environment', 'development')
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )

# API信息
@app.get("/api/info", tags=["基础"])
async def api_info():
    """获取API详细信息"""
    return {
        "api": {
            "name": "卦里乾坤 API",
            "version": "2.0.0",
            "description": "专业的命理测算和AI咨询服务",
            "author": "卦里乾坤团队",
            "license": "MIT"
        },
        "features": {
            "authentication": "微信小程序登录",
            "ai_chat": "智能命理咨询",
            "fortune_telling": "多种命理分析",
            "user_management": "完整用户系统",
            "points_system": "积分激励机制"
        },
        "api_versions": {
            "v1.0": "/api/miniprogram",
            "v2.0": "/api"
        },
        "documentation": {
            "swagger": "/docs",
            "redoc": "/redoc",
            "openapi": "/openapi.json"
        },
        "support": {
            "email": "<EMAIL>",
            "website": "https://guali-qiankun.com",
            "github": "https://github.com/guali-qiankun/api"
        }
    }

# 系统状态
@app.get("/status", tags=["基础"])
async def system_status():
    """获取系统状态"""
    try:
        ai_status = get_ai_status()
        
        return {
            "system": {
                "status": "running",
                "version": "2.0.0",
                "environment": getattr(settings, 'environment', 'development'),
                "timestamp": datetime.now().isoformat()
            },
            "services": {
                "api_server": {"status": "healthy", "version": "2.0.0"},
                "ai_service": ai_status,
                "database": {"status": "connected", "type": "MySQL"},
                "cache": {"status": "connected", "type": "Redis"}
            },
            "metrics": {
                "total_requests": "统计中",
                "active_users": "统计中",
                "ai_calls": "统计中"
            }
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": "获取系统状态失败",
                "timestamp": datetime.now().isoformat()
            }
        )

if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "app.main_enhanced:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True
    )
