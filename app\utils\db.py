from typing import Any, Dict, List, Optional, Union
import pymysql
from pymysql.cursors import DictCursor
from dbutils.pooled_db import PooledDB
from contextlib import contextmanager
import json
from datetime import datetime
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class MySQLPool:
    """MySQL连接池管理类"""
    _instance = None
    _pool = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if self._pool is None:
            self._pool = PooledDB(
                creator=pymysql,
                maxconnections=settings.MYSQL_MAX_CONNECTIONS,
                mincached=settings.MYSQL_MIN_CACHED,
                maxcached=settings.MYSQL_MAX_CACHED,
                maxshared=settings.MYSQL_MAX_SHARED,
                blocking=True,
                maxusage=settings.MYSQL_MAX_USAGE,
                setsession=[],
                host=settings.MYSQL_HOST,
                port=settings.MYSQL_PORT,
                user=settings.MYSQL_USER,
                password=settings.MYSQL_PASSWORD,
                database=settings.MYSQL_DATABASE,
                charset='utf8mb4',
                cursorclass=DictCursor
            )

    @contextmanager
    def connection(self):
        """获取数据库连接的上下文管理器"""
        conn = self._pool.connection()
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"Database error: {str(e)}")
            raise
        finally:
            conn.close()

class MySQLClient:
    """MySQL数据库操作客户端"""

    def __init__(self):
        self.pool = MySQLPool()

    async def execute(self, query: str, params: tuple = None) -> int:
        """
        执行SQL语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            影响的行数
        """
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                affected_rows = cursor.execute(query, params)
                conn.commit()
                return affected_rows

    async def execute_many(self, query: str, params: List[tuple]) -> int:
        """
        批量执行SQL语句
        
        Args:
            query: SQL查询语句
            params: 参数列表
            
        Returns:
            影响的行数
        """
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                affected_rows = cursor.executemany(query, params)
                conn.commit()
                return affected_rows

    async def fetch_one(self, query: str, params: tuple = None) -> Optional[Dict]:
        """
        获取单条记录
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            记录字典或None
        """
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchone()

    async def fetch_all(self, query: str, params: tuple = None) -> List[Dict]:
        """
        获取多条记录
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            记录字典列表
        """
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()

    async def fetch_value(self, query: str, params: tuple = None) -> Any:
        """
        获取单个值
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            单个值
        """
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                result = cursor.fetchone()
                return result[0] if result else None

    async def insert(self, table: str, data: Dict) -> int:
        """
        插入数据
        
        Args:
            table: 表名
            data: 数据字典
            
        Returns:
            插入记录的ID
        """
        fields = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        query = f"INSERT INTO {table} ({fields}) VALUES ({placeholders})"
        
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, tuple(data.values()))
                conn.commit()
                return cursor.lastrowid

    async def update(self, table: str, data: Dict, where: str, params: tuple) -> int:
        """
        更新数据
        
        Args:
            table: 表名
            data: 更新的数据字典
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            影响的行数
        """
        set_clause = ', '.join([f"{k} = %s" for k in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where}"
        all_params = tuple(data.values()) + params
        
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                affected_rows = cursor.execute(query, all_params)
                conn.commit()
                return affected_rows

    async def delete(self, table: str, where: str, params: tuple) -> int:
        """
        删除数据
        
        Args:
            table: 表名
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            影响的行数
        """
        query = f"DELETE FROM {table} WHERE {where}"
        
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                affected_rows = cursor.execute(query, params)
                conn.commit()
                return affected_rows

    async def count(self, table: str, where: str = None, params: tuple = None) -> int:
        """
        统计记录数
        
        Args:
            table: 表名
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            记录数
        """
        query = f"SELECT COUNT(*) as count FROM {table}"
        if where:
            query += f" WHERE {where}"
            
        with self.pool.connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                result = cursor.fetchone()
                return result['count']

    async def exists(self, table: str, where: str, params: tuple) -> bool:
        """
        检查记录是否存在
        
        Args:
            table: 表名
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            是否存在
        """
        count = await self.count(table, where, params)
        return count > 0

# 创建全局数据库客户端实例
db = MySQLClient() 