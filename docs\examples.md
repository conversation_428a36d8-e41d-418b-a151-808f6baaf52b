# API 使用示例

## Python 示例

### 1. 基础问答

```python
import requests

def chat_with_model(query: str, api_key: str):
    """基础问答示例"""
    url = "http://localhost:8000/api/v1/chat"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "query": query,
        "max_length": 2048,
        "top_p": 0.7,
        "temperature": 0.95
    }
    
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 使用示例
result = chat_with_model(
    "什么是人工智能？",
    "your-api-key"
)
print(result['answer'])
```

### 2. 带上下文的对话

```python
import requests
from typing import List, Dict

class ChatSession:
    """对话会话管理"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.history: List[Dict] = []
        self.base_url = "http://localhost:8000/api/v1"
    
    def chat(self, query: str):
        """发送带上下文的对话"""
        url = f"{self.base_url}/chat"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "query": query,
            "context": self._format_history(),
            "max_length": 2048
        }
        
        response = requests.post(url, json=data, headers=headers)
        result = response.json()
        
        # 更新对话历史
        self.history.append({
            "query": query,
            "response": result['answer']
        })
        
        return result
    
    def _format_history(self) -> str:
        """格式化对话历史"""
        if not self.history:
            return ""
        
        formatted = []
        for item in self.history[-5:]:  # 只保留最近5轮对话
            formatted.extend([
                f"User: {item['query']}",
                f"Assistant: {item['response']}"
            ])
        return "\n".join(formatted)

# 使用示例
session = ChatSession("your-api-key")
response1 = session.chat("Python是什么？")
response2 = session.chat("它有什么特点？")
response3 = session.chat("与其他语言相比呢？")
```

### 3. 提交用户反馈

```python
import requests
from datetime import datetime

def submit_feedback(
    query_id: str,
    response_id: str,
    rating: int,
    feedback_text: str,
    api_key: str
):
    """提交用户反馈"""
    url = "http://localhost:8000/api/v1/feedback"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "query_id": query_id,
        "response_id": response_id,
        "rating": rating,
        "feedback_text": feedback_text,
        "is_helpful": rating > 3,
        "category": "general",
        "improvement_suggestions": None
    }
    
    response = requests.post(url, json=data, headers=headers)
    return response.json()

# 使用示例
feedback = submit_feedback(
    query_id="q123",
    response_id="r456",
    rating=4,
    feedback_text="回答很准确",
    api_key="your-api-key"
)
```

## JavaScript 示例

### 1. 基础API调用

```javascript
// chat.js
async function chatWithModel(query, apiKey) {
    const response = await fetch('http://localhost:8000/api/v1/chat', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query,
            max_length: 2048,
            top_p: 0.7,
            temperature: 0.95
        })
    });
    
    return await response.json();
}

// 使用示例
chatWithModel('什么是机器学习？', 'your-api-key')
    .then(result => console.log(result.answer))
    .catch(error => console.error('Error:', error));
```

### 2. WebSocket实时对话

```javascript
// chat-ws.js
class ChatWebSocket {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.ws = null;
        this.messageHandler = null;
    }
    
    connect() {
        this.ws = new WebSocket('ws://localhost:8000/api/v1/ws/chat');
        
        this.ws.onopen = () => {
            console.log('Connected to chat server');
            // 发送认证
            this.ws.send(JSON.stringify({
                type: 'auth',
                api_key: this.apiKey
            }));
        };
        
        this.ws.onmessage = (event) => {
            const response = JSON.parse(event.data);
            if (this.messageHandler) {
                this.messageHandler(response);
            }
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
        
        this.ws.onclose = () => {
            console.log('Disconnected from chat server');
        };
    }
    
    sendMessage(query) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'chat',
                query,
                timestamp: new Date().toISOString()
            }));
        } else {
            console.error('WebSocket is not connected');
        }
    }
    
    onMessage(handler) {
        this.messageHandler = handler;
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 使用示例
const chat = new ChatWebSocket('your-api-key');

chat.onMessage(response => {
    console.log('Received:', response);
});

chat.connect();

// 发送消息
chat.sendMessage('你好！');

// 断开连接
// chat.disconnect();
```

### 3. React组件示例

```jsx
// ChatComponent.jsx
import React, { useState, useEffect } from 'react';

const ChatComponent = ({ apiKey }) => {
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [loading, setLoading] = useState(false);
    
    const sendMessage = async () => {
        if (!input.trim()) return;
        
        setLoading(true);
        try {
            const response = await fetch('http://localhost:8000/api/v1/chat', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: input })
            });
            
            const result = await response.json();
            setMessages(prev => [...prev, {
                type: 'user',
                content: input
            }, {
                type: 'assistant',
                content: result.answer
            }]);
            
            setInput('');
        } catch (error) {
            console.error('Error:', error);
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <div className="chat-container">
            <div className="messages">
                {messages.map((msg, idx) => (
                    <div key={idx} className={`message ${msg.type}`}>
                        {msg.content}
                    </div>
                ))}
            </div>
            
            <div className="input-area">
                <input
                    value={input}
                    onChange={e => setInput(e.target.value)}
                    placeholder="输入问题..."
                    disabled={loading}
                />
                <button
                    onClick={sendMessage}
                    disabled={loading || !input.trim()}
                >
                    {loading ? '发送中...' : '发送'}
                </button>
            </div>
        </div>
    );
};

export default ChatComponent;
```

## cURL 示例

### 1. 发送聊天请求

```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "什么是深度学习？",
         "max_length": 2048,
         "top_p": 0.7,
         "temperature": 0.95
     }'
```

### 2. 提交反馈

```bash
curl -X POST "http://localhost:8000/api/v1/feedback" \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query_id": "q123",
         "response_id": "r456",
         "rating": 4,
         "feedback_text": "回答很准确",
         "is_helpful": true,
         "category": "general"
     }'
```

### 3. 获取性能指标

```bash
curl -X GET "http://localhost:8000/api/v1/metrics" \
     -H "Authorization: Bearer your-api-key"
``` 