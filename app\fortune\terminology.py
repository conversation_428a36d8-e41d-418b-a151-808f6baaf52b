from typing import Dict, List, Any, Optional, Set
import json
from pathlib import Path
import logging
import jieba
import re
from pydantic import BaseModel
from datetime import datetime

class Term(BaseModel):
    """术语模型"""
    name: str
    aliases: List[str]
    category: str
    description: str
    usage_examples: List[str]
    related_terms: List[str]
    metadata: Dict[str, Any] = {}

class TerminologyProcessor:
    """专业术语处理器"""
    
    def __init__(self, terms_file: str = "data/terminology.json"):
        """
        初始化术语处理器
        
        Args:
            terms_file: 术语库文件路径
        """
        self.terms_file = Path(terms_file)
        self.terms: Dict[str, Term] = {}
        self.term_aliases: Dict[str, str] = {}  # 别名到标准名的映射
        self.logger = logging.getLogger(__name__)
        self._load_terms()
        self._initialize_jieba()
    
    def _load_terms(self):
        """加载术语库"""
        if not self.terms_file.exists():
            self.logger.warning(f"Terminology file {self.terms_file} does not exist")
            return
            
        try:
            with open(self.terms_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                for term_data in data.get("terms", []):
                    term = Term(**term_data)
                    self.terms[term.name] = term
                    # 建立别名映射
                    for alias in term.aliases:
                        self.term_aliases[alias] = term.name
        except Exception as e:
            self.logger.error(f"Failed to load terminology: {e}")
    
    def _initialize_jieba(self):
        """初始化结巴分词器"""
        # 将所有术语添加到自定义词典
        for term in self.terms.values():
            jieba.add_word(term.name)
            for alias in term.aliases:
                jieba.add_word(alias)
    
    def identify_terms(self, text: str) -> List[Dict[str, Any]]:
        """
        识别文本中的专业术语
        
        Args:
            text: 输入文本
            
        Returns:
            List[Dict[str, Any]]: 识别到的术语列表
        """
        terms_found = []
        words = jieba.cut(text)
        
        for word in words:
            # 检查是否是标准术语
            if word in self.terms:
                terms_found.append({
                    "term": word,
                    "standard_form": word,
                    "type": "standard"
                })
            # 检查是否是别名
            elif word in self.term_aliases:
                standard_form = self.term_aliases[word]
                terms_found.append({
                    "term": word,
                    "standard_form": standard_form,
                    "type": "alias"
                })
        
        return terms_found
    
    def standardize_terms(self, text: str) -> str:
        """
        将文本中的术语标准化
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        result = text
        terms = self.identify_terms(text)
        
        # 从最长的术语开始替换，避免部分匹配问题
        terms.sort(key=lambda x: len(x["term"]), reverse=True)
        
        for term in terms:
            if term["type"] == "alias":
                result = result.replace(term["term"], term["standard_form"])
                
        return result
    
    def get_term_info(self, term: str) -> Optional[Term]:
        """
        获取术语信息
        
        Args:
            term: 术语名称或别名
            
        Returns:
            Optional[Term]: 术语信息
        """
        if term in self.terms:
            return self.terms[term]
        elif term in self.term_aliases:
            standard_form = self.term_aliases[term]
            return self.terms[standard_form]
        return None
    
    def get_related_terms(self, term: str) -> List[Term]:
        """
        获取相关术语
        
        Args:
            term: 术语名称
            
        Returns:
            List[Term]: 相关术语列表
        """
        term_info = self.get_term_info(term)
        if not term_info:
            return []
            
        related_terms = []
        for related_term_name in term_info.related_terms:
            if related_term_name in self.terms:
                related_terms.append(self.terms[related_term_name])
                
        return related_terms
    
    def explain_term(self, term: str, detailed: bool = False) -> Optional[Dict[str, Any]]:
        """
        解释术语
        
        Args:
            term: 术语名称或别名
            detailed: 是否返回详细信息
            
        Returns:
            Optional[Dict[str, Any]]: 术语解释
        """
        term_info = self.get_term_info(term)
        if not term_info:
            return None
            
        if detailed:
            return {
                "name": term_info.name,
                "aliases": term_info.aliases,
                "category": term_info.category,
                "description": term_info.description,
                "usage_examples": term_info.usage_examples,
                "related_terms": [
                    self.terms[rt].name for rt in term_info.related_terms
                    if rt in self.terms
                ]
            }
        else:
            return {
                "name": term_info.name,
                "description": term_info.description
            }
    
    def validate_term_usage(self, text: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        验证术语使用是否正确
        
        Args:
            text: 输入文本
            category: 术语类别（可选）
            
        Returns:
            List[Dict[str, Any]]: 验证结果列表
        """
        issues = []
        terms = self.identify_terms(text)
        
        for term_found in terms:
            term_info = self.get_term_info(term_found["term"])
            if term_info:
                # 检查类别
                if category and term_info.category != category:
                    issues.append({
                        "term": term_found["term"],
                        "type": "category_mismatch",
                        "message": f"术语 '{term_found['term']}' 不适用于 {category} 类别"
                    })
                
                # 检查是否使用了非标准形式
                if term_found["type"] == "alias":
                    issues.append({
                        "term": term_found["term"],
                        "type": "non_standard_form",
                        "message": f"建议使用标准术语 '{term_found['standard_form']}' 替代 '{term_found['term']}'"
                    })
        
        return issues
    
    def add_term(self, term: Term) -> bool:
        """
        添加新术语
        
        Args:
            term: 术语对象
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 更新内存中的术语库
            self.terms[term.name] = term
            for alias in term.aliases:
                self.term_aliases[alias] = term.name
                
            # 更新文件
            self._save_terms()
            
            # 更新分词器
            jieba.add_word(term.name)
            for alias in term.aliases:
                jieba.add_word(alias)
                
            return True
        except Exception as e:
            self.logger.error(f"Failed to add term: {e}")
            return False
    
    def _save_terms(self):
        """保存术语库到文件"""
        try:
            data = {
                "metadata": {
                    "last_updated": datetime.now().isoformat(),
                    "total_terms": len(self.terms)
                },
                "terms": [term.dict() for term in self.terms.values()]
            }
            
            with open(self.terms_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save terminology: {e}")
            raise

# 使用示例
if __name__ == "__main__":
    # 初始化术语处理器
    processor = TerminologyProcessor()
    
    # 添加示例术语
    term = Term(
        name="天干",
        aliases=["十天干"],
        category="basic_concepts",
        description="天干是中国传统历法中的重要概念，包括甲、乙、丙、丁、戊、己、庚、辛、壬、癸十个字",
        usage_examples=[
            "甲木生于亥子",
            "天干配地支可以确定年柱"
        ],
        related_terms=["地支", "干支"]
    )
    
    processor.add_term(term)
    
    # 测试文本
    text = "在十天干中，甲木属阳，乙木属阴"
    
    # 识别术语
    terms = processor.identify_terms(text)
    print("识别到的术语:", terms)
    
    # 标准化术语
    standardized = processor.standardize_terms(text)
    print("标准化后:", standardized)
    
    # 验证术语使用
    issues = processor.validate_term_usage(text, category="basic_concepts")
    print("验证结果:", issues) 