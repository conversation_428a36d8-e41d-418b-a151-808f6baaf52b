from typing import Dict, List, Any, Optional, Callable
import redis
from functools import lru_cache
import json
import logging
from datetime import datetime, timedelta
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading
from prometheus_client import Counter, Histogram, Gauge
import time
import hashlib

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, redis_config: Dict[str, Any] = None):
        """
        初始化性能优化器
        
        Args:
            redis_config: Redis配置
        """
        self.logger = logging.getLogger(__name__)
        
        # 初始化Redis客户端
        self.redis = self._init_redis(redis_config or {})
        
        # 初始化线程池
        self.executor = ThreadPoolExecutor(
            max_workers=10,
            thread_name_prefix="fortune_worker"
        )
        
        # 初始化监控指标
        self.request_counter = Counter(
            "fortune_requests_total",
            "Total number of fortune telling requests",
            ["type"]
        )
        
        self.response_time = Histogram(
            "fortune_response_seconds",
            "Response time in seconds",
            ["type"]
        )
        
        self.concurrent_requests = Gauge(
            "fortune_concurrent_requests",
            "Number of concurrent requests",
            ["type"]
        )
        
        # 请求队列
        self.request_queue = asyncio.Queue()
        
        # 资源锁
        self.locks = {}
    
    def _init_redis(self, config: Dict[str, Any]) -> redis.Redis:
        """
        初始化Redis客户端
        
        Args:
            config: Redis配置
            
        Returns:
            redis.Redis: Redis客户端
        """
        default_config = {
            "host": "localhost",
            "port": 6379,
            "db": 0,
            "decode_responses": True
        }
        
        config = {**default_config, **config}
        
        try:
            client = redis.Redis(**config)
            client.ping()  # 测试连接
            return client
        except Exception as e:
            self.logger.error(f"Failed to initialize Redis: {e}")
            return None
    
    @lru_cache(maxsize=1000)
    def get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存结果
        
        Args:
            cache_key: 缓存键
            
        Returns:
            Optional[Dict[str, Any]]: 缓存的结果
        """
        if not self.redis:
            return None
            
        try:
            data = self.redis.get(cache_key)
            if data:
                return json.loads(data)
        except Exception as e:
            self.logger.error(f"Failed to get cached result: {e}")
        
        return None
    
    def cache_result(self, cache_key: str, result: Dict[str, Any], expire: int = 3600) -> bool:
        """
        缓存结果
        
        Args:
            cache_key: 缓存键
            result: 结果数据
            expire: 过期时间（秒）
            
        Returns:
            bool: 是否成功
        """
        if not self.redis:
            return False
            
        try:
            data = json.dumps(result)
            self.redis.setex(cache_key, expire, data)
            return True
        except Exception as e:
            self.logger.error(f"Failed to cache result: {e}")
            return False
    
    def generate_cache_key(self, params: Dict[str, Any]) -> str:
        """
        生成缓存键
        
        Args:
            params: 请求参数
            
        Returns:
            str: 缓存键
        """
        # 排序参数以确保相同参数生成相同的键
        sorted_params = dict(sorted(params.items()))
        param_str = json.dumps(sorted_params, sort_keys=True)
        
        # 使用MD5生成固定长度的键
        return hashlib.md5(param_str.encode()).hexdigest()
    
    async def process_request(self, 
                            request_type: str,
                            params: Dict[str, Any],
                            processor: Callable) -> Dict[str, Any]:
        """
        处理请求
        
        Args:
            request_type: 请求类型
            params: 请求参数
            processor: 处理函数
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        # 增加并发请求计数
        self.concurrent_requests.labels(type=request_type).inc()
        
        try:
            # 检查缓存
            cache_key = self.generate_cache_key(params)
            cached_result = self.get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # 记录请求
            self.request_counter.labels(type=request_type).inc()
            
            # 测量响应时间
            with self.response_time.labels(type=request_type).time():
                # 使用线程池处理耗时操作
                result = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    processor,
                    params
                )
            
            # 缓存结果
            self.cache_result(cache_key, result)
            
            return result
        finally:
            # 减少并发请求计数
            self.concurrent_requests.labels(type=request_type).dec()
    
    async def batch_process(self,
                          requests: List[Dict[str, Any]],
                          processor: Callable,
                          max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """
        批量处理请求
        
        Args:
            requests: 请求列表
            processor: 处理函数
            max_concurrent: 最大并发数
            
        Returns:
            List[Dict[str, Any]]: 处理结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(request):
            async with semaphore:
                return await self.process_request(
                    request["type"],
                    request["params"],
                    processor
                )
        
        tasks = [process_with_semaphore(req) for req in requests]
        return await asyncio.gather(*tasks)
    
    def acquire_lock(self, resource_id: str, timeout: int = 10) -> bool:
        """
        获取资源锁
        
        Args:
            resource_id: 资源ID
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功获取锁
        """
        if not self.redis:
            return False
            
        lock_key = f"lock:{resource_id}"
        lock_value = str(threading.get_ident())
        
        return bool(
            self.redis.set(
                lock_key,
                lock_value,
                ex=timeout,
                nx=True
            )
        )
    
    def release_lock(self, resource_id: str) -> bool:
        """
        释放资源锁
        
        Args:
            resource_id: 资源ID
            
        Returns:
            bool: 是否成功释放锁
        """
        if not self.redis:
            return False
            
        lock_key = f"lock:{resource_id}"
        return bool(self.redis.delete(lock_key))
    
    async def rate_limit(self, key: str, limit: int, period: int) -> bool:
        """
        速率限制
        
        Args:
            key: 限制键
            limit: 限制次数
            period: 时间周期（秒）
            
        Returns:
            bool: 是否允许请求
        """
        if not self.redis:
            return True
            
        current = int(time.time())
        period_key = f"{key}:{current // period}"
        
        try:
            # 使用管道确保原子性
            pipe = self.redis.pipeline()
            pipe.incr(period_key)
            pipe.expire(period_key, period)
            result = pipe.execute()
            
            count = result[0]
            return count <= limit
        except Exception as e:
            self.logger.error(f"Failed to check rate limit: {e}")
            return True
    
    def clear_cache(self, pattern: str = None):
        """
        清理缓存
        
        Args:
            pattern: 缓存键模式
        """
        if not self.redis:
            return
            
        try:
            if pattern:
                keys = self.redis.keys(pattern)
                if keys:
                    self.redis.delete(*keys)
            else:
                self.redis.flushdb()
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标数据
        """
        return {
            "requests": {
                type_: self.request_counter.labels(type=type_)._value.get()
                for type_ in ["bazi", "ziwei", "yijing"]
            },
            "concurrent_requests": {
                type_: self.concurrent_requests.labels(type=type_)._value.get()
                for type_ in ["bazi", "ziwei", "yijing"]
            },
            "response_time": {
                type_: self.response_time.labels(type=type_).observe(0)
                for type_ in ["bazi", "ziwei", "yijing"]
            }
        }

# 使用示例
if __name__ == "__main__":
    # 初始化性能优化器
    optimizer = PerformanceOptimizer()
    
    async def main():
        # 模拟处理函数
        def process_fortune(params):
            time.sleep(1)  # 模拟耗时操作
            return {
                "result": "处理完成",
                "params": params
            }
        
        # 单个请求处理
        result = await optimizer.process_request(
            "bazi",
            {"birth_year": 1990, "birth_month": 1},
            process_fortune
        )
        print("单个请求结果:", result)
        
        # 批量请求处理
        requests = [
            {
                "type": "bazi",
                "params": {"birth_year": 1990, "birth_month": i}
            }
            for i in range(1, 4)
        ]
        
        results = await optimizer.batch_process(requests, process_fortune)
        print("批量处理结果:", results)
        
        # 获取性能指标
        metrics = optimizer.get_metrics()
        print("性能指标:", metrics)
    
    # 运行示例
    asyncio.run(main()) 