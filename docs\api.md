# API 文档

## 基础信息

- 基础URL: `http://localhost:8000`
- API版本: v1
- 接口前缀: `/api/v1`

## 认证

所有API请求需要在header中包含API密钥：

```http
Authorization: Bearer your-api-key
```

## 接口列表

### 1. 健康检查

检查服务是否正常运行。

```http
GET /health
```

响应示例：
```json
{
    "status": "healthy"
}
```

### 2. 问答接口

向模型提交问题并获取答案。

```http
POST /api/v1/chat
```

请求体：
```json
{
    "query": "什么是人工智能？",
    "context": "可选的上下文信息",
    "max_length": 2048,
    "top_p": 0.7,
    "temperature": 0.95
}
```

响应示例：
```json
{
    "answer": "人工智能是计算机科学的一个分支...",
    "confidence": 0.95,
    "sources": ["doc1", "doc2"],
    "processing_time": 1.23
}
```

### 3. 用户反馈接口

提交用户对回答的反馈。

```http
POST /api/v1/feedback
```

请求体：
```json
{
    "query_id": "q123",
    "response_id": "r456",
    "rating": 4,
    "feedback_text": "回答很准确",
    "is_helpful": true,
    "category": "知识问答",
    "improvement_suggestions": "可以提供更多相关例子"
}
```

响应示例：
```json
{
    "status": "success",
    "message": "Feedback submitted successfully",
    "feedback_id": "fb_20240101123456"
}
```

### 4. 性能指标接口

获取系统性能指标。

```http
GET /api/v1/metrics
```

响应示例：
```json
{
    "total_requests": 1000,
    "average_response_time": 0.5,
    "success_rate": 99.5,
    "memory_usage": "4.2GB"
}
```

## 错误处理

所有API错误响应遵循以下格式：

```json
{
    "error": {
        "code": "ERROR_CODE",
        "message": "错误描述",
        "details": {
            "additional": "错误详细信息"
        }
    }
}
```

常见错误码：

- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 429: 请求过于频繁
- 500: 服务器内部错误

## 速率限制

- 免费用户: 60次/小时
- 专业用户: 1000次/小时
- 企业用户: 无限制

超过限制将返回429错误。

## WebSocket 接口

### 实时对话

```javascript
ws://localhost:8000/api/v1/ws/chat

// 连接示例
const ws = new WebSocket('ws://localhost:8000/api/v1/ws/chat');
ws.onmessage = (event) => {
    const response = JSON.parse(event.data);
    console.log(response);
};

// 发送消息
ws.send(JSON.stringify({
    "query": "你好",
    "session_id": "session123"
}));
```

## API 客户端

提供多种语言的客户端SDK：

### Python

```python
from chatglm_client import ChatGLMClient

client = ChatGLMClient('your-api-key')
response = client.chat('你好')
print(response.answer)
```

### JavaScript

```javascript
import { ChatGLMClient } from 'chatglm-js-client';

const client = new ChatGLMClient('your-api-key');
const response = await client.chat('你好');
console.log(response.answer);
```