"""
优化的AI服务模块

提供完整的AI服务功能，包括：
1. 多模型支持和管理
2. 智能缓存策略
3. 性能监控和优化
4. 错误处理和恢复
5. 批量处理和并发控制
6. 配置动态调整
"""

from typing import Optional, List, Dict, Any, Union, AsyncGenerator, Callable
import asyncio
import time
import hashlib
import json
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from contextlib import asynccontextmanager
from functools import wraps
import numpy as np

from app.core.cache_manager import CacheManager
from app.core.config_optimized import settings
from app.core.error_handler_optimized import (
    ModelError, ModelNotLoadedError, ModelInferenceError, 
    ValidationError, RateLimitExceededError, with_retry
)

logger = logging.getLogger(__name__)

class ModelType(str, Enum):
    """模型类型枚举"""
    CHATGLM = "chatglm"
    GPT = "gpt"
    CLAUDE = "claude"
    CUSTOM = "custom"

class InferenceMode(str, Enum):
    """推理模式枚举"""
    SYNC = "sync"
    ASYNC = "async"
    BATCH = "batch"
    STREAM = "stream"

@dataclass
class GenerationConfig:
    """生成配置类"""
    max_length: int = 2048
    min_length: int = 1
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    repetition_penalty: float = 1.1
    do_sample: bool = True
    early_stopping: bool = False
    num_beams: int = 1
    num_return_sequences: int = 1
    pad_token_id: Optional[int] = None
    eos_token_id: Optional[int] = None
    use_cache: bool = True

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "GenerationConfig":
        """从字典创建配置"""
        return cls(**data)

    def validate(self) -> None:
        """验证配置参数"""
        if self.max_length <= 0:
            raise ValidationError("max_length must be positive")
        if self.min_length < 0:
            raise ValidationError("min_length must be non-negative")
        if self.min_length >= self.max_length:
            raise ValidationError("min_length must be less than max_length")
        if not 0.0 <= self.temperature <= 2.0:
            raise ValidationError("temperature must be between 0.0 and 2.0")
        if not 0.0 <= self.top_p <= 1.0:
            raise ValidationError("top_p must be between 0.0 and 1.0")
        if self.top_k <= 0:
            raise ValidationError("top_k must be positive")

@dataclass
class ModelInfo:
    """模型信息类"""
    name: str
    type: ModelType
    version: str
    size: str
    device: str
    precision: str
    load_time: Optional[float] = None
    memory_usage: Optional[float] = None
    is_loaded: bool = False
    last_used: Optional[datetime] = None
    total_requests: int = 0
    total_tokens: int = 0
    avg_latency: float = 0.0

    def update_stats(self, tokens: int, latency: float) -> None:
        """更新统计信息"""
        self.total_requests += 1
        self.total_tokens += tokens
        self.avg_latency = (self.avg_latency * (self.total_requests - 1) + latency) / self.total_requests
        self.last_used = datetime.utcnow()

@dataclass
class InferenceRequest:
    """推理请求类"""
    request_id: str
    prompt: str
    config: GenerationConfig
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context: Optional[List[Dict[str, str]]] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()

@dataclass
class InferenceResponse:
    """推理响应类"""
    request_id: str
    text: str
    tokens_generated: int
    latency: float
    model_name: str
    cached: bool = False
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.request_times: List[float] = []
        self.token_counts: List[int] = []
        self.error_counts: Dict[str, int] = {}
        self.start_time = time.time()

    def record_request(self, latency: float, tokens: int, success: bool = True, error_type: str = None) -> None:
        """记录请求性能"""
        if success:
            self.request_times.append(latency)
            self.token_counts.append(tokens)
        else:
            if error_type:
                self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1

    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.request_times:
            return {
                "total_requests": 0,
                "avg_latency": 0.0,
                "p95_latency": 0.0,
                "p99_latency": 0.0,
                "throughput": 0.0,
                "total_tokens": 0,
                "tokens_per_second": 0.0,
                "error_rate": 0.0,
                "uptime": time.time() - self.start_time
            }

        request_times = np.array(self.request_times)
        token_counts = np.array(self.token_counts)
        total_errors = sum(self.error_counts.values())
        total_requests = len(self.request_times) + total_errors

        return {
            "total_requests": total_requests,
            "successful_requests": len(self.request_times),
            "failed_requests": total_errors,
            "avg_latency": float(np.mean(request_times)),
            "p95_latency": float(np.percentile(request_times, 95)),
            "p99_latency": float(np.percentile(request_times, 99)),
            "throughput": len(self.request_times) / (time.time() - self.start_time),
            "total_tokens": int(np.sum(token_counts)),
            "tokens_per_second": float(np.sum(token_counts) / np.sum(request_times)),
            "error_rate": total_errors / total_requests if total_requests > 0 else 0.0,
            "error_breakdown": self.error_counts.copy(),
            "uptime": time.time() - self.start_time
        }

class RateLimiter:
    """速率限制器"""
    
    def __init__(self, requests_per_minute: int = 60, requests_per_day: int = 1000):
        self.requests_per_minute = requests_per_minute
        self.requests_per_day = requests_per_day
        self.minute_requests: Dict[str, List[float]] = {}
        self.day_requests: Dict[str, List[float]] = {}

    async def check_rate_limit(self, user_id: str) -> bool:
        """检查速率限制"""
        now = time.time()
        
        # 清理过期记录
        self._cleanup_expired_records(user_id, now)
        
        # 检查分钟级限制
        minute_requests = self.minute_requests.get(user_id, [])
        if len(minute_requests) >= self.requests_per_minute:
            raise RateLimitExceededError("minute", reset_time=int(60 - (now % 60)))
        
        # 检查日级限制
        day_requests = self.day_requests.get(user_id, [])
        if len(day_requests) >= self.requests_per_day:
            seconds_until_midnight = 86400 - (now % 86400)
            raise RateLimitExceededError("day", reset_time=int(seconds_until_midnight))
        
        # 记录当前请求
        if user_id not in self.minute_requests:
            self.minute_requests[user_id] = []
        if user_id not in self.day_requests:
            self.day_requests[user_id] = []
        
        self.minute_requests[user_id].append(now)
        self.day_requests[user_id].append(now)
        
        return True

    def _cleanup_expired_records(self, user_id: str, now: float) -> None:
        """清理过期的请求记录"""
        # 清理分钟级记录
        if user_id in self.minute_requests:
            self.minute_requests[user_id] = [
                t for t in self.minute_requests[user_id] if now - t < 60
            ]
        
        # 清理日级记录
        if user_id in self.day_requests:
            self.day_requests[user_id] = [
                t for t in self.day_requests[user_id] if now - t < 86400
            ]

class AIService:
    """
    优化的AI服务类
    
    提供完整的AI服务功能，包括模型管理、推理、缓存、监控等
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        default_model: str = "chatglm-6b",
        enable_monitoring: bool = True,
        enable_rate_limiting: bool = True
    ):
        """
        初始化AI服务
        
        Args:
            cache_manager: 缓存管理器实例
            default_model: 默认模型名称
            enable_monitoring: 是否启用性能监控
            enable_rate_limiting: 是否启用速率限制
        """
        self.cache_manager = cache_manager
        self.default_model = default_model
        self.models: Dict[str, ModelInfo] = {}
        self.current_model: Optional[str] = None
        
        # 性能监控
        self.monitor = PerformanceMonitor() if enable_monitoring else None
        
        # 速率限制
        self.rate_limiter = RateLimiter(
            requests_per_minute=settings.security.rate_limit_per_minute,
            requests_per_day=settings.security.rate_limit_per_day
        ) if enable_rate_limiting else None
        
        # 批处理队列
        self.batch_queue: asyncio.Queue = asyncio.Queue()
        self.batch_processor_task: Optional[asyncio.Task] = None
        
        # 并发控制
        self.max_concurrent_requests = 10
        self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)
        
        logger.info(f"AI服务初始化完成，默认模型: {default_model}")

    async def initialize(self) -> None:
        """初始化AI服务"""
        try:
            # 加载默认模型
            await self.load_model(self.default_model)
            
            # 启动批处理器
            if not self.batch_processor_task:
                self.batch_processor_task = asyncio.create_task(self._batch_processor())
            
            logger.info("AI服务初始化成功")
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            raise ModelError(f"AI服务初始化失败: {str(e)}")

    async def shutdown(self) -> None:
        """关闭AI服务"""
        try:
            # 停止批处理器
            if self.batch_processor_task:
                self.batch_processor_task.cancel()
                try:
                    await self.batch_processor_task
                except asyncio.CancelledError:
                    pass
            
            # 卸载所有模型
            for model_name in list(self.models.keys()):
                await self.unload_model(model_name)
            
            logger.info("AI服务已关闭")
        except Exception as e:
            logger.error(f"AI服务关闭时出现错误: {e}")

    @with_retry(max_retries=3)
    async def load_model(self, model_name: str, force_reload: bool = False) -> None:
        """
        加载AI模型
        
        Args:
            model_name: 模型名称
            force_reload: 是否强制重新加载
        """
        if model_name in self.models and self.models[model_name].is_loaded and not force_reload:
            logger.info(f"模型 {model_name} 已加载")
            return

        start_time = time.time()
        
        try:
            logger.info(f"开始加载模型: {model_name}")
            
            # 这里应该是实际的模型加载逻辑
            # 目前使用模拟实现
            await asyncio.sleep(2)  # 模拟加载时间
            
            load_time = time.time() - start_time
            
            # 创建模型信息
            model_info = ModelInfo(
                name=model_name,
                type=ModelType.CHATGLM,
                version="1.0.0",
                size="6B",
                device=settings.model.model_device,
                precision=settings.model.model_precision,
                load_time=load_time,
                is_loaded=True
            )
            
            self.models[model_name] = model_info
            self.current_model = model_name
            
            logger.info(f"模型 {model_name} 加载成功，耗时: {load_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"模型 {model_name} 加载失败: {e}")
            raise ModelError(f"模型加载失败: {str(e)}", model_name=model_name)

    async def unload_model(self, model_name: str) -> None:
        """卸载模型"""
        if model_name not in self.models:
            raise ModelNotLoadedError(model_name)
        
        try:
            # 实际的模型卸载逻辑
            del self.models[model_name]
            
            if self.current_model == model_name:
                self.current_model = None
            
            logger.info(f"模型 {model_name} 已卸载")
            
        except Exception as e:
            logger.error(f"模型 {model_name} 卸载失败: {e}")
            raise ModelError(f"模型卸载失败: {str(e)}", model_name=model_name)

    async def switch_model(self, model_name: str) -> None:
        """切换当前模型"""
        if model_name not in self.models or not self.models[model_name].is_loaded:
            await self.load_model(model_name)
        
        self.current_model = model_name
        logger.info(f"已切换到模型: {model_name}")

    async def generate_text(
        self,
        prompt: str,
        config: Optional[GenerationConfig] = None,
        user_id: Optional[str] = None,
        use_cache: bool = True,
        mode: InferenceMode = InferenceMode.SYNC
    ) -> Union[InferenceResponse, AsyncGenerator[str, None]]:
        """
        生成文本
        
        Args:
            prompt: 输入提示
            config: 生成配置
            user_id: 用户ID
            use_cache: 是否使用缓存
            mode: 推理模式
            
        Returns:
            推理响应或流式生成器
        """
        # 验证输入
        if not prompt or not prompt.strip():
            raise ValidationError("提示不能为空")
        
        if not self.current_model:
            raise ModelNotLoadedError("当前没有加载的模型")
        
        # 检查速率限制
        if self.rate_limiter and user_id:
            await self.rate_limiter.check_rate_limit(user_id)
        
        # 使用默认配置
        if config is None:
            config = GenerationConfig()
        config.validate()
        
        # 创建请求
        request = InferenceRequest(
            request_id=self._generate_request_id(prompt, config),
            prompt=prompt,
            config=config,
            user_id=user_id
        )
        
        # 检查缓存
        if use_cache:
            cached_response = await self._get_cached_response(request)
            if cached_response:
                return cached_response
        
        # 根据模式处理
        if mode == InferenceMode.STREAM:
            return self._generate_stream(request)
        elif mode == InferenceMode.BATCH:
            return await self._add_to_batch(request)
        else:
            return await self._generate_sync(request, use_cache)

    async def _generate_sync(self, request: InferenceRequest, use_cache: bool = True) -> InferenceResponse:
        """同步生成文本"""
        async with self.semaphore:  # 并发控制
            start_time = time.time()
            
            try:
                # 实际的模型推理逻辑
                # 这里使用模拟实现
                generated_text = await self._mock_inference(request.prompt, request.config)
                
                latency = time.time() - start_time
                tokens_generated = len(generated_text.split())
                
                # 创建响应
                response = InferenceResponse(
                    request_id=request.request_id,
                    text=generated_text,
                    tokens_generated=tokens_generated,
                    latency=latency,
                    model_name=self.current_model,
                    cached=False
                )
                
                # 缓存响应
                if use_cache:
                    await self._cache_response(request, response)
                
                # 更新统计
                if self.monitor:
                    self.monitor.record_request(latency, tokens_generated, True)
                
                model_info = self.models[self.current_model]
                model_info.update_stats(tokens_generated, latency)
                
                return response
                
            except Exception as e:
                latency = time.time() - start_time
                if self.monitor:
                    self.monitor.record_request(latency, 0, False, type(e).__name__)
                
                logger.error(f"文本生成失败: {e}")
                raise ModelInferenceError(
                    self.current_model,
                    original_error=str(e)
                )

    async def _generate_stream(self, request: InferenceRequest) -> AsyncGenerator[str, None]:
        """流式生成文本"""
        async with self.semaphore:
            try:
                # 模拟流式生成
                generated_text = await self._mock_inference(request.prompt, request.config)
                words = generated_text.split()
                
                for word in words:
                    yield word + " "
                    await asyncio.sleep(0.1)  # 模拟生成延迟
                    
            except Exception as e:
                logger.error(f"流式生成失败: {e}")
                raise ModelInferenceError(
                    self.current_model,
                    original_error=str(e)
                )

    async def _add_to_batch(self, request: InferenceRequest) -> InferenceResponse:
        """添加到批处理队列"""
        future = asyncio.Future()
        await self.batch_queue.put((request, future))
        return await future

    async def _batch_processor(self) -> None:
        """批处理器"""
        batch_size = settings.model.batch_size
        batch_timeout = 1.0  # 1秒超时
        
        while True:
            try:
                batch = []
                futures = []
                
                # 收集批次
                start_time = time.time()
                while len(batch) < batch_size and (time.time() - start_time) < batch_timeout:
                    try:
                        request, future = await asyncio.wait_for(
                            self.batch_queue.get(),
                            timeout=batch_timeout - (time.time() - start_time)
                        )
                        batch.append(request)
                        futures.append(future)
                    except asyncio.TimeoutError:
                        break
                
                if batch:
                    # 批量推理
                    responses = await self._batch_inference(batch)
                    
                    # 返回结果
                    for future, response in zip(futures, responses):
                        future.set_result(response)
                
            except Exception as e:
                logger.error(f"批处理器错误: {e}")
                # 将错误传播给所有等待的future
                for future in futures:
                    if not future.done():
                        future.set_exception(e)

    async def _batch_inference(self, requests: List[InferenceRequest]) -> List[InferenceResponse]:
        """批量推理"""
        responses = []
        
        for request in requests:
            # 这里应该是实际的批量推理逻辑
            # 目前使用单个推理模拟
            response = await self._generate_sync(request, use_cache=False)
            responses.append(response)
        
        return responses

    async def _mock_inference(self, prompt: str, config: GenerationConfig) -> str:
        """模拟推理（用于演示）"""
        # 模拟处理时间
        await asyncio.sleep(0.5)
        
        # 简单的文本生成模拟
        responses = [
            f"根据您的问题 '{prompt}'，我的回答是：这是一个很好的问题。",
            f"关于 '{prompt}' 这个话题，我认为需要从多个角度来分析。",
            f"针对您提到的 '{prompt}'，让我为您详细解答。"
        ]
        
        # 根据prompt长度选择响应
        return responses[len(prompt) % len(responses)]

    def _generate_request_id(self, prompt: str, config: GenerationConfig) -> str:
        """生成请求ID"""
        content = f"{prompt}:{json.dumps(config.to_dict(), sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()

    async def _get_cached_response(self, request: InferenceRequest) -> Optional[InferenceResponse]:
        """获取缓存的响应"""
        cache_key = f"ai_response:{request.request_id}"
        cached_data = await self.cache_manager.get(cache_key)
        
        if cached_data:
            response_data = json.loads(cached_data)
            response = InferenceResponse(**response_data)
            response.cached = True
            return response
        
        return None

    async def _cache_response(self, request: InferenceRequest, response: InferenceResponse) -> None:
        """缓存响应"""
        cache_key = f"ai_response:{request.request_id}"
        cache_data = json.dumps(asdict(response), default=str)
        ttl = settings.cache.qa_cache_ttl
        
        await self.cache_manager.set(cache_key, cache_data, ttl=ttl)

    async def get_model_info(self, model_name: Optional[str] = None) -> Union[ModelInfo, Dict[str, ModelInfo]]:
        """获取模型信息"""
        if model_name:
            if model_name not in self.models:
                raise ModelNotLoadedError(model_name)
            return self.models[model_name]
        
        return self.models.copy()

    async def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.monitor:
            return {"monitoring_disabled": True}
        
        stats = self.monitor.get_stats()
        
        # 添加模型统计
        model_stats = {}
        for name, info in self.models.items():
            model_stats[name] = {
                "total_requests": info.total_requests,
                "total_tokens": info.total_tokens,
                "avg_latency": info.avg_latency,
                "last_used": info.last_used.isoformat() if info.last_used else None,
                "memory_usage": info.memory_usage
            }
        
        stats["models"] = model_stats
        stats["current_model"] = self.current_model
        
        return stats

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "models": {},
            "performance": {}
        }
        
        try:
            # 检查模型状态
            for name, info in self.models.items():
                status["models"][name] = {
                    "loaded": info.is_loaded,
                    "last_used": info.last_used.isoformat() if info.last_used else None
                }
            
            # 检查性能指标
            if self.monitor:
                perf_stats = self.monitor.get_stats()
                status["performance"] = {
                    "avg_latency": perf_stats["avg_latency"],
                    "error_rate": perf_stats["error_rate"],
                    "throughput": perf_stats["throughput"]
                }
                
                # 检查性能阈值
                thresholds = settings.monitoring.performance_thresholds
                if perf_stats["avg_latency"] > thresholds["max_latency"]:
                    status["status"] = "degraded"
                if perf_stats["error_rate"] > thresholds["max_error_rate"]:
                    status["status"] = "unhealthy"
            
            return status
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    @asynccontextmanager
    async def model_context(self, model_name: str):
        """模型上下文管理器"""
        original_model = self.current_model
        try:
            await self.switch_model(model_name)
            yield
        finally:
            if original_model and original_model != model_name:
                await self.switch_model(original_model)

    def performance_decorator(self, func: Callable) -> Callable:
        """性能监控装饰器"""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_type = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_type = type(e).__name__
                raise
            finally:
                if self.monitor:
                    latency = time.time() - start_time
                    self.monitor.record_request(latency, 0, success, error_type)
        
        return wrapper

# 工厂函数
async def create_ai_service(
    cache_manager: CacheManager,
    default_model: str = "chatglm-6b"
) -> AIService:
    """创建AI服务实例"""
    service = AIService(
        cache_manager=cache_manager,
        default_model=default_model,
        enable_monitoring=settings.monitoring.enable_metrics,
        enable_rate_limiting=True
    )
    
    await service.initialize()
    return service

__all__ = [
    "AIService",
    "ModelType",
    "InferenceMode", 
    "GenerationConfig",
    "ModelInfo",
    "InferenceRequest",
    "InferenceResponse",
    "PerformanceMonitor",
    "RateLimiter",
    "create_ai_service"
] 