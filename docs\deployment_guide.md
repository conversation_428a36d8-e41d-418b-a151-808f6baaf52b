# 🚀 AI占卜项目 - 完整部署指南

## 📋 目录

- [概述](#概述)
- [系统要求](#系统要求)
- [部署方式](#部署方式)
  - [1. 阿里云ECS手动部署](#1-阿里云ecs手动部署)
  - [2. CI/CD自动化部署](#2-cicd自动化部署)
  - [3. Kubernetes集群部署](#3-kubernetes集群部署)
- [配置说明](#配置说明)
- [安全配置](#安全配置)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 概述

本指南提供了AI占卜项目的完整部署方案，支持多种部署方式和环境。项目采用现代化的容器化架构，支持自动化CI/CD流程。

### 🎯 部署特性

- ✅ **容器化部署** - 使用Docker和Docker Compose
- ✅ **自动化CI/CD** - GitHub Actions工作流
- ✅ **多环境支持** - 开发、测试、生产环境
- ✅ **高可用架构** - PostgreSQL + Redis + Nginx
- ✅ **完整监控** - Prometheus + Grafana
- ✅ **安全防护** - 多层安全措施
- ✅ **自动扩缩容** - 支持水平扩展

## 系统要求

### 🖥️ 硬件要求

| 组件 | 最低配置 | 推荐配置 | 说明 |
|------|----------|----------|------|
| CPU | 4核 | 8核+ | 支持AVX指令集 |
| 内存 | 8GB | 16GB+ | AI模型需要较大内存 |
| 磁盘 | 50GB | 100GB+ | SSD存储，用于模型和数据 |
| 网络 | 10Mbps | 100Mbps+ | 稳定的公网连接 |

### 🐧 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Amazon Linux 2
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.25+
- **Shell**: Bash 4.0+

## 部署方式

### 1. 阿里云ECS手动部署

#### 1.1 准备ECS实例

```bash
# 登录阿里云控制台，创建ECS实例
# 推荐配置：
# - 实例规格：ecs.g6.2xlarge (8vCPU 32GB)
# - 操作系统：Ubuntu 20.04 LTS
# - 磁盘：100GB高效云盘
# - 网络：VPC，配置弹性公网IP
```

#### 1.2 配置安全组

在阿里云控制台配置以下安全组规则：

```text
入站规则：
- 22/tcp    SSH访问        您的IP地址
- 80/tcp    HTTP           0.0.0.0/0
- 443/tcp   HTTPS          0.0.0.0/0
- 8000/tcp  应用API        0.0.0.0/0（可限制为特定IP）
- 3000/tcp  Grafana        管理员IP
- 9090/tcp  Prometheus     内网/管理员IP

内网规则：
- 5432/tcp  PostgreSQL     VPC内网
- 6379/tcp  Redis          VPC内网
```

#### 1.3 执行自动化部署

```bash
# 1. 连接到ECS实例
ssh root@您的ECS公网IP

# 2. 更新系统包
apt update && apt upgrade -y

# 3. 克隆项目代码
git clone https://github.com/your-username/ai-fortune-project.git
cd ai-fortune-project

# 4. 运行自动化部署脚本
chmod +x deploy_aliyun.sh
./deploy_aliyun.sh

# 5. 按照提示完成配置
# 脚本会自动安装Docker、创建配置文件、启动服务
```

#### 1.4 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f app

# 测试健康检查
curl http://localhost:8000/health

# 访问应用
# 浏览器打开: http://您的ECS公网IP:8000
```

### 2. CI/CD自动化部署

#### 2.1 配置GitHub Secrets

在GitHub仓库设置中添加以下Secrets：

```text
# 阿里云配置
ALIYUN_REGISTRY_USERNAME=your_username
ALIYUN_REGISTRY_PASSWORD=your_password
ALIYUN_REGION=cn-hangzhou
ALIYUN_ACCESS_KEY_ID=your_access_key
ALIYUN_ACCESS_KEY_SECRET=your_secret_key

# ECS配置
ECS_HOST=your_ecs_public_ip
ECS_USERNAME=root
ECS_PRIVATE_KEY=your_private_key_content

# 应用配置
SECRET_KEY=your_super_secret_key
POSTGRES_DB=fortune
POSTGRES_USER=fortune
POSTGRES_PASSWORD=your_db_password
REDIS_PASSWORD=your_redis_password
GRAFANA_PASSWORD=your_grafana_password

# 通知配置（可选）
SLACK_WEBHOOK_URL=your_slack_webhook
```

#### 2.2 触发自动部署

```bash
# 推送代码到main分支触发部署
git add .
git commit -m "feat: 添加新功能"
git push origin main

# 或创建版本标签
git tag v1.0.0
git push origin v1.0.0
```

#### 2.3 监控部署流程

在GitHub Actions页面监控部署过程：

1. **代码质量检查** - 格式、类型、安全检查
2. **单元测试** - 运行测试套件
3. **构建镜像** - 构建并推送Docker镜像
4. **安全扫描** - 漏洞扫描
5. **部署到生产** - 自动部署到ECS
6. **性能测试** - 自动化性能测试

### 3. Kubernetes集群部署

#### 3.1 准备Kubernetes集群

```bash
# 使用阿里云容器服务ACK
# 或自建Kubernetes集群

# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
```

#### 3.2 部署到Kubernetes

```bash
# 1. 创建命名空间
kubectl create namespace ai-fortune

# 2. 创建配置文件
kubectl apply -f deploy/kubernetes/

# 3. 查看部署状态
kubectl get pods -n ai-fortune
kubectl get services -n ai-fortune

# 4. 获取访问地址
kubectl get ingress -n ai-fortune
```

## 配置说明

### 环境变量配置

创建 `.env` 文件：

```bash
# 应用配置
PROJECT_NAME="AI Fortune Telling System"
ENVIRONMENT=production
DEBUG=false
PORT=8000
WORKERS=4

# 安全配置
SECRET_KEY=your-super-secret-key-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# 数据库配置
POSTGRES_DB=fortune
POSTGRES_USER=fortune
POSTGRES_PASSWORD=your-secure-password
POSTGRES_PORT=5432

# Redis配置
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# AI模型配置
MODEL_DEVICE=cpu          # cpu, cuda, mps
MODEL_PRECISION=fp32      # fp32, fp16, bf16

# 速率限制
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_DAY=10000

# 监控配置
ENABLE_METRICS=true
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=your-grafana-password
LOG_LEVEL=INFO

# Nginx配置
NGINX_PORT=80
NGINX_SSL_PORT=443
```

### Docker Compose配置

```yaml
# docker-compose.override.yml - 本地开发覆盖
version: '3.9'
services:
  app:
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ./app:/app/app:ro  # 热重载
    command: ["uvicorn", "app.main_optimized:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

## 安全配置

### 🔒 基础安全措施

#### 1. 网络安全

```bash
# 配置防火墙
ufw enable
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8000/tcp

# 限制数据库访问
ufw deny 5432/tcp
ufw deny 6379/tcp
```

#### 2. SSL/TLS配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 3. 应用安全

```yaml
# nginx/ssl.conf
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
```

### 🛡️ 高级安全配置

#### 1. WAF（Web应用防火墙）

```bash
# 使用阿里云WAF或Cloudflare
# 配置规则：
# - SQL注入防护
# - XSS防护
# - CC攻击防护
# - 恶意爬虫防护
```

#### 2. 密钥管理

```bash
# 使用阿里云KMS或HashiCorp Vault
# 环境变量加密存储
# 定期轮换密钥
```

#### 3. 访问控制

```bash
# 实施最小权限原则
# 使用IAM角色
# 配置VPN访问
# 启用审计日志
```

## 监控和维护

### 📊 监控配置

#### 1. Prometheus指标

```yaml
# prometheus/rules/app-alerts.yml
groups:
  - name: app-alerts
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        annotations:
          summary: "应用响应时间过高"
          
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        annotations:
          summary: "应用错误率过高"
```

#### 2. Grafana仪表板

```json
{
  "dashboard": {
    "title": "AI占卜应用监控",
    "panels": [
      {
        "title": "请求速率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "响应时间",
        "type": "graph", 
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

#### 3. 日志聚合

```bash
# 使用ELK Stack或阿里云SLS
# 配置日志收集和分析
# 设置告警规则
```

### 🔧 维护任务

#### 1. 定期备份

```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# PostgreSQL备份
docker-compose exec -T postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > "$BACKUP_DIR/postgres_$DATE.sql"

# Redis备份
docker-compose exec -T redis redis-cli --rdb "$BACKUP_DIR/redis_$DATE.rdb"

# 上传到阿里云OSS
aliyun oss cp "$BACKUP_DIR/" oss://your-backup-bucket/backups/ --recursive
```

#### 2. 更新管理

```bash
# 应用更新
./deploy_aliyun.sh --update

# 系统更新
sudo apt update && sudo apt upgrade -y
sudo reboot

# Docker镜像更新
docker-compose pull
docker-compose up -d
```

#### 3. 性能优化

```bash
# 数据库优化
docker-compose exec postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -c "VACUUM ANALYZE;"

# Redis优化
docker-compose exec redis redis-cli MEMORY PURGE

# 清理无用镜像
docker system prune -f
```

## 故障排除

### 🔍 常见问题

#### 1. 应用启动失败

```bash
# 检查日志
docker-compose logs app

# 常见原因：
# - 数据库连接失败
# - 环境变量配置错误
# - 端口冲突
# - 内存不足

# 解决方案：
# 1. 检查配置文件
# 2. 确认服务依赖
# 3. 检查系统资源
```

#### 2. 数据库连接问题

```bash
# 检查PostgreSQL状态
docker-compose exec postgres pg_isready

# 检查连接配置
docker-compose exec app python -c "
from app.core.config_optimized import settings
print(settings.get_database_uri())
"

# 测试连接
docker-compose exec postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;"
```

#### 3. 性能问题

```bash
# 检查系统资源
htop
df -h
free -h

# 检查应用性能
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# 分析慢查询
docker-compose exec postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
"
```

#### 4. 网络问题

```bash
# 检查端口监听
netstat -tlnp | grep :8000

# 检查防火墙
ufw status

# 检查DNS解析
nslookup your-domain.com

# 测试网络连通性
curl -I http://your-domain.com
```

### 📞 获取支持

- **文档**: [项目Wiki](https://github.com/your-repo/wiki)
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- **社区讨论**: [Discussions](https://github.com/your-repo/discussions)
- **技术支持**: <EMAIL>

### 📚 相关资源

- [Docker官方文档](https://docs.docker.com/)
- [阿里云ECS文档](https://help.aliyun.com/product/25365.html)
- [Kubernetes文档](https://kubernetes.io/docs/)
- [Prometheus文档](https://prometheus.io/docs/)
- [Grafana文档](https://grafana.com/docs/)

---

*最后更新: 2024年1月*

> 💡 **提示**: 部署过程中遇到问题，请先查看日志和监控信息，大多数问题都可以通过日志快速定位和解决。 