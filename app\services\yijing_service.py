"""
易经占卜服务
提供专业的易经卦象分析和占卜功能
"""
import random
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class YijingService:
    """易经占卜服务类"""
    
    def __init__(self):
        # 六十四卦基础信息
        self.hexagrams = {
            "乾": {"number": 1, "trigrams": ["乾", "乾"], "nature": "天", "attribute": "刚健"},
            "坤": {"number": 2, "trigrams": ["坤", "坤"], "nature": "地", "attribute": "柔顺"},
            "屯": {"number": 3, "trigrams": ["震", "坎"], "nature": "雷水", "attribute": "积聚"},
            "蒙": {"number": 4, "trigrams": ["坎", "艮"], "nature": "水山", "attribute": "启蒙"},
            "需": {"number": 5, "trigrams": ["乾", "坎"], "nature": "天水", "attribute": "等待"},
            "讼": {"number": 6, "trigrams": ["坎", "乾"], "nature": "水天", "attribute": "争讼"},
            "师": {"number": 7, "trigrams": ["坤", "坎"], "nature": "地水", "attribute": "军师"},
            "比": {"number": 8, "trigrams": ["坎", "坤"], "nature": "水地", "attribute": "亲比"},
            "小畜": {"number": 9, "trigrams": ["乾", "巽"], "nature": "天风", "attribute": "小聚"},
            "履": {"number": 10, "trigrams": ["兑", "乾"], "nature": "泽天", "attribute": "践履"},
            "泰": {"number": 11, "trigrams": ["坤", "乾"], "nature": "地天", "attribute": "通泰"},
            "否": {"number": 12, "trigrams": ["乾", "坤"], "nature": "天地", "attribute": "否塞"},
            "同人": {"number": 13, "trigrams": ["乾", "离"], "nature": "天火", "attribute": "同心"},
            "大有": {"number": 14, "trigrams": ["离", "乾"], "nature": "火天", "attribute": "大得"},
            "谦": {"number": 15, "trigrams": ["坤", "艮"], "nature": "地山", "attribute": "谦逊"},
            "豫": {"number": 16, "trigrams": ["震", "坤"], "nature": "雷地", "attribute": "安乐"}
        }
        
        # 八卦基础信息
        self.trigrams = {
            "乾": {"element": "金", "direction": "西北", "family": "父", "nature": "天"},
            "兑": {"element": "金", "direction": "西", "family": "少女", "nature": "泽"},
            "离": {"element": "火", "direction": "南", "family": "中女", "nature": "火"},
            "震": {"element": "木", "direction": "东", "family": "长男", "nature": "雷"},
            "巽": {"element": "木", "direction": "东南", "family": "长女", "nature": "风"},
            "坎": {"element": "水", "direction": "北", "family": "中男", "nature": "水"},
            "艮": {"element": "土", "direction": "东北", "family": "少男", "nature": "山"},
            "坤": {"element": "土", "direction": "西南", "family": "母", "nature": "地"}
        }
        
        # 爻的含义
        self.yao_meanings = {
            9: {"name": "老阳", "symbol": "⚌", "change": True, "nature": "阳"},
            8: {"name": "少阴", "symbol": "⚋", "change": False, "nature": "阴"},
            7: {"name": "少阳", "symbol": "⚊", "change": False, "nature": "阳"},
            6: {"name": "老阴", "symbol": "⚍", "change": True, "nature": "阴"}
        }
    
    async def divine(self, question: str, method: str = "manual", coins: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        执行易经占卜
        
        Args:
            question: 占卜问题
            method: 起卦方式 (manual/auto/time)
            coins: 手动投币结果
            
        Returns:
            占卜结果字典
        """
        try:
            logger.info(f"开始易经占卜：{question[:30]}...")
            
            # 生成卦象
            hexagram_result = await self._generate_hexagram(method, coins)
            
            # 分析卦象
            interpretation = await self._interpret_hexagram(
                hexagram_result["main_hexagram"],
                hexagram_result.get("changed_hexagram"),
                question
            )
            
            # 生成建议
            advice = await self._generate_advice(
                hexagram_result["main_hexagram"],
                interpretation,
                question
            )
            
            # 计算吉凶评分
            score = self._calculate_score(hexagram_result["main_hexagram"], interpretation)
            
            # 确定有利因素
            lucky_elements = self._get_lucky_elements(hexagram_result["main_hexagram"])
            
            result = {
                "hexagram": hexagram_result,
                "interpretation": interpretation,
                "advice": advice,
                "score": score,
                "lucky_elements": lucky_elements
            }
            
            logger.info(f"易经占卜完成，主卦：{hexagram_result['main_hexagram']['name']}")
            return result
            
        except Exception as e:
            logger.error(f"易经占卜失败：{e}")
            # 返回基础占卜结果
            return await self._get_fallback_divination(question)
    
    async def _generate_hexagram(self, method: str, coins: Optional[List[int]] = None) -> Dict[str, Any]:
        """生成卦象"""
        
        if method == "manual" and coins:
            # 手动投币方式
            yao_values = coins[:6] if len(coins) >= 6 else coins + [random.choice([6, 7, 8, 9]) for _ in range(6 - len(coins))]
        elif method == "time":
            # 时间起卦
            now = datetime.now()
            seed = now.year + now.month + now.day + now.hour + now.minute
            random.seed(seed)
            yao_values = [random.choice([6, 7, 8, 9]) for _ in range(6)]
        else:
            # 自动随机起卦
            yao_values = [random.choice([6, 7, 8, 9]) for _ in range(6)]
        
        # 构建主卦
        main_hex_binary = []
        changed_hex_binary = []
        changing_lines = []
        
        for i, yao in enumerate(yao_values):
            yao_info = self.yao_meanings[yao]
            
            # 主卦爻
            if yao_info["nature"] == "阳":
                main_hex_binary.append(1)
            else:
                main_hex_binary.append(0)
            
            # 变卦爻
            if yao_info["change"]:
                changed_hex_binary.append(1 - main_hex_binary[i])
                changing_lines.append(i + 1)
            else:
                changed_hex_binary.append(main_hex_binary[i])
        
        # 识别卦名
        main_hexagram = self._identify_hexagram(main_hex_binary)
        changed_hexagram = self._identify_hexagram(changed_hex_binary) if changing_lines else None
        
        return {
            "main_hexagram": main_hexagram,
            "changed_hexagram": changed_hexagram,
            "yao_values": yao_values,
            "changing_lines": changing_lines,
            "method": method
        }
    
    def _identify_hexagram(self, binary_rep: List[int]) -> Dict[str, Any]:
        """根据二进制表示识别卦象"""
        
        # 简化版卦象识别，实际应用中需要完整的64卦对照表
        upper_trigram = binary_rep[3:6]  # 上卦
        lower_trigram = binary_rep[0:3]  # 下卦
        
        # 根据二进制模式匹配基本卦象
        patterns = {
            (1, 1, 1): "乾",
            (0, 0, 0): "坤",
            (1, 0, 0): "震",
            (0, 1, 0): "坎",
            (0, 0, 1): "艮",
            (1, 1, 0): "巽",
            (0, 1, 1): "离",
            (1, 0, 1): "兑"
        }
        
        upper_name = patterns.get(tuple(upper_trigram), "乾")
        lower_name = patterns.get(tuple(lower_trigram), "坤")
        
        # 组合卦名（简化版）
        if upper_name == "乾" and lower_name == "乾":
            hexagram_name = "乾"
        elif upper_name == "坤" and lower_name == "坤":
            hexagram_name = "坤"
        elif upper_name == "震" and lower_name == "坎":
            hexagram_name = "屯"
        elif upper_name == "坎" and lower_name == "艮":
            hexagram_name = "蒙"
        else:
            # 组合名称
            hexagram_name = f"{upper_name}{lower_name}"
        
        hexagram_info = self.hexagrams.get(hexagram_name, {
            "number": 1,
            "trigrams": [upper_name, lower_name],
            "nature": f"{self.trigrams[upper_name]['nature']}{self.trigrams[lower_name]['nature']}",
            "attribute": "变化"
        })
        
        return {
            "name": hexagram_name,
            "number": hexagram_info["number"],
            "upper_trigram": upper_name,
            "lower_trigram": lower_name,
            "nature": hexagram_info["nature"],
            "attribute": hexagram_info["attribute"],
            "binary": binary_rep,
            "symbol": self._get_hexagram_symbol(binary_rep)
        }
    
    def _get_hexagram_symbol(self, binary_rep: List[int]) -> str:
        """生成卦象符号"""
        symbols = []
        for bit in reversed(binary_rep):  # 从上到下
            if bit == 1:
                symbols.append("⚊")  # 阳爻
            else:
                symbols.append("⚋")  # 阴爻
        return "\n".join(symbols)
    
    async def _interpret_hexagram(self, main_hexagram: Dict, changed_hexagram: Optional[Dict], question: str) -> Dict[str, Any]:
        """解读卦象"""
        
        # 模拟思考时间
        await asyncio.sleep(0.5)
        
        # 主卦解读
        main_interpretation = self._get_hexagram_interpretation(main_hexagram, question)
        
        # 变卦解读
        change_interpretation = None
        if changed_hexagram:
            change_interpretation = self._get_hexagram_interpretation(changed_hexagram, question)
        
        # 综合分析
        overall_analysis = self._analyze_hexagram_combination(main_hexagram, changed_hexagram, question)
        
        return {
            "main_hexagram_meaning": main_interpretation,
            "changed_hexagram_meaning": change_interpretation,
            "overall_analysis": overall_analysis,
            "trend": self._determine_trend(main_hexagram, changed_hexagram),
            "timing": self._determine_timing(main_hexagram)
        }
    
    def _get_hexagram_interpretation(self, hexagram: Dict, question: str) -> str:
        """获取单个卦象的解读"""
        
        name = hexagram["name"]
        attribute = hexagram["attribute"]
        nature = hexagram["nature"]
        
        # 基础解读模板
        base_interpretations = {
            "乾": f"乾卦象征{nature}，具有{attribute}的特质。在您询问的问题上，代表着强势、主动、创造的力量。事情有积极向上的发展趋势。",
            "坤": f"坤卦象征{nature}，具有{attribute}的特质。在您的问题上，建议以包容、顺应的态度处理，厚德载物将带来好运。",
            "屯": f"屯卦象征{nature}，具有{attribute}的特质。当前处于起步阶段，虽有困难但蕴含希望，需要耐心积累。",
            "蒙": f"蒙卦象征{nature}，具有{attribute}的特质。提示需要学习和指导，保持谦逊求教的心态。",
            "需": f"需卦象征{nature}，具有{attribute}的特质。时机尚未成熟，需要耐心等待，急进反而不利。",
            "泰": f"泰卦象征{nature}，具有{attribute}的特质。这是大吉之卦，天地交泰，万事顺遂，是行动的好时机。"
        }
        
        interpretation = base_interpretations.get(name, 
            f"{name}卦象征{nature}，具有{attribute}的特质。结合您的问题，需要根据卦象特点灵活应对。")
        
        # 根据问题类型调整解读
        if "工作" in question or "事业" in question:
            interpretation += " 在事业发展上，要把握时机，发挥自身优势。"
        elif "感情" in question or "婚姻" in question:
            interpretation += " 在感情方面，需要真诚相待，注重沟通理解。"
        elif "财运" in question or "投资" in question:
            interpretation += " 在财运方面，要谨慎理财，量力而行。"
        
        return interpretation
    
    def _analyze_hexagram_combination(self, main_hex: Dict, changed_hex: Optional[Dict], question: str) -> str:
        """分析主卦和变卦的组合含义"""
        
        if not changed_hex:
            return f"卦象稳定，{main_hex['name']}卦的影响将持续，建议按照{main_hex['attribute']}的方式行事。"
        
        main_name = main_hex["name"]
        changed_name = changed_hex["name"]
        
        if main_name == changed_name:
            return "卦象稳定，预示结果基本确定，按当前方向发展即可。"
        
        # 分析变化趋势
        trend_analysis = f"从{main_name}卦变为{changed_name}卦，"
        
        # 根据卦象性质分析趋势
        main_nature = main_hex.get("attribute", "")
        changed_nature = changed_hex.get("attribute", "")
        
        if "刚健" in main_nature and "柔顺" in changed_nature:
            trend_analysis += "显示需要从强势转为柔和，以退为进可能更有效。"
        elif "柔顺" in main_nature and "刚健" in changed_nature:
            trend_analysis += "预示需要更加主动积极，时机到了可以大胆行动。"
        else:
            trend_analysis += "表明情况将发生转变，需要适时调整策略应对。"
        
        return trend_analysis
    
    def _determine_trend(self, main_hex: Dict, changed_hex: Optional[Dict]) -> str:
        """确定发展趋势"""
        
        if not changed_hex:
            return "稳定"
        
        main_num = main_hex.get("number", 1)
        changed_num = changed_hex.get("number", 1)
        
        if changed_num > main_num:
            return "上升"
        elif changed_num < main_num:
            return "下降"
        else:
            return "平稳"
    
    def _determine_timing(self, hexagram: Dict) -> str:
        """确定时机"""
        
        attribute = hexagram.get("attribute", "")
        
        timing_map = {
            "刚健": "宜立即行动",
            "柔顺": "宜耐心等待",
            "积聚": "宜缓慢积累",
            "启蒙": "宜学习准备",
            "等待": "宜静观其变",
            "通泰": "宜把握机会"
        }
        
        return timing_map.get(attribute, "宜谨慎观察")
    
    async def _generate_advice(self, hexagram: Dict, interpretation: Dict, question: str) -> str:
        """生成建议指导"""
        
        name = hexagram["name"]
        attribute = hexagram["attribute"]
        trend = interpretation["trend"]
        timing = interpretation["timing"]
        
        # 基础建议模板
        advice_templates = {
            "乾": "秉承刚健精神，积极主动，但要避免过于强势。适合开创新事业，领导团队。",
            "坤": "保持柔顺包容，以德服人，耐心等待时机。适合协助他人，营造和谐。",
            "屯": "当前虽有困难，但要坚持初心，逐步积累，终将成功。",
            "泰": "正值大好时机，要积极把握，但也要居安思危，保持谦逊。"
        }
        
        base_advice = advice_templates.get(name, f"根据{name}卦的{attribute}特质，建议采取相应的策略。")
        
        # 根据趋势和时机调整建议
        trend_advice = {
            "上升": "趋势向好，可以适当激进一些。",
            "下降": "需要保守稳健，避免冒险。",
            "稳定": "保持现状即可，不必急于改变。"
        }
        
        timing_advice = {
            "宜立即行动": "时机成熟，不要犹豫。",
            "宜耐心等待": "暂时不动，等待更好时机。",
            "宜谨慎观察": "多观察情况变化，再作决定。"
        }
        
        comprehensive_advice = f"{base_advice} {trend_advice.get(trend, '')} {timing_advice.get(timing, '')}"
        
        # 根据问题类型添加具体建议
        if "工作" in question:
            comprehensive_advice += " 工作上要发挥专长，与同事协作。"
        elif "感情" in question:
            comprehensive_advice += " 感情要真诚投入，理解包容。"
        elif "健康" in question:
            comprehensive_advice += " 健康要注意调养，劳逸结合。"
        
        return comprehensive_advice
    
    def _calculate_score(self, hexagram: Dict, interpretation: Dict) -> int:
        """计算吉凶评分（0-100）"""
        
        # 基础分数
        base_scores = {
            "乾": 85, "坤": 80, "泰": 95, "否": 30,
            "屯": 60, "蒙": 55, "需": 70, "讼": 40,
            "师": 65, "比": 75, "小畜": 55, "履": 70,
            "同人": 80, "大有": 90, "谦": 85, "豫": 75
        }
        
        base_score = base_scores.get(hexagram["name"], 60)
        
        # 根据趋势调整
        trend = interpretation.get("trend", "稳定")
        if trend == "上升":
            base_score += 10
        elif trend == "下降":
            base_score -= 10
        
        # 确保分数在合理范围
        return max(10, min(100, base_score))
    
    def _get_lucky_elements(self, hexagram: Dict) -> List[str]:
        """获取有利因素"""
        
        upper_trigram = hexagram["upper_trigram"]
        lower_trigram = hexagram["lower_trigram"]
        
        elements = []
        
        # 添加五行元素
        if upper_trigram in self.trigrams:
            elements.append(self.trigrams[upper_trigram]["element"])
        if lower_trigram in self.trigrams:
            elements.append(self.trigrams[lower_trigram]["element"])
        
        # 添加方位
        if upper_trigram in self.trigrams:
            elements.append(self.trigrams[upper_trigram]["direction"])
        
        # 去重并添加通用有利因素
        unique_elements = list(set(elements))
        unique_elements.extend(["诚心", "耐心", "智慧"])
        
        return unique_elements[:5]  # 最多返回5个
    
    async def _get_fallback_divination(self, question: str) -> Dict[str, Any]:
        """获取备用占卜结果"""
        
        # 简单的备用卦象
        fallback_hexagram = {
            "name": "中孚",
            "number": 61,
            "upper_trigram": "巽",
            "lower_trigram": "兑",
            "nature": "风泽",
            "attribute": "诚信",
            "binary": [1, 1, 0, 0, 1, 1],
            "symbol": "⚊\n⚊\n⚋\n⚋\n⚊\n⚊"
        }
        
        return {
            "hexagram": {
                "main_hexagram": fallback_hexagram,
                "changed_hexagram": None,
                "yao_values": [7, 7, 8, 8, 7, 7],
                "changing_lines": [],
                "method": "auto"
            },
            "interpretation": {
                "main_hexagram_meaning": "中孚卦象征诚信，在您的问题上建议以诚待人，真心求索，必有好结果。",
                "changed_hexagram_meaning": None,
                "overall_analysis": "卦象稳定，诚信为本，坚持正道必获成功。",
                "trend": "稳定",
                "timing": "宜真诚行动"
            },
            "advice": "保持诚信的心态，以真诚面对问题。虽然过程可能需要耐心，但诚心所至，金石为开。建议您在处理相关事务时要实事求是，不弄虚作假。",
            "score": 75,
            "lucky_elements": ["风", "泽", "木", "金", "诚信"]
        } 