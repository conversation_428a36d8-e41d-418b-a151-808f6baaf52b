from fastapi import Request, HTTPException
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from typing import Dict, Optional
import time
import logging
from datetime import datetime, timedelta
from app.core.exceptions import RateLimitExceededError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RateLimiter:
    def __init__(self):
        self.requests: Dict[str, list] = {}  # IP -> [timestamp, timestamp, ...]
        self.FREE_TIER_LIMIT = 60  # 每小时请求数
        self.PREMIUM_TIER_LIMIT = 300  # 高级用户每小时请求数
        self.WINDOW_SIZE = 3600  # 1小时窗口（秒）

    def is_rate_limited(self, ip: str, is_premium: bool = False) -> bool:
        """检查是否超出速率限制"""
        now = time.time()
        if ip not in self.requests:
            self.requests[ip] = []
        
        # 清理过期的请求记录
        self.requests[ip] = [
            ts for ts in self.requests[ip]
            if now - ts < self.WINDOW_SIZE
        ]
        
        # 检查请求数量
        limit = self.PREMIUM_TIER_LIMIT if is_premium else self.FREE_TIER_LIMIT
        if len(self.requests[ip]) >= limit:
            return True
        
        # 记录新请求
        self.requests[ip].append(now)
        return False

    def get_reset_time(self, ip: str) -> str:
        """获取限制重置时间"""
        if ip not in self.requests or not self.requests[ip]:
            return datetime.now().isoformat()
        
        oldest_request = min(self.requests[ip])
        reset_time = datetime.fromtimestamp(oldest_request + self.WINDOW_SIZE)
        return reset_time.isoformat()

class RequestLogger:
    def __init__(self):
        self.logger = logging.getLogger("request_logger")
        self.logger.setLevel(logging.INFO)
        
        # 添加文件处理器
        handler = logging.FileHandler("logs/requests.log")
        handler.setFormatter(
            logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        )
        self.logger.addHandler(handler)

    async def log_request(
        self,
        request: Request,
        response: Optional[JSONResponse] = None,
        error: Optional[Exception] = None
    ):
        """记录请求和响应信息"""
        # 获取请求信息
        timestamp = datetime.now().isoformat()
        method = request.method
        url = str(request.url)
        client_ip = request.client.host
        headers = dict(request.headers)
        
        # 构建日志信息
        log_data = {
            "timestamp": timestamp,
            "method": method,
            "url": url,
            "client_ip": client_ip,
            "headers": headers
        }
        
        # 添加响应信息
        if response:
            log_data["status_code"] = response.status_code
            log_data["response_time"] = time.time() - request.state.start_time
        
        # 添加错误信息
        if error:
            log_data["error"] = str(error)
            log_data["error_type"] = type(error).__name__
        
        # 记录日志
        self.logger.info(log_data)

rate_limiter = RateLimiter()
request_logger = RequestLogger()

async def rate_limit_middleware(request: Request, call_next):
    """速率限制中间件"""
    # 记录请求开始时间
    request.state.start_time = time.time()
    
    try:
        # 获取客户端IP
        client_ip = request.client.host
        
        # 检查是否是高级用户
        is_premium = "premium" in request.headers.get("x-user-tier", "").lower()
        
        # 检查速率限制
        if rate_limiter.is_rate_limited(client_ip, is_premium):
            reset_time = rate_limiter.get_reset_time(client_ip)
            limit = rate_limiter.PREMIUM_TIER_LIMIT if is_premium else rate_limiter.FREE_TIER_LIMIT
            raise RateLimitExceededError(limit, reset_time)
        
        # 处理请求
        response = await call_next(request)
        
        # 记录成功的请求
        await request_logger.log_request(request, response)
        
        return response
        
    except Exception as e:
        # 记录失败的请求
        await request_logger.log_request(request, error=e)
        
        # 返回错误响应
        if isinstance(e, RateLimitExceededError):
            return JSONResponse(
                status_code=429,
                content={"detail": str(e)},
                headers=e.headers
            )
        
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        ) 