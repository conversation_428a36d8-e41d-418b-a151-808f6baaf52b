from typing import Dict, Any, List, Optional, Tuple
import json
from pathlib import Path
import numpy as np
from datetime import datetime
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jieba
from loguru import logger
from app.core.config import settings

class EnhancedKnowledgeBase:
    """
    增强型知识库管理器，支持智能学习和动态优化
    """
    
    def __init__(self, knowledge_path: Path = Path(settings.KNOWLEDGE_BASE_DIR)):
        self.knowledge_path = knowledge_path
        self.knowledge_path.mkdir(parents=True, exist_ok=True)
        self.knowledge_file = self.knowledge_path / "knowledge.json"
        self.vectorizer = TfidfVectorizer(tokenizer=self._tokenize_chinese)
        self.knowledge_data = self._load_knowledge()
        self.vectors = None
        self._update_vectors()

    def _tokenize_chinese(self, text: str) -> List[str]:
        """
        中文分词处理
        """
        return list(jieba.cut(text))

    def _load_knowledge(self) -> Dict[str, Any]:
        """
        加载知识库数据，如果不存在则创建新的
        """
        if self.knowledge_file.exists():
            with self.knowledge_file.open("r", encoding="utf-8") as f:
                return json.load(f)
        return {
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "version": "1.0",
                "total_entries": 0
            },
            "entries": [],
            "categories": {},
            "learning_history": []
        }

    def _save_knowledge(self) -> None:
        """
        保存知识库数据
        """
        self.knowledge_data["metadata"]["last_updated"] = datetime.now().isoformat()
        with self.knowledge_file.open("w", encoding="utf-8") as f:
            json.dump(self.knowledge_data, f, ensure_ascii=False, indent=2)

    def _update_vectors(self) -> None:
        """
        更新知识向量
        """
        if not self.knowledge_data["entries"]:
            self.vectors = None
            return
            
        texts = [entry["content"] for entry in self.knowledge_data["entries"]]
        self.vectors = self.vectorizer.fit_transform(texts)

    def add_entry(self, content: str, category: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        添加新知识条目
        """
        if metadata is None:
            metadata = {}
            
        entry = {
            "id": len(self.knowledge_data["entries"]) + 1,
            "content": content,
            "category": category,
            "created_at": datetime.now().isoformat(),
            "last_used": None,
            "use_count": 0,
            "feedback_score": 0.0,
            "metadata": metadata
        }
        
        self.knowledge_data["entries"].append(entry)
        
        # 更新类别统计
        if category not in self.knowledge_data["categories"]:
            self.knowledge_data["categories"][category] = {
                "count": 0,
                "last_updated": None,
                "average_feedback": 0.0
            }
        self.knowledge_data["categories"][category]["count"] += 1
        self.knowledge_data["categories"][category]["last_updated"] = datetime.now().isoformat()
        
        self.knowledge_data["metadata"]["total_entries"] += 1
        self._update_vectors()
        self._save_knowledge()

    def find_similar(self, query: str, top_k: int = 5, threshold: float = 0.3) -> List[Dict[str, Any]]:
        """
        查找相似的知识条目
        """
        if not self.vectors:
            return []
            
        query_vector = self.vectorizer.transform([query])
        similarities = cosine_similarity(query_vector, self.vectors).flatten()
        
        # 获取相似度排序后的索引
        similar_indices = similarities.argsort()[::-1]
        
        results = []
        for idx in similar_indices:
            if similarities[idx] < threshold:
                break
            entry = self.knowledge_data["entries"][idx]
            entry["similarity_score"] = float(similarities[idx])
            results.append(entry)
            if len(results) >= top_k:
                break
                
        return results

    def update_feedback(self, entry_id: int, is_helpful: bool) -> None:
        """
        更新知识条目的反馈
        """
        for entry in self.knowledge_data["entries"]:
            if entry["id"] == entry_id:
                entry["use_count"] += 1
                entry["last_used"] = datetime.now().isoformat()
                
                # 更新反馈分数
                feedback_value = 1.0 if is_helpful else -0.5
                if entry["use_count"] == 1:
                    entry["feedback_score"] = feedback_value
                else:
                    entry["feedback_score"] = (entry["feedback_score"] * (entry["use_count"] - 1) + feedback_value) / entry["use_count"]
                
                # 更新类别统计
                category = entry["category"]
                category_data = self.knowledge_data["categories"][category]
                category_data["average_feedback"] = (
                    category_data["average_feedback"] * (category_data["count"] - 1) + feedback_value
                ) / category_data["count"]
                
                # 记录学习历史
                self.knowledge_data["learning_history"].append({
                    "entry_id": entry_id,
                    "timestamp": datetime.now().isoformat(),
                    "is_helpful": is_helpful,
                    "feedback_value": feedback_value
                })
                
                self._save_knowledge()
                break

    def get_smart_response(self, query: str, context_size: int = 3) -> Tuple[str, List[Dict[str, Any]]]:
        """
        获取智能响应
        """
        similar_entries = self.find_similar(query)
        if not similar_entries:
            return "抱歉，我没有找到相关的知识。", []
            
        # 根据相似度和反馈分数排序
        for entry in similar_entries:
            entry["ranking_score"] = entry["similarity_score"] * (1 + max(0, entry["feedback_score"]))
            
        similar_entries.sort(key=lambda x: x["ranking_score"], reverse=True)
        
        # 获取最相关的内容
        top_entries = similar_entries[:context_size]
        
        # 构建响应
        response_parts = []
        for entry in top_entries:
            response_parts.append(entry["content"])
            
        # 更新使用统计
        self.update_feedback(top_entries[0]["id"], True)
        
        return "\n".join(response_parts), top_entries

    def optimize_knowledge_base(self) -> None:
        """
        优化知识库
        """
        # 删除低质量的条目
        self.knowledge_data["entries"] = [
            entry for entry in self.knowledge_data["entries"]
            if entry["feedback_score"] >= -0.5 or entry["use_count"] < 5
        ]
        
        # 重新计算类别统计
        for category in self.knowledge_data["categories"].values():
            category["count"] = len([
                entry for entry in self.knowledge_data["entries"]
                if entry["category"] == category
            ])
            
        self._update_vectors()
        self._save_knowledge()

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        """
        return {
            "total_entries": self.knowledge_data["metadata"]["total_entries"],
            "categories": self.knowledge_data["categories"],
            "last_updated": self.knowledge_data["metadata"]["last_updated"],
            "learning_history_size": len(self.knowledge_data["learning_history"]),
            "average_feedback": np.mean([
                entry["feedback_score"] 
                for entry in self.knowledge_data["entries"]
                if entry["use_count"] > 0
            ]) if self.knowledge_data["entries"] else 0.0
        }

# 创建全局实例
enhanced_kb = EnhancedKnowledgeBase() 