from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
from typing import List, Optional, Dict, Any
from app.core.config import settings
from app.core.monitoring import monitor_model_inference, logger
from app.core.cache import cache
import gc
import os
import time
from tqdm import tqdm
import traceback
import asyncio

class ModelLoadError(Exception):
    """自定义模型加载错误类"""
    pass

class ModelHandler:
    def __init__(self):
        self.device = torch.device(settings.MODEL_DEVICE)
        self.model = None
        self.tokenizer = None
        self.max_length = settings.MAX_LENGTH
        self.is_initialized = False
        self.load_progress = 0
        self.error_details = None

    def _check_system_requirements(self) -> Dict[str, Any]:
        """检查系统要求"""
        requirements = {
            "cuda_available": torch.cuda.is_available(),
            "gpu_memory": None,
            "cuda_version": torch.version.cuda if torch.cuda.is_available() else None,
            "device_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
        }
        
        if torch.cuda.is_available():
            requirements["gpu_memory"] = {
                "total": torch.cuda.get_device_properties(0).total_memory / 1024**3,  # GB
                "free": torch.cuda.memory_reserved(0) / 1024**3  # GB
            }
        
        return requirements

    def _prepare_model_environment(self):
        """准备模型环境"""
        try:
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                gc.collect()
            
            # 设置环境变量
            os.environ["TOKENIZERS_PARALLELISM"] = "true"
            
            # 检查模型文件是否存在
            if not os.path.exists(settings.MODEL_PATH):
                raise ModelLoadError(f"Model path not found: {settings.MODEL_PATH}")
                
        except Exception as e:
            error_msg = f"Error preparing environment: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            raise ModelLoadError(error_msg)

    async def progressive_load(self):
        """渐进式加载模型"""
        try:
            self.load_progress = 0
            logger.info("Starting progressive model loading...")
            
            # 1. 检查系统要求
            requirements = self._check_system_requirements()
            logger.info(f"System requirements checked: {requirements}")
            self.load_progress = 10
            
            # 2. 准备环境
            self._prepare_model_environment()
            self.load_progress = 20
            
            # 3. 加载分词器
            logger.info("Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(settings.MODEL_PATH)
            self.load_progress = 40
            
            # 4. 渐进式加载模型
            logger.info("Loading model progressively...")
            self.model = AutoModelForCausalLM.from_pretrained(
                settings.MODEL_PATH,
                device_map="auto",
                torch_dtype=torch.float16,
                low_cpu_mem_usage=True,
                offload_folder="offload"
            )
            self.load_progress = 80
            
            # 5. 模型预热
            await self.warmup()
            self.load_progress = 100
            
            self.is_initialized = True
            logger.info("Model loaded successfully")
            
        except Exception as e:
            error_msg = f"Error in progressive loading: {str(e)}\n{traceback.format_exc()}"
            self.error_details = error_msg
            logger.error(error_msg)
            raise ModelLoadError(error_msg)

    async def warmup(self, num_warmup_steps: int = 3):
        """模型预热"""
        try:
            logger.info("Starting model warmup...")
            warmup_inputs = [
                "你好",
                "今天天气真不错",
                "请介绍一下你自己"
            ]
            
            for i in tqdm(range(num_warmup_steps), desc="Warming up"):
                input_text = warmup_inputs[i % len(warmup_inputs)]
                inputs = self.tokenizer(input_text, return_tensors="pt").to(self.device)
                
                # 使用较小的max_length进行预热
                with torch.no_grad():
                    self.model.generate(
                        **inputs,
                        max_length=50,
                        temperature=0.7,
                        num_return_sequences=1,
                        pad_token_id=self.tokenizer.eos_token_id
                    )
                
                # 清理缓存
                torch.cuda.empty_cache()
                time.sleep(1)  # 短暂暂停，避免GPU过热
                
            logger.info("Model warmup completed successfully")
            
        except Exception as e:
            error_msg = f"Error during warmup: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            raise ModelLoadError(error_msg)

    def load_model(self):
        """加载模型和分词器"""
        try:
            logger.info(f"Loading model from {settings.MODEL_PATH}")
            asyncio.run(self.progressive_load())
        except Exception as e:
            error_msg = f"Error loading model: {str(e)}\n{traceback.format_exc()}"
            self.error_details = error_msg
            logger.error(error_msg)
            raise ModelLoadError(error_msg)

    def get_load_status(self) -> Dict[str, Any]:
        """获取模型加载状态"""
        return {
            "is_initialized": self.is_initialized,
            "load_progress": self.load_progress,
            "error_details": self.error_details,
            "device": str(self.device),
            "model_path": settings.MODEL_PATH
        }

    @monitor_model_inference()
    @cache(expire=3600)
    async def generate(
        self,
        prompt: str,
        max_length: Optional[int] = None,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
    ) -> str:
        """生成回复"""
        if not self.is_initialized:
            raise ModelLoadError("Model is not initialized yet")
            
        try:
            inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
            max_length = max_length or self.max_length

            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_length=max_length,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k,
                    pad_token_id=self.tokenizer.eos_token_id,
                )

            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            return response

        except Exception as e:
            error_msg = f"Error in generate: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            raise

    @monitor_model_inference()
    async def batch_generate(
        self,
        prompts: List[str],
        max_length: Optional[int] = None,
        temperature: float = 0.7,
        top_p: float = 0.9,
        top_k: int = 50,
    ) -> List[str]:
        """批量生成回复"""
        if not self.is_initialized:
            raise ModelLoadError("Model is not initialized yet")
            
        try:
            inputs = self.tokenizer(
                prompts,
                padding=True,
                return_tensors="pt"
            ).to(self.device)
            
            max_length = max_length or self.max_length

            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_length=max_length,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k,
                    pad_token_id=self.tokenizer.eos_token_id,
                )

            responses = [
                self.tokenizer.decode(output, skip_special_tokens=True)
                for output in outputs
            ]
            return responses

        except Exception as e:
            error_msg = f"Error in batch_generate: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            raise

model_handler = ModelHandler() 