import os
import logging
from typing import Optional, Dict, Any
from huggingface_hub import snapshot_download
from tqdm import tqdm
import torch
from app.core.config import settings

logger = logging.getLogger(__name__)

class ModelDownloader:
    def __init__(self):
        self.download_progress = 0
        self.error_details = None
        self.cache_dir = os.path.join(os.path.dirname(settings.MODEL_PATH), "model_cache")
        os.makedirs(self.cache_dir, exist_ok=True)

    async def download_model(
        self,
        model_name: str = "THUDM/chatglm3-6b",
        revision: str = "main",
        local_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        下载模型文件
        
        Args:
            model_name: Hugging Face 模型名称
            revision: 模型版本
            local_dir: 本地保存目录
        """
        try:
            self.download_progress = 0
            logger.info(f"Starting download of model {model_name}")
            
            # 设置下载目录
            local_dir = local_dir or settings.MODEL_PATH
            os.makedirs(local_dir, exist_ok=True)
            
            # 检查磁盘空间
            self._check_disk_space(local_dir)
            self.download_progress = 10
            
            # 下载模型
            logger.info(f"Downloading model to {local_dir}")
            snapshot_download(
                repo_id=model_name,
                revision=revision,
                local_dir=local_dir,
                local_dir_use_symlinks=False,
                cache_dir=self.cache_dir
            )
            self.download_progress = 90
            
            # 验证下载
            self._verify_model_files(local_dir)
            self.download_progress = 100
            
            logger.info("Model downloaded successfully")
            return {
                "status": "success",
                "model_path": local_dir,
                "model_name": model_name,
                "revision": revision
            }
            
        except Exception as e:
            error_msg = f"Error downloading model: {str(e)}"
            self.error_details = error_msg
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "model_name": model_name
            }

    def _check_disk_space(self, path: str):
        """检查磁盘空间"""
        try:
            total, used, free = os.statvfs(path)[0:3]
            free_gb = (free * total) / (1024 ** 3)
            if free_gb < 20:  # 需要至少20GB空间
                raise Exception(f"Insufficient disk space. Only {free_gb:.2f}GB available")
        except Exception as e:
            logger.error(f"Error checking disk space: {str(e)}")
            raise

    def _verify_model_files(self, model_path: str):
        """验证模型文件完整性"""
        required_files = [
            "config.json",
            "pytorch_model.bin",
            "tokenizer_config.json",
            "tokenizer.json"
        ]
        
        for file in required_files:
            file_path = os.path.join(model_path, file)
            if not os.path.exists(file_path):
                raise Exception(f"Missing required model file: {file}")

    def get_download_status(self) -> Dict[str, Any]:
        """获取下载状态"""
        return {
            "progress": self.download_progress,
            "error_details": self.error_details,
            "cache_dir": self.cache_dir
        }

    @staticmethod
    def list_available_models() -> Dict[str, Any]:
        """列出可用的模型"""
        return {
            "chatglm": {
                "chatglm-6b": "THUDM/chatglm-6b",
                "chatglm2-6b": "THUDM/chatglm2-6b",
                "chatglm3-6b": "THUDM/chatglm3-6b",
            },
            "other": {
                "baichuan": "baichuan-inc/Baichuan-13B-Chat",
                "qwen": "Qwen/Qwen-7B-Chat",
            }
        }

model_downloader = ModelDownloader() 