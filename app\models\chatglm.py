from typing import List, Optional, Union, Dict, Any
from haystack.nodes import BaseGenerator
from transformers import AutoTokenizer, AutoModel
import torch
from app.core.config import settings

class ChatGLMGenerator(BaseGenerator):
    """
    使用 ChatGLM-6B 模型实现的 Haystack Generator
    """
    
    def __init__(
        self,
        model_name_or_path: str = settings.MODEL_PATH,
        device: str = settings.DEVICE,
        load_in_8bit: bool = settings.LOAD_IN_8BIT,
        use_half: bool = settings.USE_HALF_PRECISION,
        max_length: int = 2048,
        top_k: int = 1,
        top_p: float = 0.7,
        temperature: float = 0.95,
    ):
        super().__init__()
        
        self.model_name_or_path = model_name_or_path
        self.device = device
        self.max_length = max_length
        self.top_k = top_k
        self.top_p = top_p
        self.temperature = temperature
        
        # 初始化分词器和模型
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_name_or_path, trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            model_name_or_path, trust_remote_code=True
        )
        
        if device == "cuda":
            if load_in_8bit:
                self.model = self.model.quantize(8)
            if use_half:
                self.model = self.model.half()
            self.model = self.model.cuda()
        
        self.model = self.model.eval()
    
    def generate(
        self,
        query: str,
        documents: List[dict],
        top_k: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        使用检索到的文档作为上下文生成答案
        """
        if not documents:
            return {"answers": [{"answer": "抱歉，我没有找到相关的信息。", "score": 0.0}]}
        
        # 构建提示模板
        context = "\n".join([doc.get("content", "") for doc in documents])
        prompt = f"基于以下信息回答问题：\n\n{context}\n\n问题：{query}\n\n回答："
        
        # 生成回答
        response, history = self.model.chat(
            self.tokenizer,
            prompt,
            history=[],
            max_length=self.max_length,
            top_p=self.top_p,
            temperature=self.temperature,
        )
        
        return {
            "answers": [
                {
                    "answer": response,
                    "score": 1.0,  # 简化的评分
                    "context": context,
                    "document_ids": [doc.get("id", "") for doc in documents],
                }
            ]
        }
    
    def predict(self, query: str, documents: List[dict], top_k: Optional[int] = None) -> Dict[str, Any]:
        """
        与 generate 方法相同，用于兼容性
        """
        return self.generate(query, documents, top_k) 